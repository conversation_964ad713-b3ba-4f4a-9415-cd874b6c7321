from django.contrib import admin
from .models import (
    AcademicSyllabus, Assignment, StudentAssignment, StudyMaterial,
    Curriculum, SyllabusUnit, LearningMaterial, TermPlan,
    SyllabusProgress, AssignmentCategory, SyllabusReport,
    AcademicYear, Term
)

@admin.register(AcademicSyllabus)
class AcademicSyllabusAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'description', 'grade', 'subject', 'teacher', 'file_name', 'file_type', 'file_size', 'year', 'term', 'upload_date', 'status')
    search_fields = ('title', 'grade__name', 'subject__name', 'teacher__full_name')
    list_filter = ('grade', 'subject', 'teacher', 'status', 'year', 'term')
    ordering = ('-upload_date', 'title')

@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'description', 'grade', 'subject', 'teacher', 'file_name', 'file_type', 'file_size', 'assign_date', 'due_date', 'total_marks', 'status', 'year', 'term')
    search_fields = ('title', 'grade__name', 'subject__name', 'teacher__full_name')
    list_filter = ('grade', 'subject', 'teacher', 'status', 'year', 'term')
    ordering = ('-assign_date', 'title')

@admin.register(StudentAssignment)
class StudentAssignmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'assignment', 'student', 'submission_date', 'file_name', 'file_type', 'file_size', 'marks', 'comments', 'status', 'graded_by', 'graded_date')
    search_fields = ('assignment__title', 'student__full_name', 'status')
    list_filter = ('assignment', 'status', 'graded_by')
    ordering = ('-submission_date', 'assignment')

@admin.register(StudyMaterial)
class StudyMaterialAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'description', 'grade', 'subject', 'teacher', 'file_name', 'file_type', 'file_size', 'material_type', 'url', 'upload_date', 'year', 'term', 'status')
    search_fields = ('title', 'grade__name', 'subject__name', 'teacher__full_name')
    list_filter = ('grade', 'subject', 'teacher', 'material_type', 'status', 'year', 'term')
    ordering = ('-upload_date', 'title')

# Enhanced Models Admin

@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    list_display = ('id', 'year', 'start_date', 'end_date', 'is_current', 'created_at')
    search_fields = ('year',)
    list_filter = ('is_current', 'created_at')
    ordering = ('-year',)

@admin.register(Term)
class TermAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'academic_year', 'start_date', 'end_date', 'is_current', 'created_at')
    search_fields = ('name', 'academic_year__year')
    list_filter = ('name', 'academic_year', 'is_current', 'created_at')
    ordering = ('-academic_year__year', 'name')

@admin.register(Curriculum)
class CurriculumAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'curriculum_type', 'academic_year', 'total_hours', 'status', 'created_at')
    search_fields = ('name', 'code', 'description')
    list_filter = ('curriculum_type', 'status', 'academic_year', 'created_at')
    filter_horizontal = ('grade_levels', 'subjects')
    ordering = ('-created_at', 'name')

@admin.register(SyllabusUnit)
class SyllabusUnitAdmin(admin.ModelAdmin):
    list_display = ('id', 'syllabus', 'unit_number', 'title', 'unit_type', 'duration_hours', 'is_mandatory', 'order')
    search_fields = ('title', 'syllabus__title', 'description')
    list_filter = ('unit_type', 'is_mandatory', 'syllabus__subject', 'syllabus__grade')
    ordering = ('syllabus', 'order', 'unit_number')

@admin.register(LearningMaterial)
class LearningMaterialAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'syllabus', 'material_type', 'material_format', 'access_level', 'is_mandatory', 'view_count', 'download_count')
    search_fields = ('title', 'description', 'syllabus__title')
    list_filter = ('material_type', 'material_format', 'access_level', 'is_mandatory', 'syllabus__subject')
    ordering = ('-created_at', 'title')

@admin.register(TermPlan)
class TermPlanAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'syllabus', 'academic_year', 'term', 'start_date', 'end_date', 'total_weeks', 'status')
    search_fields = ('title', 'syllabus__title', 'objectives')
    list_filter = ('status', 'academic_year', 'term', 'syllabus__subject', 'syllabus__grade')
    ordering = ('-created_at', 'title')

@admin.register(SyllabusProgress)
class SyllabusProgressAdmin(admin.ModelAdmin):
    list_display = ('id', 'syllabus_unit', 'teacher', 'grade', 'completion_percentage', 'status', 'planned_start_date', 'actual_start_date')
    search_fields = ('syllabus_unit__title', 'teacher__full_name', 'grade__name')
    list_filter = ('status', 'grade', 'teacher', 'syllabus_unit__syllabus__subject')
    ordering = ('-last_updated', 'syllabus_unit')

@admin.register(AssignmentCategory)
class AssignmentCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'category_type', 'default_weight', 'color_code', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('category_type', 'is_active', 'created_at')
    ordering = ('name',)

@admin.register(SyllabusReport)
class SyllabusReportAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'report_type', 'report_format', 'academic_year', 'term', 'generated_by', 'generated_at')
    search_fields = ('title', 'generated_by__username')
    list_filter = ('report_type', 'report_format', 'academic_year', 'term', 'generated_at')
    ordering = ('-generated_at', 'title')
