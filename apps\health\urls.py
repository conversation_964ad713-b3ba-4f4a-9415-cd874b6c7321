from django.urls import path
from . import views

app_name = 'health'

urlpatterns = [
    # Dashboard
    path('', views.health_dashboard, name='dashboard'),
    
    # Health Records Management
    path('records/', views.health_records_list, name='health_records_list'),
    path('records/<int:record_id>/', views.health_record_detail, name='health_record_detail'),
    path('records/add/', views.add_health_record, name='add_health_record'),
    path('records/<int:record_id>/edit/', views.edit_health_record, name='edit_health_record'),
    
    # Health Visits Management
    path('visits/', views.health_visits_list, name='health_visits_list'),
    path('visits/<int:visit_id>/', views.health_visit_detail, name='health_visit_detail'),
    path('visits/add/', views.add_health_visit, name='add_health_visit'),

    # Reports
    path('reports/', views.health_reports, name='health_reports'),
]
