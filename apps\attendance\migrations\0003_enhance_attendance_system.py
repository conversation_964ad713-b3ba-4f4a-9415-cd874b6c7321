# Generated by Django 5.2.1 on 2025-06-10 08:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0002_initial"),
        ("attendance", "0002_attendanceperiod_studentattendance_period_and_more"),
        ("students", "0002_guardian_alter_student_options_and_more"),
        ("teachers", "0002_enhance_teacher_models"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AttendanceAlert",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("consecutive_absence", "Consecutive Absence"),
                            ("low_attendance", "Low Attendance Rate"),
                            ("frequent_lateness", "Frequent Lateness"),
                            ("pattern_change", "Attendance Pattern Change"),
                            ("risk_identification", "At-Risk Student"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("dismissed", "Dismissed"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                (
                    "threshold_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "current_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("triggered_date", models.DateTimeField(auto_now_add=True)),
                ("acknowledged_date", models.DateTimeField(blank=True, null=True)),
                ("resolved_date", models.DateTimeField(blank=True, null=True)),
                ("action_taken", models.TextField(blank=True, null=True)),
                ("follow_up_required", models.BooleanField(default=False)),
                ("follow_up_date", models.DateField(blank=True, null=True)),
                (
                    "acknowledged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="acknowledged_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendance_alerts",
                        to="students.student",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendance_alerts",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Attendance Alert",
                "verbose_name_plural": "Attendance Alerts",
                "ordering": ["-triggered_date"],
            },
        ),
        migrations.CreateModel(
            name="AttendanceReport",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("daily_summary", "Daily Summary"),
                            ("weekly_summary", "Weekly Summary"),
                            ("monthly_summary", "Monthly Summary"),
                            ("term_summary", "Term Summary"),
                            ("student_individual", "Individual Student Report"),
                            ("class_summary", "Class Summary"),
                            ("grade_summary", "Grade Summary"),
                            ("teacher_summary", "Teacher Summary"),
                            ("attendance_trends", "Attendance Trends"),
                            ("absence_analysis", "Absence Analysis"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("excel", "Excel"),
                            ("csv", "CSV"),
                            ("html", "HTML"),
                        ],
                        default="pdf",
                        max_length=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="generating",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "file_path",
                    models.FileField(
                        blank=True, null=True, upload_to="attendance/reports/"
                    ),
                ),
                ("total_students", models.PositiveIntegerField(default=0)),
                ("total_days", models.PositiveIntegerField(default=0)),
                (
                    "average_attendance_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                ("generated_at", models.DateTimeField(auto_now_add=True)),
                ("accessed_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(blank=True, null=True)),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Attendance Report",
                "verbose_name_plural": "Attendance Reports",
                "ordering": ["-generated_at"],
            },
        ),
        migrations.CreateModel(
            name="AttendanceStatistics",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "entity_type",
                    models.CharField(
                        choices=[
                            ("student", "Student"),
                            ("teacher", "Teacher"),
                            ("class", "Class"),
                            ("grade", "Grade"),
                            ("school", "School"),
                        ],
                        max_length=20,
                    ),
                ),
                ("entity_id", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("term", "Term"),
                            ("annual", "Annual"),
                        ],
                        max_length=20,
                    ),
                ),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                ("total_days", models.PositiveIntegerField(default=0)),
                ("present_days", models.PositiveIntegerField(default=0)),
                ("absent_days", models.PositiveIntegerField(default=0)),
                ("late_days", models.PositiveIntegerField(default=0)),
                ("excused_days", models.PositiveIntegerField(default=0)),
                (
                    "attendance_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                (
                    "absence_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                (
                    "punctuality_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                ("calculated_at", models.DateTimeField(auto_now=True)),
                ("academic_year", models.PositiveIntegerField(default=2024)),
                ("term", models.PositiveIntegerField(default=1)),
            ],
            options={
                "verbose_name": "Attendance Statistics",
                "verbose_name_plural": "Attendance Statistics",
                "ordering": ["-period_start"],
                "unique_together": {
                    (
                        "entity_type",
                        "entity_id",
                        "period_type",
                        "period_start",
                        "period_end",
                    )
                },
            },
        ),
        migrations.CreateModel(
            name="BulkAttendanceSession",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("class_marking", "Class Marking"),
                            ("csv_import", "CSV Import"),
                            ("api_import", "API Import"),
                            ("manual_bulk", "Manual Bulk Entry"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="in_progress",
                        max_length=20,
                    ),
                ),
                ("date", models.DateField()),
                ("section", models.CharField(blank=True, max_length=10, null=True)),
                ("total_records", models.PositiveIntegerField(default=0)),
                ("processed_records", models.PositiveIntegerField(default=0)),
                ("successful_records", models.PositiveIntegerField(default=0)),
                ("failed_records", models.PositiveIntegerField(default=0)),
                (
                    "uploaded_file",
                    models.FileField(
                        blank=True, null=True, upload_to="attendance/bulk_imports/"
                    ),
                ),
                ("error_log", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "period",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="attendance.attendanceperiod",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bulk Attendance Session",
                "verbose_name_plural": "Bulk Attendance Sessions",
                "ordering": ["-created_at"],
            },
        ),
    ]
