# Generated by Django 5.2.1 on 2025-06-10 09:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Dashboard",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "dashboard_type",
                    models.CharField(
                        choices=[
                            ("admin", "Administrative Dashboard"),
                            ("teacher", "Teacher Dashboard"),
                            ("student", "Student Dashboard"),
                            ("parent", "Parent Dashboard"),
                            ("financial", "Financial Dashboard"),
                            ("academic", "Academic Dashboard"),
                            ("custom", "Custom Dashboard"),
                        ],
                        max_length=20,
                    ),
                ),
                ("layout_config", models.J<PERSON><PERSON>ield(default=dict)),
                ("refresh_interval", models.PositiveIntegerField(default=300)),
                ("is_default", models.BooleanField(default=False)),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "allowed_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="accessible_dashboards",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_dashboards",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Dashboard",
                "verbose_name_plural": "Dashboards",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DashboardWidget",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                (
                    "widget_type",
                    models.CharField(
                        choices=[
                            ("chart", "Chart"),
                            ("table", "Table"),
                            ("metric", "Metric"),
                            ("progress", "Progress Bar"),
                            ("list", "List"),
                            ("calendar", "Calendar"),
                            ("map", "Map"),
                            ("iframe", "Embedded Content"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "chart_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("line", "Line Chart"),
                            ("bar", "Bar Chart"),
                            ("pie", "Pie Chart"),
                            ("doughnut", "Doughnut Chart"),
                            ("area", "Area Chart"),
                            ("scatter", "Scatter Plot"),
                            ("gauge", "Gauge Chart"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                ("position_x", models.PositiveIntegerField(default=0)),
                ("position_y", models.PositiveIntegerField(default=0)),
                ("width", models.PositiveIntegerField(default=4)),
                ("height", models.PositiveIntegerField(default=3)),
                ("data_source", models.CharField(max_length=255)),
                ("data_config", models.JSONField(default=dict)),
                ("chart_config", models.JSONField(default=dict)),
                ("auto_refresh", models.BooleanField(default=True)),
                ("refresh_interval", models.PositiveIntegerField(default=300)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("order", models.PositiveIntegerField(default=0)),
                (
                    "dashboard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="widgets",
                        to="reporting.dashboard",
                    ),
                ),
            ],
            options={
                "verbose_name": "Dashboard Widget",
                "verbose_name_plural": "Dashboard Widgets",
                "ordering": ["dashboard", "order"],
            },
        ),
        migrations.CreateModel(
            name="DataVisualization",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "visualization_type",
                    models.CharField(
                        choices=[
                            ("chart", "Chart"),
                            ("graph", "Graph"),
                            ("heatmap", "Heatmap"),
                            ("treemap", "Treemap"),
                            ("sankey", "Sankey Diagram"),
                            ("wordcloud", "Word Cloud"),
                            ("timeline", "Timeline"),
                            ("network", "Network Diagram"),
                        ],
                        max_length=20,
                    ),
                ),
                ("data_query", models.TextField()),
                ("config", models.JSONField(default=dict)),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="visualizations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Data Visualization",
                "verbose_name_plural": "Data Visualizations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReportTemplate",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("student_performance", "Student Performance"),
                            ("attendance_summary", "Attendance Summary"),
                            ("financial_overview", "Financial Overview"),
                            ("teacher_workload", "Teacher Workload"),
                            ("class_analytics", "Class Analytics"),
                            ("exam_results", "Exam Results"),
                            ("fee_collection", "Fee Collection"),
                            ("library_usage", "Library Usage"),
                            ("transport_utilization", "Transport Utilization"),
                            ("hostel_occupancy", "Hostel Occupancy"),
                            ("custom", "Custom Report"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "output_format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("excel", "Excel"),
                            ("csv", "CSV"),
                            ("html", "HTML"),
                            ("json", "JSON"),
                        ],
                        default="pdf",
                        max_length=10,
                    ),
                ),
                ("template_config", models.JSONField(default=dict)),
                ("sql_query", models.TextField(blank=True, null=True)),
                ("chart_config", models.JSONField(default=dict)),
                ("is_automated", models.BooleanField(default=False)),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("manual", "Manual"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("annually", "Annually"),
                        ],
                        default="manual",
                        max_length=20,
                    ),
                ),
                ("next_run", models.DateTimeField(blank=True, null=True)),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "allowed_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="accessible_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Report Template",
                "verbose_name_plural": "Report Templates",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReportSchedule",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("cron_expression", models.CharField(max_length=100)),
                ("timezone", models.CharField(default="UTC", max_length=50)),
                ("email_recipients", models.JSONField(default=list)),
                ("is_active", models.BooleanField(default=True)),
                ("last_run", models.DateTimeField(blank=True, null=True)),
                ("next_run", models.DateTimeField(blank=True, null=True)),
                ("run_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_schedules",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_recipients",
                    models.ManyToManyField(
                        blank=True,
                        related_name="report_subscriptions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedules",
                        to="reporting.reporttemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Report Schedule",
                "verbose_name_plural": "Report Schedules",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="GeneratedReport",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("parameters", models.JSONField(default=dict)),
                (
                    "file_path",
                    models.FileField(blank=True, null=True, upload_to="reports/"),
                ),
                ("file_size", models.PositiveIntegerField(blank=True, null=True)),
                ("total_records", models.PositiveIntegerField(default=0)),
                ("generation_time", models.DurationField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True, null=True)),
                ("generated_at", models.DateTimeField(auto_now_add=True)),
                ("accessed_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reporting_generated_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_reports",
                        to="reporting.reporttemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Generated Report",
                "verbose_name_plural": "Generated Reports",
                "ordering": ["-generated_at"],
            },
        ),
    ]
