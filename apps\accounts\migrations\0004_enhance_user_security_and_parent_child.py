# Generated by Django 5.2.1 on 2025-06-10 09:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0003_user_is_staff_alter_user_is_superuser"),
        ("students", "0002_guardian_alter_student_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="account_locked_until",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="failed_login_attempts",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="user",
            name="last_failed_login",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="lockout_reason",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name="LoginAttempt",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("email", models.EmailField(max_length=254)),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                (
                    "attempt_type",
                    models.CharField(
                        choices=[
                            ("success", "Successful Login"),
                            ("failed", "Failed Login"),
                            ("locked", "Account Locked"),
                        ],
                        max_length=20,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "failure_reason",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="login_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Login Attempt",
                "verbose_name_plural": "Login Attempts",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="ParentChildRelationship",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "relationship_type",
                    models.CharField(
                        choices=[
                            ("father", "Father"),
                            ("mother", "Mother"),
                            ("guardian", "Guardian"),
                            ("stepfather", "Step Father"),
                            ("stepmother", "Step Mother"),
                            ("grandfather", "Grandfather"),
                            ("grandmother", "Grandmother"),
                            ("uncle", "Uncle"),
                            ("aunt", "Aunt"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("is_primary_contact", models.BooleanField(default=False)),
                ("is_emergency_contact", models.BooleanField(default=False)),
                ("can_pick_up", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "parent",
                    models.ForeignKey(
                        limit_choices_to={"type": "parent"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parents",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "verbose_name": "Parent-Child Relationship",
                "verbose_name_plural": "Parent-Child Relationships",
                "ordering": ["student", "-is_primary_contact"],
                "unique_together": {("parent", "student")},
            },
        ),
    ]
