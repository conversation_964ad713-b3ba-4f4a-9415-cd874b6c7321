from django import forms
from django.core.exceptions import ValidationError
from .models import Grade, Department, Subject, SubjectRouting, ClassRoom
from apps.core.models import SchoolInfo, SystemSettings
from apps.teachers.models import Teacher


class SchoolInfoForm(forms.ModelForm):
    """Form for managing school information and academic year settings"""
    
    class Meta:
        model = SchoolInfo
        fields = [
            'name', 'motto', 'logo', 'school_type', 'curriculum_type',
            'academic_year_start', 'academic_year_end', 'address', 'city',
            'county', 'postal_code', 'phone', 'alt_phone', 'email',
            'website', 'working_hours', 'principal_name', 'principal_message',
            'theme_color', 'date_established'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'School Name'}),
            'motto': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'School Motto'}),
            'logo': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'school_type': forms.Select(attrs={'class': 'form-control'}),
            'curriculum_type': forms.Select(attrs={'class': 'form-control'}),
            'academic_year_start': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'academic_year_end': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'county': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'alt_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'website': forms.URLInput(attrs={'class': 'form-control'}),
            'working_hours': forms.TextInput(attrs={'class': 'form-control'}),
            'principal_name': forms.TextInput(attrs={'class': 'form-control'}),
            'principal_message': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'theme_color': forms.TextInput(attrs={'class': 'form-control', 'type': 'color'}),
            'date_established': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('academic_year_start')
        end_date = cleaned_data.get('academic_year_end')
        
        if start_date and end_date and start_date >= end_date:
            raise ValidationError('Academic year start date must be before end date.')
        
        return cleaned_data


class GradeForm(forms.ModelForm):
    """Form for managing grades/classes"""
    
    class Meta:
        model = Grade
        fields = [
            'name', 'section', 'level', 'class_teacher', 'minimum_subjects',
            'admission_fee', 'hall_charge', 'boarding_fee', 'is_boarding'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Grade Name (e.g., Grade 1, Form 1)'}),
            'section': forms.Select(attrs={'class': 'form-control'}),
            'level': forms.Select(attrs={'class': 'form-control'}),
            'class_teacher': forms.Select(attrs={'class': 'form-control'}),
            'minimum_subjects': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'admission_fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'hall_charge': forms.NumberInput(attrs={'class': 'form-control'}),
            'boarding_fee': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_boarding': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['class_teacher'].queryset = Teacher.objects.filter(status='active')
        self.fields['class_teacher'].empty_label = "Select Class Teacher"


class DepartmentForm(forms.ModelForm):
    """Form for managing academic departments"""
    
    class Meta:
        model = Department
        fields = ['name', 'head_teacher', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Department Name'}),
            'head_teacher': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Department description'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['head_teacher'].queryset = Teacher.objects.filter(status='active')
        self.fields['head_teacher'].empty_label = "Select Department Head"


class SubjectForm(forms.ModelForm):
    """Form for managing subjects"""
    
    class Meta:
        model = Subject
        fields = ['name', 'code', 'department', 'description', 'is_optional', 'lessons_per_week']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Subject Name'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Subject Code (e.g., MATH101)'}),
            'department': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_optional': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'lessons_per_week': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 10}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['department'].empty_label = "Select Department"


class ClassRoomForm(forms.ModelForm):
    """Form for managing classrooms"""
    
    class Meta:
        model = ClassRoom
        fields = [
            'name', 'capacity', 'building', 'floor', 'room_number',
            'has_projector', 'has_ac', 'class_teacher'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Classroom Name'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'building': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Building Name'}),
            'floor': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Floor'}),
            'room_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Room Number'}),
            'has_projector': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'has_ac': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'class_teacher': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['class_teacher'].queryset = Teacher.objects.filter(status='active')
        self.fields['class_teacher'].empty_label = "Select Class Teacher"


class SystemSettingsForm(forms.Form):
    """Form for managing system settings"""
    
    # Academic settings
    current_academic_year = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '2024-2025'})
    )
    current_term = forms.ChoiceField(
        choices=[('1', 'Term 1'), ('2', 'Term 2'), ('3', 'Term 3')],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Grading settings
    grading_system = forms.ChoiceField(
        choices=[
            ('letter', 'Letter Grades (A, B, C, D, F)'),
            ('percentage', 'Percentage (0-100%)'),
            ('points', 'Points (1-10)'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Attendance settings
    attendance_required = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    minimum_attendance_percentage = forms.IntegerField(
        min_value=0, max_value=100,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '75'})
    )
    
    # Fee settings
    late_fee_percentage = forms.DecimalField(
        max_digits=5, decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'placeholder': '5.00'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Load current settings
        self.load_current_settings()
    
    def load_current_settings(self):
        """Load current system settings into form"""
        settings_map = {
            'current_academic_year': 'current_academic_year',
            'current_term': 'current_term',
            'grading_system': 'grading_system',
            'attendance_required': 'attendance_required',
            'minimum_attendance_percentage': 'minimum_attendance_percentage',
            'late_fee_percentage': 'late_fee_percentage',
        }
        
        for field_name, setting_key in settings_map.items():
            try:
                setting = SystemSettings.objects.get(setting_key=setting_key)
                if field_name in ['attendance_required']:
                    self.fields[field_name].initial = setting.setting_value.lower() == 'true'
                else:
                    self.fields[field_name].initial = setting.setting_value
            except SystemSettings.DoesNotExist:
                pass
