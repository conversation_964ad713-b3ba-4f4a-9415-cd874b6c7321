# Generated by Django 5.2.1 on 2025-06-09 08:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("health", "0001_initial"),
        ("students", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HealthAlert",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("medication_reminder", "Medication Reminder"),
                            ("appointment_reminder", "Appointment Reminder"),
                            ("immunization_due", "Immunization Due"),
                            ("condition_monitoring", "Condition Monitoring"),
                            ("emergency_alert", "Emergency Alert"),
                            ("insurance_expiry", "Insurance Expiry"),
                            ("follow_up_required", "Follow-up Required"),
                        ],
                        max_length=30,
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=200)),
                ("message", models.TextField()),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("dismissed", "Dismissed"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("due_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("acknowledged_at", models.DateTimeField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "acknowledged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="health_alerts",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HealthInsurance",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("insurance_provider", models.CharField(max_length=200)),
                (
                    "insurance_type",
                    models.CharField(
                        choices=[
                            ("private", "Private Insurance"),
                            ("public", "Public/Government Insurance"),
                            ("school", "School Insurance"),
                            ("family", "Family Plan"),
                            ("individual", "Individual Plan"),
                        ],
                        max_length=20,
                    ),
                ),
                ("policy_number", models.CharField(max_length=100)),
                ("group_number", models.CharField(blank=True, max_length=100)),
                ("policy_holder_name", models.CharField(max_length=200)),
                ("policy_holder_relationship", models.CharField(max_length=50)),
                ("effective_date", models.DateField()),
                ("expiry_date", models.DateField(blank=True, null=True)),
                ("coverage_details", models.TextField(blank=True)),
                (
                    "copay_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "deductible_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("emergency_coverage", models.BooleanField(default=True)),
                ("prescription_coverage", models.BooleanField(default=True)),
                ("dental_coverage", models.BooleanField(default=False)),
                ("vision_coverage", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="health_insurance",
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Immunization",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("vaccine_name", models.CharField(max_length=200)),
                (
                    "vaccine_type",
                    models.CharField(
                        choices=[
                            ("routine", "Routine Childhood Vaccine"),
                            ("travel", "Travel Vaccine"),
                            ("seasonal", "Seasonal Vaccine"),
                            ("emergency", "Emergency Vaccine"),
                            ("booster", "Booster Shot"),
                        ],
                        max_length=20,
                    ),
                ),
                ("date_administered", models.DateField()),
                ("administered_by", models.CharField(max_length=200)),
                ("clinic_hospital", models.CharField(blank=True, max_length=200)),
                ("batch_number", models.CharField(blank=True, max_length=100)),
                ("expiry_date", models.DateField(blank=True, null=True)),
                ("next_due_date", models.DateField(blank=True, null=True)),
                ("dose_number", models.IntegerField(default=1)),
                ("total_doses_required", models.IntegerField(default=1)),
                ("side_effects_observed", models.TextField(blank=True)),
                ("certificate_number", models.CharField(blank=True, max_length=100)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="immunizations",
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MedicalCondition",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("condition_name", models.CharField(max_length=200)),
                (
                    "condition_type",
                    models.CharField(
                        choices=[
                            ("chronic", "Chronic Condition"),
                            ("acute", "Acute Condition"),
                            ("allergy", "Allergy"),
                            ("disability", "Disability"),
                            ("mental_health", "Mental Health"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("mild", "Mild"),
                            ("moderate", "Moderate"),
                            ("severe", "Severe"),
                            ("critical", "Critical"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("diagnosed_date", models.DateField(blank=True, null=True)),
                ("diagnosed_by", models.CharField(blank=True, max_length=200)),
                ("is_active", models.BooleanField(default=True)),
                ("requires_medication", models.BooleanField(default=False)),
                ("requires_monitoring", models.BooleanField(default=False)),
                (
                    "emergency_action",
                    models.TextField(
                        blank=True,
                        help_text="Emergency action to take if condition flares up",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_conditions",
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Medication",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("medication_name", models.CharField(max_length=200)),
                (
                    "medication_type",
                    models.CharField(
                        choices=[
                            ("prescription", "Prescription"),
                            ("over_counter", "Over-the-Counter"),
                            ("supplement", "Supplement"),
                            ("emergency", "Emergency Medication"),
                        ],
                        max_length=20,
                    ),
                ),
                ("dosage", models.CharField(max_length=100)),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("once_daily", "Once Daily"),
                            ("twice_daily", "Twice Daily"),
                            ("three_times_daily", "Three Times Daily"),
                            ("four_times_daily", "Four Times Daily"),
                            ("as_needed", "As Needed"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "frequency_details",
                    models.CharField(
                        blank=True, help_text="Specific timing details", max_length=200
                    ),
                ),
                ("prescribed_by", models.CharField(max_length=200)),
                ("prescribed_date", models.DateField()),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("side_effects", models.TextField(blank=True)),
                ("special_instructions", models.TextField(blank=True)),
                ("stored_at_school", models.BooleanField(default=False)),
                ("storage_location", models.CharField(blank=True, max_length=100)),
                ("emergency_medication", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "condition",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="health.medicalcondition",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medications",
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmergencyContact",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("full_name", models.CharField(max_length=200)),
                (
                    "relationship",
                    models.CharField(
                        choices=[
                            ("parent", "Parent"),
                            ("guardian", "Guardian"),
                            ("sibling", "Sibling"),
                            ("grandparent", "Grandparent"),
                            ("aunt_uncle", "Aunt/Uncle"),
                            ("family_friend", "Family Friend"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("primary", "Primary Contact"),
                            ("secondary", "Secondary Contact"),
                            ("tertiary", "Tertiary Contact"),
                        ],
                        max_length=20,
                    ),
                ),
                ("phone_primary", models.CharField(max_length=20)),
                ("phone_secondary", models.CharField(blank=True, max_length=20)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("address", models.TextField(blank=True)),
                ("workplace", models.CharField(blank=True, max_length=200)),
                ("work_phone", models.CharField(blank=True, max_length=20)),
                ("is_authorized_pickup", models.BooleanField(default=False)),
                ("is_medical_decision_maker", models.BooleanField(default=False)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emergency_contacts",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "unique_together": {("student", "priority")},
            },
        ),
    ]
