from django.db import models

# Create your models here.

class LibraryBook(models.Model):
    BOOK_STATUSES = [
        ('available', 'Available'),
        ('damaged', 'Damaged'),
        ('lost', 'Lost'),
    ]
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    isbn = models.CharField(max_length=50, null=True, blank=True)
    author = models.CharField(max_length=255)
    category = models.ForeignKey('LibraryCategory', on_delete=models.CASCADE)
    publisher = models.CharField(max_length=255, null=True, blank=True)
    publication_year = models.PositiveIntegerField(null=True, blank=True)
    edition = models.CharField(max_length=50, null=True, blank=True)
    pages = models.IntegerField(null=True, blank=True)
    available_copies = models.IntegerField(default=0)
    total_copies = models.IntegerField(default=0)
    shelf_location = models.CharField(max_length=50, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    cover_image = models.CharField(max_length=255, null=True, blank=True)
    date_added = models.DateField()
    added_by = models.IntegerField(null=True, blank=True)
    status = models.CharField(max_length=50, default='available', choices=BOOK_STATUSES)

    def __str__(self):
        return self.title

class LibraryCategory(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.name

class LibraryBorrowing(models.Model):
    BORROWING_STATUSES = [
        ('borrowed', 'Borrowed'),
        ('returned', 'Returned'),
        ('overdue', 'Overdue'),
        ('lost', 'Lost'),
    ]
    id = models.AutoField(primary_key=True)
    book = models.ForeignKey(LibraryBook, on_delete=models.CASCADE)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    issue_date = models.DateField()
    due_date = models.DateField()
    return_date = models.DateField(null=True, blank=True)
    fine_amount = models.DecimalField(max_digits=11, decimal_places=2, default=0)
    fine_paid = models.BooleanField(default=False)
    status = models.CharField(max_length=50, default='borrowed', choices=BORROWING_STATUSES)
    issued_by = models.IntegerField(null=True, blank=True)
    returned_to = models.IntegerField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"Borrowing - {self.id}"
