from django.contrib import admin
from .models import Event, EventCategory, EventCategoryType

@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'note', 'color', 'category', 'grade_id', 'create_by', 'creator_type', 'start_date_time', 'end_date_time', 'location', 'is_all_day', 'notification_sent', 'year', 'month')
    search_fields = ('title', 'note', 'category__name', 'grade_id', 'create_by', 'creator_type', 'location')
    list_filter = ('category', 'is_all_day', 'notification_sent', 'year', 'month')
    ordering = ('-year', '-month', 'start_date_time')

@admin.register(EventCategory)
class EventCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'color')
    search_fields = ('name',)
    ordering = ('name',)

@admin.register(EventCategoryType)
class EventCategoryTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'category', 'name')
    search_fields = ('name', 'category__name')
    ordering = ('category', 'name')
