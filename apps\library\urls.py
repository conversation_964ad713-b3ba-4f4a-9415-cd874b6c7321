from django.urls import path
from . import views

app_name = 'library'

urlpatterns = [
    # Dashboard
    path('', views.library_dashboard, name='dashboard'),
    
    # Books
    path('books/', views.book_list, name='book_list'),
    path('books/<int:book_id>/', views.book_detail, name='book_detail'),
    path('books/add/', views.add_book, name='add_book'),
    path('books/<int:book_id>/edit/', views.edit_book, name='edit_book'),
    path('books/<int:book_id>/delete/', views.delete_book, name='delete_book'),
    
    # Categories
    path('categories/', views.category_list, name='category_list'),
    path('categories/add/', views.add_category, name='add_category'),
    path('categories/<int:category_id>/edit/', views.edit_category, name='edit_category'),
    path('categories/<int:category_id>/delete/', views.delete_category, name='delete_category'),
    
    # Borrowing
    path('borrow/', views.borrow_book, name='borrow_book'),
    path('borrowings/', views.borrowing_list, name='borrowing_list'),
    path('borrowings/<int:borrowing_id>/', views.borrowing_detail, name='borrowing_detail'),
    path('borrowings/<int:borrowing_id>/return/', views.return_book, name='return_book'),
    
    # Student history
    path('students/<int:student_id>/history/', views.student_borrowing_history, name='student_borrowing_history'),
    
    # Reports
    path('overdue/', views.overdue_books, name='overdue_books'),
    path('statistics/', views.library_statistics, name='statistics'),
    path('export/borrowings/', views.export_borrowings_csv, name='export_borrowings_csv'),
]
