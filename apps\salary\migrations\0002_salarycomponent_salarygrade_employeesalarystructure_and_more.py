# Generated by Django 5.2.1 on 2025-06-09 11:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("salary", "0001_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SalaryComponent",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.Char<PERSON>ield(max_length=200)),
                (
                    "component_type",
                    models.CharField(
                        choices=[
                            ("allowance", "Allowance"),
                            ("deduction", "Deduction"),
                            ("bonus", "Bonus"),
                            ("overtime", "Overtime"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "calculation_method",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Amount"),
                            ("percentage", "Percentage of Basic Salary"),
                            ("hourly", "Hourly Rate"),
                            ("daily", "Daily Rate"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "fixed_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "percentage_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "hourly_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                (
                    "daily_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                ("is_mandatory", models.BooleanField(default=False)),
                ("is_taxable", models.BooleanField(default=True)),
                ("applies_to_overtime", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="SalaryGrade",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("grade_name", models.CharField(max_length=100, unique=True)),
                (
                    "grade_type",
                    models.CharField(
                        choices=[
                            ("teaching", "Teaching Staff"),
                            ("administrative", "Administrative Staff"),
                            ("support", "Support Staff"),
                            ("management", "Management"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "minimum_salary",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "maximum_salary",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "annual_increment",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["grade_type", "minimum_salary"],
            },
        ),
        migrations.CreateModel(
            name="EmployeeSalaryStructure",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("basic_salary", models.DecimalField(decimal_places=2, max_digits=12)),
                ("effective_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("bank_name", models.CharField(blank=True, max_length=200)),
                ("account_number", models.CharField(blank=True, max_length=50)),
                ("account_name", models.CharField(blank=True, max_length=200)),
                ("bank_branch", models.CharField(blank=True, max_length=200)),
                ("tax_number", models.CharField(blank=True, max_length=50)),
                (
                    "tax_rate",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="salary_structures",
                        to="teachers.teacher",
                    ),
                ),
                (
                    "salary_grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="salary.salarygrade",
                    ),
                ),
            ],
            options={
                "ordering": ["-effective_date"],
            },
        ),
        migrations.CreateModel(
            name="PayrollPeriod",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("period_name", models.CharField(max_length=200)),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("monthly", "Monthly"),
                            ("bi_weekly", "Bi-Weekly"),
                            ("weekly", "Weekly"),
                            ("quarterly", "Quarterly"),
                        ],
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("pay_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("processing", "Processing"),
                            ("approved", "Approved"),
                            ("paid", "Paid"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("total_employees", models.IntegerField(default=0)),
                (
                    "total_gross_salary",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_deductions",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_net_salary",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_payrolls",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-start_date"],
                "unique_together": {("period_type", "start_date", "end_date")},
            },
        ),
        migrations.CreateModel(
            name="EnhancedPayroll",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("payroll_reference", models.CharField(max_length=100, unique=True)),
                ("basic_salary", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "total_allowances",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "total_deductions",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("gross_salary", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("net_salary", models.DecimalField(decimal_places=2, max_digits=12)),
                ("working_days", models.IntegerField(default=0)),
                ("days_worked", models.IntegerField(default=0)),
                (
                    "overtime_hours",
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                ("leave_days", models.IntegerField(default=0)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Bank", "Bank"),
                            ("Cash", "Cash"),
                            ("M-Pesa", "M-Pesa"),
                        ],
                        default="Bank Transfer",
                        max_length=50,
                    ),
                ),
                ("bank_name", models.CharField(blank=True, max_length=200)),
                ("account_number", models.CharField(blank=True, max_length=50)),
                ("transaction_reference", models.CharField(blank=True, max_length=200)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("calculated", "Calculated"),
                            ("approved", "Approved"),
                            ("paid", "Paid"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("calculated_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("paid_at", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="enhanced_payrolls",
                        to="teachers.teacher",
                    ),
                ),
                (
                    "salary_structure",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="salary.employeesalarystructure",
                    ),
                ),
                (
                    "payroll_period",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payrolls",
                        to="salary.payrollperiod",
                    ),
                ),
            ],
            options={
                "ordering": ["-payroll_period__start_date", "employee__full_name"],
                "unique_together": {("employee", "payroll_period")},
            },
        ),
        migrations.CreateModel(
            name="PaySlip",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("payslip_number", models.CharField(max_length=100, unique=True)),
                ("generated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("generated", "Generated"),
                            ("sent", "Sent"),
                            ("viewed", "Viewed"),
                            ("downloaded", "Downloaded"),
                        ],
                        default="generated",
                        max_length=20,
                    ),
                ),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("viewed_at", models.DateTimeField(blank=True, null=True)),
                ("downloaded_at", models.DateTimeField(blank=True, null=True)),
                ("pdf_file", models.FileField(blank=True, upload_to="payslips/")),
                ("file_size", models.IntegerField(default=0)),
                (
                    "generated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "payroll",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payslip",
                        to="salary.enhancedpayroll",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PayrollComponent",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "base_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "calculated_amount",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "units",
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                (
                    "rate",
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "payroll",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="components",
                        to="salary.enhancedpayroll",
                    ),
                ),
                (
                    "component",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="salary.salarycomponent",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmployeeSalaryComponent",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "custom_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "custom_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "custom_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("effective_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "salary_structure",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="components",
                        to="salary.employeesalarystructure",
                    ),
                ),
                (
                    "component",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="salary.salarycomponent",
                    ),
                ),
            ],
        ),
    ]
