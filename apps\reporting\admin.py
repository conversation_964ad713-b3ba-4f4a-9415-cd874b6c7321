from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count
from .models import (
    ReportTemplate, GeneratedReport, Dashboard, DashboardWidget,
    ReportSchedule, DataVisualization
)


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'report_type', 'output_format', 'is_automated',
        'frequency', 'is_public', 'created_by', 'created_at'
    )
    list_filter = ('report_type', 'output_format', 'is_automated', 'frequency', 'is_public')
    search_fields = ('name', 'description', 'created_by__email')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'report_type', 'output_format')
        }),
        ('Configuration', {
            'fields': ('template_config', 'sql_query', 'chart_config')
        }),
        ('Automation', {
            'fields': ('is_automated', 'frequency', 'next_run')
        }),
        ('Permissions', {
            'fields': ('is_public', 'allowed_users')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at', 'is_active'),
            'classes': ('collapse',)
        })
    )
    
    filter_horizontal = ('allowed_users',)
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(GeneratedReport)
class GeneratedReportAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'template', 'status', 'file_size_display',
        'total_records', 'generated_by', 'generated_at', 'accessed_count'
    )
    list_filter = ('status', 'template__report_type', 'generated_at')
    search_fields = ('title', 'template__name', 'generated_by__email')
    readonly_fields = (
        'generated_at', 'generation_time', 'file_size',
        'accessed_count', 'last_accessed'
    )
    ordering = ('-generated_at',)
    
    fieldsets = (
        ('Report Information', {
            'fields': ('template', 'title', 'status', 'parameters')
        }),
        ('File Information', {
            'fields': ('file_path', 'file_size', 'generation_time')
        }),
        ('Statistics', {
            'fields': ('total_records', 'accessed_count', 'last_accessed')
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('generated_by', 'generated_at', 'expires_at'),
            'classes': ('collapse',)
        })
    )
    
    def file_size_display(self, obj):
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} B"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return "N/A"
    file_size_display.short_description = "File Size"
    
    def has_add_permission(self, request):
        return False  # Reports are generated, not manually created


@admin.register(Dashboard)
class DashboardAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'dashboard_type', 'widget_count', 'is_default',
        'is_public', 'created_by', 'created_at'
    )
    list_filter = ('dashboard_type', 'is_default', 'is_public', 'is_active')
    search_fields = ('name', 'description', 'created_by__email')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'dashboard_type')
        }),
        ('Configuration', {
            'fields': ('layout_config', 'refresh_interval')
        }),
        ('Permissions', {
            'fields': ('is_default', 'is_public', 'allowed_users')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at', 'is_active'),
            'classes': ('collapse',)
        })
    )
    
    filter_horizontal = ('allowed_users',)
    
    def widget_count(self, obj):
        return obj.widgets.count()
    widget_count.short_description = "Widgets"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class DashboardWidgetInline(admin.TabularInline):
    model = DashboardWidget
    extra = 0
    fields = ('title', 'widget_type', 'chart_type', 'position_x', 'position_y', 'width', 'height', 'is_active')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(DashboardWidget)
class DashboardWidgetAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'dashboard', 'widget_type', 'chart_type',
        'position_display', 'size_display', 'auto_refresh', 'is_active'
    )
    list_filter = ('widget_type', 'chart_type', 'auto_refresh', 'is_active')
    search_fields = ('title', 'dashboard__name', 'data_source')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('dashboard', 'order')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('dashboard', 'title', 'widget_type', 'chart_type')
        }),
        ('Position and Size', {
            'fields': ('position_x', 'position_y', 'width', 'height', 'order')
        }),
        ('Data Configuration', {
            'fields': ('data_source', 'data_config', 'chart_config')
        }),
        ('Refresh Settings', {
            'fields': ('auto_refresh', 'refresh_interval')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'is_active'),
            'classes': ('collapse',)
        })
    )
    
    def position_display(self, obj):
        return f"({obj.position_x}, {obj.position_y})"
    position_display.short_description = "Position"
    
    def size_display(self, obj):
        return f"{obj.width} × {obj.height}"
    size_display.short_description = "Size"


@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'template', 'cron_expression', 'is_active',
        'last_run', 'next_run', 'run_count'
    )
    list_filter = ('is_active', 'timezone', 'last_run')
    search_fields = ('name', 'template__name', 'created_by__email')
    readonly_fields = ('last_run', 'next_run', 'run_count', 'created_at', 'updated_at')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'template', 'is_active')
        }),
        ('Schedule Configuration', {
            'fields': ('cron_expression', 'timezone')
        }),
        ('Recipients', {
            'fields': ('email_recipients', 'user_recipients')
        }),
        ('Execution History', {
            'fields': ('last_run', 'next_run', 'run_count'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    filter_horizontal = ('user_recipients',)
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DataVisualization)
class DataVisualizationAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'visualization_type', 'is_public',
        'created_by', 'created_at', 'is_active'
    )
    list_filter = ('visualization_type', 'is_public', 'is_active')
    search_fields = ('name', 'description', 'created_by__email')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'visualization_type')
        }),
        ('Configuration', {
            'fields': ('data_query', 'config')
        }),
        ('Permissions', {
            'fields': ('is_public',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at', 'is_active'),
            'classes': ('collapse',)
        })
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# Add inline widgets to Dashboard admin
DashboardAdmin.inlines = [DashboardWidgetInline]
