# Generated by Django 5.2.1 on 2025-05-21 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ContactMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("email", models.EmailField(max_length=254)),
                ("subject", models.CharField(max_length=255)),
                ("message", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_read", models.<PERSON><PERSON><PERSON><PERSON>ield(default=False)),
                ("replied", models.BooleanField(default=False)),
                ("reply_message", models.TextField(blank=True, null=True)),
                ("replied_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="FAQ",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question", models.CharField(max_length=255)),
                ("answer", models.TextField()),
                ("category", models.CharField(default="General", max_length=100)),
                ("is_active", models.BooleanField(default=True)),
                ("order", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "FAQ",
                "verbose_name_plural": "FAQs",
                "ordering": ["order", "category"],
            },
        ),
        migrations.CreateModel(
            name="SchoolInfo",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("motto", models.CharField(blank=True, max_length=255, null=True)),
                ("logo", models.ImageField(blank=True, null=True, upload_to="school/")),
                (
                    "school_type",
                    models.CharField(
                        choices=[
                            ("Primary", "Primary"),
                            ("Secondary", "Secondary"),
                            ("College", "College"),
                        ],
                        max_length=100,
                    ),
                ),
                (
                    "curriculum_type",
                    models.CharField(
                        choices=[
                            ("CBC", "CBC"),
                            ("8-4-4", "8-4-4"),
                            ("IGCSE", "IGCSE"),
                        ],
                        max_length=100,
                    ),
                ),
                ("academic_year_start", models.DateField(blank=True, null=True)),
                ("academic_year_end", models.DateField(blank=True, null=True)),
                ("address", models.TextField()),
                ("city", models.CharField(max_length=100)),
                ("county", models.CharField(max_length=100)),
                ("postal_code", models.CharField(blank=True, max_length=20, null=True)),
                ("phone", models.CharField(max_length=20)),
                ("alt_phone", models.CharField(blank=True, max_length=20, null=True)),
                ("email", models.EmailField(max_length=254)),
                ("website", models.URLField(blank=True, null=True)),
                (
                    "working_hours",
                    models.CharField(default="8:00 AM - 5:00 PM", max_length=100),
                ),
                (
                    "principal_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("principal_message", models.TextField(blank=True, null=True)),
                ("theme_color", models.CharField(default="#4682B4", max_length=20)),
                ("date_established", models.DateField(blank=True, null=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "School Information",
            },
        ),
        migrations.CreateModel(
            name="SystemSettings",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("setting_key", models.CharField(max_length=100)),
                ("setting_value", models.TextField()),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_public", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Testimonial",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("role", models.CharField(max_length=100)),
                ("message", models.TextField()),
                (
                    "image",
                    models.ImageField(blank=True, null=True, upload_to="testimonials/"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
