# Generated by Django 5.2.1 on 2025-06-09 11:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("academics", "0002_initial"),
        ("students", "0001_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ExamType",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, unique=True)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("continuous_assessment", "Continuous Assessment"),
                            ("mid_term", "Mid-Term Examination"),
                            ("end_term", "End-Term Examination"),
                            ("annual", "Annual Examination"),
                            ("mock", "Mock Examination"),
                            ("national", "National Examination"),
                            ("entrance", "Entrance Examination"),
                            ("placement", "Placement Test"),
                            ("diagnostic", "Diagnostic Test"),
                            ("remedial", "Remedial Test"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("default_duration_minutes", models.IntegerField(default=120)),
                ("default_total_marks", models.IntegerField(default=100)),
                (
                    "weight_percentage",
                    models.DecimalField(decimal_places=2, default=100.0, max_digits=5),
                ),
                ("requires_timetable", models.BooleanField(default=True)),
                ("allows_retake", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["category", "name"],
            },
        ),
        migrations.CreateModel(
            name="Exam",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("exam_code", models.CharField(max_length=50, unique=True)),
                ("name", models.CharField(max_length=255)),
                ("academic_year", models.IntegerField()),
                ("term", models.IntegerField(default=1)),
                ("description", models.TextField(blank=True)),
                ("instructions", models.TextField(blank=True)),
                ("total_marks", models.IntegerField(default=100)),
                ("pass_marks", models.IntegerField(default=40)),
                ("duration_minutes", models.IntegerField(default=120)),
                (
                    "marking_scheme",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage (0-100)"),
                            ("points", "Points System"),
                            ("letter_grade", "Letter Grades"),
                            ("gpa", "GPA System"),
                        ],
                        default="percentage",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("registration_start", models.DateField(blank=True, null=True)),
                ("registration_end", models.DateField(blank=True, null=True)),
                ("results_release_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("scheduled", "Scheduled"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("postponed", "Postponed"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("is_published", models.BooleanField(default=False)),
                ("results_published", models.BooleanField(default=False)),
                ("allow_calculator", models.BooleanField(default=False)),
                ("allow_reference_materials", models.BooleanField(default=False)),
                ("requires_signature", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grades",
                    models.ManyToManyField(related_name="exams", to="academics.grade"),
                ),
                (
                    "subjects",
                    models.ManyToManyField(
                        related_name="exams", to="academics.subject"
                    ),
                ),
                (
                    "exam_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exams",
                        to="exams.examtype",
                    ),
                ),
            ],
            options={
                "ordering": ["-academic_year", "-term", "start_date"],
                "unique_together": {("exam_code", "academic_year")},
            },
        ),
        migrations.CreateModel(
            name="ClassRanking",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "ranking_criteria",
                    models.CharField(
                        choices=[
                            ("overall_average", "Overall Average"),
                            ("total_marks", "Total Marks"),
                            ("gpa", "GPA"),
                            ("weighted_average", "Weighted Average"),
                        ],
                        default="overall_average",
                        max_length=20,
                    ),
                ),
                ("include_absent_students", models.BooleanField(default=False)),
                ("minimum_subjects_required", models.IntegerField(default=1)),
                ("total_students_ranked", models.IntegerField()),
                ("highest_score", models.DecimalField(decimal_places=2, max_digits=6)),
                ("lowest_score", models.DecimalField(decimal_places=2, max_digits=6)),
                ("class_average", models.DecimalField(decimal_places=2, max_digits=6)),
                ("generated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "generated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="class_rankings",
                        to="academics.grade",
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="class_rankings",
                        to="exams.exam",
                    ),
                ),
            ],
            options={
                "ordering": ["-exam__start_date", "grade"],
                "unique_together": {("exam", "grade", "ranking_criteria")},
            },
        ),
        migrations.CreateModel(
            name="ExamRangeGrade",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mark_range", models.CharField(max_length=255)),
                ("_from", models.IntegerField()),
                ("_to", models.IntegerField()),
                ("mark_grade", models.CharField(max_length=255)),
                (
                    "grade_point",
                    models.DecimalField(
                        blank=True, decimal_places=1, max_digits=3, null=True
                    ),
                ),
                ("remarks", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ExamTimetable",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("exam_date", models.DateField()),
                ("start_time", models.TimeField()),
                ("end_time", models.TimeField()),
                ("duration_minutes", models.IntegerField()),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("morning", "Morning Session"),
                            ("afternoon", "Afternoon Session"),
                            ("evening", "Evening Session"),
                        ],
                        default="morning",
                        max_length=20,
                    ),
                ),
                ("total_marks", models.IntegerField(default=100)),
                ("instructions", models.TextField(blank=True)),
                ("special_requirements", models.TextField(blank=True)),
                ("materials_allowed", models.TextField(blank=True)),
                ("materials_provided", models.TextField(blank=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("published", "Published"),
                            ("modified", "Modified"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("is_published", models.BooleanField(default=False)),
                ("published_at", models.DateTimeField(blank=True, null=True)),
                ("expected_candidates", models.IntegerField(default=0)),
                ("actual_candidates", models.IntegerField(default=0)),
                ("absentees", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "additional_venues",
                    models.ManyToManyField(
                        blank=True,
                        related_name="additional_exam_venues",
                        to="academics.classroom",
                    ),
                ),
                (
                    "assistant_invigilators",
                    models.ManyToManyField(
                        blank=True,
                        related_name="assistant_invigilated_exams",
                        to="teachers.teacher",
                    ),
                ),
                (
                    "chief_invigilator",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="chief_invigilated_exams",
                        to="teachers.teacher",
                    ),
                ),
                (
                    "classroom",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.classroom",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="timetable_entries",
                        to="exams.exam",
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
            ],
            options={
                "ordering": ["exam_date", "start_time", "grade", "subject"],
                "unique_together": {("exam", "grade", "subject")},
            },
        ),
        migrations.CreateModel(
            name="GradingSystem",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, unique=True)),
                (
                    "system_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage System"),
                            ("letter_grade", "Letter Grade System"),
                            ("gpa_4", "4-Point GPA System"),
                            ("gpa_5", "5-Point GPA System"),
                            ("points", "Points System"),
                            ("custom", "Custom System"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("is_default", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("use_weighted_average", models.BooleanField(default=False)),
                ("round_to_decimal_places", models.IntegerField(default=2)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "grades",
                    models.ManyToManyField(
                        related_name="grading_systems", to="academics.grade"
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceComparison",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("comparison_name", models.CharField(max_length=200)),
                (
                    "comparison_type",
                    models.CharField(
                        choices=[
                            ("term_to_term", "Term to Term"),
                            ("year_to_year", "Year to Year"),
                            ("subject_progression", "Subject Progression"),
                            ("peer_comparison", "Peer Comparison"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("total_students_analyzed", models.IntegerField(default=0)),
                ("improvement_count", models.IntegerField(default=0)),
                ("decline_count", models.IntegerField(default=0)),
                ("stable_count", models.IntegerField(default=0)),
                (
                    "average_improvement",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                (
                    "highest_improvement",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                (
                    "highest_decline",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                ("analysis_report", models.TextField(blank=True)),
                ("recommendations", models.TextField(blank=True)),
                ("analyzed_at", models.DateTimeField(auto_now_add=True)),
                (
                    "analyzed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "exams",
                    models.ManyToManyField(
                        related_name="performance_comparisons", to="exams.exam"
                    ),
                ),
                (
                    "grades",
                    models.ManyToManyField(
                        blank=True,
                        related_name="performance_comparisons",
                        to="academics.grade",
                    ),
                ),
                (
                    "students",
                    models.ManyToManyField(
                        related_name="performance_comparisons", to="students.student"
                    ),
                ),
                (
                    "subjects",
                    models.ManyToManyField(
                        blank=True,
                        related_name="performance_comparisons",
                        to="academics.subject",
                    ),
                ),
            ],
            options={
                "ordering": ["-analyzed_at"],
            },
        ),
        migrations.CreateModel(
            name="StudentExam",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("marks", models.CharField(max_length=255)),
                ("grade", models.CharField(blank=True, max_length=10, null=True)),
                ("remarks", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "position_in_subject",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("First", "First"),
                            ("Second", "Second"),
                            ("Third", "Third"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                ("year", models.PositiveIntegerField()),
                ("date", models.DateField()),
                ("recorded_by", models.BigIntegerField(blank=True, null=True)),
                ("verified", models.BooleanField(default=False)),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="exams.exam"
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentOverallExamResult",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("total_marks", models.DecimalField(decimal_places=2, max_digits=11)),
                ("average", models.DecimalField(decimal_places=2, max_digits=11)),
                ("grade", models.CharField(blank=True, max_length=10, null=True)),
                ("position_in_class", models.IntegerField(blank=True, null=True)),
                ("class_teacher_remarks", models.TextField(blank=True, null=True)),
                ("principal_remarks", models.TextField(blank=True, null=True)),
                (
                    "attendance_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "previous_average",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                (
                    "deviation",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                ("year", models.PositiveIntegerField()),
                ("term", models.IntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="exams.exam"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentOverallPerformance",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("total_subjects", models.IntegerField()),
                ("subjects_passed", models.IntegerField()),
                ("subjects_failed", models.IntegerField()),
                (
                    "total_marks_obtained",
                    models.DecimalField(decimal_places=2, max_digits=8),
                ),
                (
                    "total_marks_possible",
                    models.DecimalField(decimal_places=2, max_digits=8),
                ),
                (
                    "overall_percentage",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                ("overall_grade", models.CharField(blank=True, max_length=5)),
                (
                    "overall_gpa",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=4, null=True
                    ),
                ),
                ("class_position", models.IntegerField(blank=True, null=True)),
                ("grade_position", models.IntegerField(blank=True, null=True)),
                ("school_position", models.IntegerField(blank=True, null=True)),
                ("total_students_in_class", models.IntegerField(default=0)),
                ("total_students_in_grade", models.IntegerField(default=0)),
                (
                    "highest_subject_score",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "lowest_subject_score",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "previous_exam_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "percentage_change",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                (
                    "performance_trend",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("improving", "Improving"),
                            ("declining", "Declining"),
                            ("stable", "Stable"),
                            ("fluctuating", "Fluctuating"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "attendance_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("class_teacher_remarks", models.TextField(blank=True)),
                ("principal_remarks", models.TextField(blank=True)),
                ("recommendations", models.TextField(blank=True)),
                ("is_promoted", models.BooleanField(blank=True, null=True)),
                ("promotion_status", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "best_subject",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="best_performances",
                        to="academics.subject",
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="overall_performances",
                        to="exams.exam",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="overall_performances",
                        to="students.student",
                    ),
                ),
                (
                    "weakest_subject",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="weakest_performances",
                        to="academics.subject",
                    ),
                ),
            ],
            options={
                "ordering": ["-exam__start_date", "class_position"],
                "unique_together": {("student", "exam")},
            },
        ),
        migrations.CreateModel(
            name="EnhancedStudentExamResult",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("marks_obtained", models.DecimalField(decimal_places=2, max_digits=6)),
                ("total_marks", models.DecimalField(decimal_places=2, max_digits=6)),
                ("percentage", models.DecimalField(decimal_places=2, max_digits=5)),
                ("letter_grade", models.CharField(blank=True, max_length=5)),
                (
                    "grade_point",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=4, null=True
                    ),
                ),
                ("subject_position", models.IntegerField(blank=True, null=True)),
                ("grade_position", models.IntegerField(blank=True, null=True)),
                (
                    "percentile",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "attendance_status",
                    models.CharField(
                        choices=[
                            ("present", "Present"),
                            ("absent", "Absent"),
                            ("late", "Late"),
                            ("excused", "Excused"),
                        ],
                        default="present",
                        max_length=20,
                    ),
                ),
                ("start_time", models.TimeField(blank=True, null=True)),
                ("submission_time", models.TimeField(blank=True, null=True)),
                ("time_taken_minutes", models.IntegerField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("verified", "Verified"),
                            ("published", "Published"),
                            ("disputed", "Disputed"),
                            ("corrected", "Corrected"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("verification_notes", models.TextField(blank=True)),
                ("marked_date", models.DateTimeField(blank=True, null=True)),
                ("verified_date", models.DateTimeField(blank=True, null=True)),
                ("teacher_remarks", models.TextField(blank=True)),
                ("special_considerations", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "marked_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="marked_exams",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_results",
                        to="students.student",
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_exams",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_results",
                        to="exams.exam",
                    ),
                ),
                (
                    "timetable_entry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_results",
                        to="exams.examtimetable",
                    ),
                ),
            ],
            options={
                "ordering": ["-exam__start_date", "student__full_name"],
                "unique_together": {("student", "exam", "timetable_entry")},
            },
        ),
        migrations.CreateModel(
            name="GradeScale",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("letter_grade", models.CharField(max_length=5)),
                ("grade_point", models.DecimalField(decimal_places=2, max_digits=4)),
                ("min_percentage", models.DecimalField(decimal_places=2, max_digits=5)),
                ("max_percentage", models.DecimalField(decimal_places=2, max_digits=5)),
                ("description", models.CharField(blank=True, max_length=100)),
                ("remarks", models.CharField(blank=True, max_length=255)),
                ("is_passing_grade", models.BooleanField(default=True)),
                ("color_code", models.CharField(default="#3B82F6", max_length=7)),
                (
                    "grading_system",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="grade_scales",
                        to="exams.gradingsystem",
                    ),
                ),
            ],
            options={
                "ordering": ["-min_percentage"],
                "unique_together": {("grading_system", "letter_grade")},
            },
        ),
        migrations.CreateModel(
            name="ReportCard",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("report_number", models.CharField(max_length=100, unique=True)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("term_report", "Term Report"),
                            ("annual_report", "Annual Report"),
                            ("progress_report", "Progress Report"),
                            ("transcript", "Academic Transcript"),
                        ],
                        default="term_report",
                        max_length=20,
                    ),
                ),
                ("academic_year", models.IntegerField()),
                ("term", models.IntegerField()),
                ("include_subject_details", models.BooleanField(default=True)),
                ("include_attendance", models.BooleanField(default=True)),
                ("include_conduct_grades", models.BooleanField(default=True)),
                ("include_teacher_remarks", models.BooleanField(default=True)),
                ("include_recommendations", models.BooleanField(default=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("generated", "Generated"),
                            ("reviewed", "Reviewed"),
                            ("approved", "Approved"),
                            ("distributed", "Distributed"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("generated_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("distributed_at", models.DateTimeField(blank=True, null=True)),
                ("pdf_file", models.FileField(blank=True, upload_to="report_cards/")),
                ("file_size", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="report_cards",
                        to="exams.exam",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="report_cards",
                        to="students.student",
                    ),
                ),
                (
                    "overall_performance",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="report_card",
                        to="exams.studentoverallperformance",
                    ),
                ),
            ],
            options={
                "ordering": ["-academic_year", "-term", "student__full_name"],
                "unique_together": {("student", "exam", "report_type")},
            },
        ),
        migrations.CreateModel(
            name="StudentRanking",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("position", models.IntegerField()),
                ("score", models.DecimalField(decimal_places=2, max_digits=6)),
                ("percentile", models.DecimalField(decimal_places=2, max_digits=5)),
                ("subjects_count", models.IntegerField()),
                ("average_score", models.DecimalField(decimal_places=2, max_digits=5)),
                (
                    "improvement_from_previous",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                (
                    "class_ranking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_rankings",
                        to="exams.classranking",
                    ),
                ),
                (
                    "overall_performance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rankings",
                        to="exams.studentoverallperformance",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rankings",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "ordering": ["position"],
                "unique_together": {("class_ranking", "student")},
            },
        ),
        migrations.CreateModel(
            name="SubjectPerformanceAnalysis",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("total_students", models.IntegerField()),
                ("students_present", models.IntegerField()),
                ("students_passed", models.IntegerField()),
                ("students_failed", models.IntegerField()),
                ("highest_score", models.DecimalField(decimal_places=2, max_digits=5)),
                ("lowest_score", models.DecimalField(decimal_places=2, max_digits=5)),
                ("average_score", models.DecimalField(decimal_places=2, max_digits=5)),
                (
                    "median_score",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "standard_deviation",
                    models.DecimalField(
                        blank=True, decimal_places=3, max_digits=6, null=True
                    ),
                ),
                ("grade_a_count", models.IntegerField(default=0)),
                ("grade_b_count", models.IntegerField(default=0)),
                ("grade_c_count", models.IntegerField(default=0)),
                ("grade_d_count", models.IntegerField(default=0)),
                ("grade_f_count", models.IntegerField(default=0)),
                ("pass_rate", models.DecimalField(decimal_places=2, max_digits=5)),
                (
                    "attendance_rate",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                (
                    "difficulty_index",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("teacher_remarks", models.TextField(blank=True)),
                ("improvement_suggestions", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_analyses",
                        to="exams.exam",
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_analyses",
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_analyses",
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "ordering": ["-exam__start_date", "subject__name"],
                "unique_together": {("exam", "subject", "grade")},
            },
        ),
    ]
