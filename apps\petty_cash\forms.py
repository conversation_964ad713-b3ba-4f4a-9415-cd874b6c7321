from django import forms
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from .models import (
    PettyCash, EnhancedPettyCashRequest, PettyCashCategory,
    PettyCashFund, PettyCashExpense
)

class PettyCashRequestForm(forms.ModelForm):
    """Enhanced form for creating petty cash requests"""

    class Meta:
        model = EnhancedPettyCashRequest
        fields = [
            'fund', 'category', 'request_type', 'title', 'description',
            'amount_requested', 'required_date', 'priority', 'supporting_documents'
        ]
        widgets = {
            'fund': forms.Select(attrs={
                'class': 'form-input',
                'required': True
            }),
            'category': forms.Select(attrs={
                'class': 'form-input',
                'required': True
            }),
            'request_type': forms.Select(attrs={
                'class': 'form-input',
                'required': True
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Brief title for your request',
                'maxlength': 200,
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-input',
                'rows': 4,
                'placeholder': 'Detailed description of the request...',
                'required': True
            }),
            'amount_requested': forms.NumberInput(attrs={
                'class': 'form-input',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0.01',
                'required': True
            }),
            'required_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True
            }),
            'priority': forms.Select(attrs={
                'class': 'form-input'
            }),
            'supporting_documents': forms.FileInput(attrs={
                'class': 'form-input',
                'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set minimum required date to tomorrow
        tomorrow = date.today() + timedelta(days=1)
        self.fields['required_date'].widget.attrs['min'] = tomorrow.strftime('%Y-%m-%d')

        # Filter active categories and funds
        self.fields['category'].queryset = PettyCashCategory.objects.filter(is_active=True)
        self.fields['fund'].queryset = PettyCashFund.objects.filter(status='active')

        # Set default required date to one week from today
        if not self.instance.pk:
            default_date = date.today() + timedelta(days=7)
            self.fields['required_date'].initial = default_date

    def clean_amount_requested(self):
        amount = self.cleaned_data.get('amount_requested')
        if amount and amount <= 0:
            raise ValidationError('Amount must be greater than zero.')

        # Check against fund balance if fund is selected
        fund = self.cleaned_data.get('fund')
        if fund and amount and amount > fund.current_balance:
            raise ValidationError(
                f'Requested amount (KSh {amount:,.2f}) exceeds available fund balance '
                f'(KSh {fund.current_balance:,.2f}).'
            )

        return amount

    def clean_required_date(self):
        required_date = self.cleaned_data.get('required_date')
        if required_date and required_date <= date.today():
            raise ValidationError('Required date must be in the future.')
        return required_date

    def clean(self):
        cleaned_data = super().clean()
        category = cleaned_data.get('category')
        amount = cleaned_data.get('amount_requested')

        # Check category-specific validations
        if category and amount:
            if category.approval_threshold and amount > category.approval_threshold:
                # This is just informational - the form will still be valid
                self.add_error(None,
                    f'Note: This amount exceeds the approval threshold for {category.name} '
                    f'(KSh {category.approval_threshold:,.2f}) and will require additional approval.'
                )

        return cleaned_data


class LegacyPettyCashRequestForm(forms.ModelForm):
    """Legacy form for backward compatibility"""
    class Meta:
        model = PettyCash
        fields = ['paid', 'purpose']
        widgets = {
            'paid': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border rounded',
                'placeholder': 'Amount'
            }),
            'purpose': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border rounded',
                'rows': 2,
                'placeholder': 'Purpose of request'
            }),
        }


class PettyCashExpenseForm(forms.ModelForm):
    """Form for recording actual expenses"""

    class Meta:
        model = PettyCashExpense
        fields = [
            'category', 'description', 'amount', 'expense_date',
            'vendor_name', 'receipt_number', 'receipt_image',
            'supporting_documents', 'notes'
        ]
        widgets = {
            'category': forms.Select(attrs={'class': 'form-input'}),
            'description': forms.Textarea(attrs={
                'class': 'form-input',
                'rows': 3,
                'placeholder': 'Description of the expense...'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0.01'
            }),
            'expense_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date'
            }),
            'vendor_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Vendor/Supplier name'
            }),
            'receipt_number': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Receipt/Invoice number'
            }),
            'receipt_image': forms.FileInput(attrs={
                'class': 'form-input',
                'accept': 'image/*'
            }),
            'supporting_documents': forms.FileInput(attrs={
                'class': 'form-input',
                'accept': '.pdf,.doc,.docx'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-input',
                'rows': 2,
                'placeholder': 'Additional notes...'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = PettyCashCategory.objects.filter(is_active=True)

        # Set default expense date to today
        if not self.instance.pk:
            self.fields['expense_date'].initial = date.today()


class PettyCashSearchForm(forms.Form):
    """Form for searching and filtering petty cash requests"""

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search by request number, title, or description...'
        })
    )

    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + EnhancedPettyCashRequest.REQUEST_STATUSES,
        widget=forms.Select(attrs={'class': 'form-input'})
    )

    category = forms.ModelChoiceField(
        required=False,
        queryset=PettyCashCategory.objects.filter(is_active=True),
        empty_label='All Categories',
        widget=forms.Select(attrs={'class': 'form-input'})
    )

    request_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + EnhancedPettyCashRequest.REQUEST_TYPES,
        widget=forms.Select(attrs={'class': 'form-input'})
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-input',
            'type': 'date'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-input',
            'type': 'date'
        })
    )