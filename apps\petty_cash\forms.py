from django import forms
from .models import PettyCash

class PettyCashRequestForm(forms.ModelForm):
    class Meta:
        model = PettyCash
        fields = ['paid', 'purpose']
        widgets = {
            'paid': forms.NumberInput(attrs={'class': 'w-full px-3 py-2 border rounded', 'placeholder': 'Amount'}),
            'purpose': forms.Textarea(attrs={'class': 'w-full px-3 py-2 border rounded', 'rows': 2, 'placeholder': 'Purpose of request'}),
        } 