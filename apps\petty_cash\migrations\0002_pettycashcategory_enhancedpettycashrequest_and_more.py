# Generated by Django 5.2.1 on 2025-06-09 11:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("petty_cash", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PettyCashCategory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, unique=True)),
                (
                    "category_type",
                    models.CharField(
                        choices=[
                            ("office_supplies", "Office Supplies"),
                            ("maintenance", "Maintenance & Repairs"),
                            ("utilities", "Utilities"),
                            ("transport", "Transport & Travel"),
                            ("communication", "Communication"),
                            ("refreshments", "Refreshments"),
                            ("emergency", "Emergency Expenses"),
                            ("miscellaneous", "Miscellaneous"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "budget_limit",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("requires_approval", models.BooleanField(default=False)),
                (
                    "approval_threshold",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Petty Cash Categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="EnhancedPettyCashRequest",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("request_number", models.CharField(max_length=100, unique=True)),
                (
                    "request_type",
                    models.CharField(
                        choices=[
                            ("expense", "Expense Request"),
                            ("advance", "Cash Advance"),
                            ("reimbursement", "Reimbursement"),
                            ("replenishment", "Fund Replenishment"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "amount_requested",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "amount_approved",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("requested_date", models.DateField(auto_now_add=True)),
                ("required_date", models.DateField()),
                ("approved_date", models.DateField(blank=True, null=True)),
                ("disbursed_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("under_review", "Under Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("disbursed", "Disbursed"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("rejection_reason", models.TextField(blank=True)),
                ("approval_notes", models.TextField(blank=True)),
                (
                    "supporting_documents",
                    models.FileField(blank=True, upload_to="petty_cash/documents/"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_petty_cash_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "disbursed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="disbursed_petty_cash_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="petty_cash_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="petty_cash.pettycashcategory",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PettyCashFund",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("fund_name", models.CharField(max_length=200)),
                (
                    "initial_amount",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "current_balance",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "minimum_balance",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("maximum_limit", models.DecimalField(decimal_places=2, max_digits=12)),
                ("establishment_date", models.DateField()),
                ("last_replenishment_date", models.DateField(blank=True, null=True)),
                ("last_reconciliation_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("suspended", "Suspended"),
                            ("closed", "Closed"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "total_disbursed",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "total_replenished",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "custodian",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="managed_funds",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PettyCashExpense",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("expense_number", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField()),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("expense_date", models.DateField()),
                ("vendor_name", models.CharField(blank=True, max_length=200)),
                ("receipt_number", models.CharField(blank=True, max_length=100)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("reimbursed", "Reimbursed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("approved_date", models.DateField(blank=True, null=True)),
                (
                    "receipt_image",
                    models.ImageField(blank=True, upload_to="petty_cash/receipts/"),
                ),
                (
                    "supporting_documents",
                    models.FileField(blank=True, upload_to="petty_cash/expense_docs/"),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_expenses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="petty_cash.pettycashcategory",
                    ),
                ),
                (
                    "incurred_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incurred_expenses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "request",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="expenses",
                        to="petty_cash.enhancedpettycashrequest",
                    ),
                ),
                (
                    "fund",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="expenses",
                        to="petty_cash.pettycashfund",
                    ),
                ),
            ],
            options={
                "ordering": ["-expense_date"],
            },
        ),
        migrations.AddField(
            model_name="enhancedpettycashrequest",
            name="fund",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="requests",
                to="petty_cash.pettycashfund",
            ),
        ),
        migrations.CreateModel(
            name="PettyCashReconciliation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "reconciliation_number",
                    models.CharField(max_length=100, unique=True),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("reconciliation_date", models.DateField(auto_now_add=True)),
                (
                    "opening_balance",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "closing_balance",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "calculated_balance",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "physical_cash_count",
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    "variance",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "total_expenses",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "total_replenishments",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("number_of_transactions", models.IntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("discrepancy", "Discrepancy Found"),
                            ("resolved", "Resolved"),
                        ],
                        default="in_progress",
                        max_length=20,
                    ),
                ),
                ("discrepancy_explanation", models.TextField(blank=True)),
                ("resolution_notes", models.TextField(blank=True)),
                (
                    "reconciliation_report",
                    models.FileField(
                        blank=True, upload_to="petty_cash/reconciliation_reports/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "fund",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reconciliations",
                        to="petty_cash.pettycashfund",
                    ),
                ),
                (
                    "reconciled_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reconciliations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_reconciliations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PettyCashReplenishment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("replenishment_number", models.CharField(max_length=100, unique=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=12)),
                ("reason", models.TextField()),
                ("requested_date", models.DateField(auto_now_add=True)),
                ("approved_date", models.DateField(blank=True, null=True)),
                ("disbursed_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("requested", "Requested"),
                            ("approved", "Approved"),
                            ("disbursed", "Disbursed"),
                            ("rejected", "Rejected"),
                        ],
                        default="requested",
                        max_length=20,
                    ),
                ),
                (
                    "supporting_documents",
                    models.FileField(
                        blank=True, upload_to="petty_cash/replenishment_docs/"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_replenishments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "disbursed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="disbursed_replenishments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "fund",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replenishments",
                        to="petty_cash.pettycashfund",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="requested_replenishments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
