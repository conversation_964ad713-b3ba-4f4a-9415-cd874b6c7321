from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
import csv

from .models import (
    CoCurricularActivity, StudentActivity, ActivityCategory, ActivitySchedule,
    Achievement, ActivityCoach, ActivityFeeStructure, ActivityFeePayment, ActivityFeeDiscount
)
from .forms import (
    CoCurricularActivityForm, StudentActivityForm, ActivityScheduleForm,
    AchievementForm, ActivityCoachForm, ActivityFeePaymentForm,
    ActivityFeeDiscountForm, ActivitySearchForm
)
from apps.students.models import Student

@login_required
def activities_dashboard(request):
    """Activities management dashboard with statistics and recent activities."""
    context = {
        'total_activities': CoCurricularActivity.objects.count(),
        'active_activities': CoCurricularActivity.objects.filter(status='active').count(),
        'total_participants': StudentActivity.objects.filter(status='active').count(),
        'total_achievements': Achievement.objects.count(),
        'total_coaches': ActivityCoach.objects.filter(status='active').count(),
        'total_revenue': ActivityFeePayment.objects.filter(status='completed').aggregate(
            total=Sum('amount')
        )['total'] or 0,
        'recent_enrollments': StudentActivity.objects.select_related(
            'student', 'activity'
        ).order_by('-join_date')[:10],
        'recent_achievements': Achievement.objects.select_related(
            'student_activity__student', 'student_activity__activity'
        ).order_by('-date_achieved')[:10],
        'activity_categories': CoCurricularActivity.objects.values('category').annotate(
            count=Count('id')
        ).order_by('-count')[:5],
        'top_activities': CoCurricularActivity.objects.annotate(
            participant_count=Count('studentactivity', filter=Q(studentactivity__status='active'))
        ).order_by('-participant_count')[:5],
    }
    return render(request, 'activities/dashboard.html', context)

# Activity Management Views
@login_required
def activities_list(request):
    """List all activities with search and filter functionality."""
    form = ActivitySearchForm(request.GET)
    activities = CoCurricularActivity.objects.annotate(
        participant_count=Count('studentactivity', filter=Q(studentactivity__status='active'))
    ).all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        category = form.cleaned_data.get('category')
        status = form.cleaned_data.get('status')

        if search_query:
            activities = activities.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(teacher_in_charge__full_name__icontains=search_query)
            )

        if category:
            activities = activities.filter(category=category)

        if status:
            activities = activities.filter(status=status)

    activities = activities.order_by('name')

    paginator = Paginator(activities, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'activities': page_obj,
    }
    return render(request, 'activities/activities_list.html', context)

@login_required
def activity_detail(request, activity_id):
    """Display detailed information about an activity."""
    activity = get_object_or_404(CoCurricularActivity, id=activity_id)
    participants = StudentActivity.objects.filter(
        activity=activity, status='active'
    ).select_related('student')

    schedules = ActivitySchedule.objects.filter(activity=activity, is_active=True)
    coaches = ActivityCoach.objects.filter(activity=activity, is_active=True)
    achievements = Achievement.objects.filter(
        student_activity__activity=activity
    ).select_related('student_activity__student').order_by('-date_achieved')[:10]

    fee_structures = ActivityFeeStructure.objects.filter(activity=activity, is_active=True)

    context = {
        'activity': activity,
        'participants': participants,
        'schedules': schedules,
        'coaches': coaches,
        'achievements': achievements,
        'fee_structures': fee_structures,
        'total_participants': participants.count(),
    }
    return render(request, 'activities/activity_detail.html', context)

@login_required
def add_activity(request):
    """Add a new co-curricular activity."""
    if request.method == 'POST':
        form = CoCurricularActivityForm(request.POST)
        if form.is_valid():
            activity = form.save()
            messages.success(request, f'Activity "{activity.name}" has been added successfully.')
            return redirect('activities:activity_detail', activity_id=activity.id)
    else:
        form = CoCurricularActivityForm()

    context = {'form': form}
    return render(request, 'activities/add_activity.html', context)

@login_required
def edit_activity(request, activity_id):
    """Edit an existing activity."""
    activity = get_object_or_404(CoCurricularActivity, id=activity_id)

    if request.method == 'POST':
        form = CoCurricularActivityForm(request.POST, instance=activity)
        if form.is_valid():
            form.save()
            messages.success(request, f'Activity "{activity.name}" has been updated successfully.')
            return redirect('activities:activity_detail', activity_id=activity.id)
    else:
        form = CoCurricularActivityForm(instance=activity)

    context = {'form': form, 'activity': activity}
    return render(request, 'activities/edit_activity.html', context)

# Student Participation Management
@login_required
def participants_list(request):
    """List all student activity participations."""
    participants = StudentActivity.objects.select_related(
        'student', 'activity'
    ).order_by('-join_date')

    paginator = Paginator(participants, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'participants': page_obj,
    }
    return render(request, 'activities/participants_list.html', context)

@login_required
def participant_detail(request, participant_id):
    """Display detailed information about a student's activity participation."""
    participant = get_object_or_404(StudentActivity, id=participant_id)
    achievements = Achievement.objects.filter(student_activity=participant).order_by('-date_achieved')
    fee_payments = ActivityFeePayment.objects.filter(student_activity=participant).order_by('-payment_date')

    context = {
        'participant': participant,
        'achievements': achievements,
        'fee_payments': fee_payments,
    }
    return render(request, 'activities/participant_detail.html', context)
