from django.contrib import admin
from .models import Petty<PERSON>ash, PettyCashHistory

@admin.register(PettyCash)
class PettyCashAdmin(admin.ModelAdmin):
    list_display = ('id', 'received_by', 'approved_by', 'year', 'month', 'date', 'time', 'paid', 'received_type', 'purpose', 'receipt_number', '_status')
    search_fields = ('id', 'received_by', 'approved_by', 'month', 'receipt_number')
    list_filter = ('year', 'month', 'received_type', '_status')
    ordering = ('-year', '-month', 'id')

@admin.register(PettyCashHistory)
class PettyCashHistoryAdmin(admin.ModelAdmin):
    list_display = ('id', '_desc', 'received_by', 'approved_by', 'year', 'month', 'date', 'time', 'amount', 'total_paid', 'invoice_number', 'received_type', 'payment_method', '_status')
    search_fields = ('id', '_desc', 'received_by', 'approved_by', 'month', 'invoice_number')
    list_filter = ('year', 'month', 'received_type', 'payment_method', '_status')
    ordering = ('-year', '-month', 'id')
