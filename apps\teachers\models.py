from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import date
import uuid
import os

User = get_user_model()

def teacher_photo_upload_path(instance, filename):
    """Generate upload path for teacher photos"""
    ext = filename.split('.')[-1]
    filename = f"{instance.employee_id}_{uuid.uuid4().hex[:8]}.{ext}"
    return os.path.join('teachers', 'photos', filename)

def teacher_document_upload_path(instance, filename):
    """Generate upload path for teacher documents"""
    ext = filename.split('.')[-1]
    filename = f"{instance.teacher.employee_id}_{instance.document_type}_{uuid.uuid4().hex[:8]}.{ext}"
    return os.path.join('teachers', 'documents', filename)

class TeacherQualification(models.Model):
    """
    Teacher educational qualifications and certifications
    """
    QUALIFICATION_TYPE_CHOICES = [
        ('certificate', 'Certificate'),
        ('diploma', 'Diploma'),
        ('degree', 'Bachelor\'s Degree'),
        ('masters', 'Master\'s Degree'),
        ('phd', 'PhD'),
        ('professional', 'Professional Certification'),
        ('training', 'Training Course'),
    ]

    VERIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending Verification'),
        ('verified', 'Verified'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey('Teacher', on_delete=models.CASCADE, related_name='qualifications')
    qualification_type = models.CharField(max_length=20, choices=QUALIFICATION_TYPE_CHOICES)
    qualification_name = models.CharField(max_length=255)
    institution = models.CharField(max_length=255)
    field_of_study = models.CharField(max_length=255, null=True, blank=True)
    grade_obtained = models.CharField(max_length=50, null=True, blank=True)
    year_obtained = models.PositiveIntegerField()
    certificate_number = models.CharField(max_length=100, null=True, blank=True)
    verification_status = models.CharField(max_length=20, choices=VERIFICATION_STATUS_CHOICES, default='pending')
    expiry_date = models.DateField(null=True, blank=True)
    is_teaching_qualification = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Teacher Qualification"
        verbose_name_plural = "Teacher Qualifications"
        ordering = ['-year_obtained']

    def __str__(self):
        return f"{self.teacher.full_name} - {self.qualification_name}"

class Teacher(models.Model):
    """
    Enhanced teacher model with comprehensive personal, professional, and employment information.
    """
    EMPLOYMENT_TYPE_CHOICES = [
        ('TSC', 'TSC'),
        ('BOM', 'BOM'),
        ('contract', 'Contract'),
        ('volunteer', 'Volunteer'),
        ('intern', 'Intern'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('on_leave', 'On Leave'),
        ('suspended', 'Suspended'),
        ('terminated', 'Terminated'),
        ('retired', 'Retired'),
        ('transferred', 'Transferred'),
    ]

    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
    ]

    MARITAL_STATUS_CHOICES = [
        ('single', 'Single'),
        ('married', 'Married'),
        ('divorced', 'Divorced'),
        ('widowed', 'Widowed'),
    ]

    EMPLOYMENT_STATUS_CHOICES = [
        ('probation', 'Probation'),
        ('confirmed', 'Confirmed'),
        ('contract', 'Contract'),
        ('temporary', 'Temporary'),
    ]

    # Basic Information
    id = models.AutoField(primary_key=True)
    employee_id = models.CharField(max_length=20, unique=True, null=True, blank=True, help_text="Unique employee identifier")

    # Personal Details
    first_name = models.CharField(max_length=100, default='Unknown')
    middle_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, default='Teacher')
    full_name = models.CharField(max_length=255, editable=False)  # Auto-generated
    preferred_name = models.CharField(max_length=100, null=True, blank=True)
    i_name = models.CharField(max_length=255, null=True, blank=True, help_text="Name abbreviation")  # Legacy field
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, default='Male')
    date_of_birth = models.DateField(default=date.today)
    place_of_birth = models.CharField(max_length=100, null=True, blank=True)
    marital_status = models.CharField(max_length=20, choices=MARITAL_STATUS_CHOICES, default='single')

    # Identification
    national_id = models.CharField(max_length=20, unique=True, null=True, blank=True)
    passport_number = models.CharField(max_length=20, null=True, blank=True)
    kra_pin = models.CharField(max_length=20, null=True, blank=True, help_text="KRA PIN number")
    nhif_number = models.CharField(max_length=20, null=True, blank=True)
    nssf_number = models.CharField(max_length=20, null=True, blank=True)

    # Contact Information
    phone = models.CharField(max_length=20, default='0700000000')
    alternative_phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(unique=True, null=True, blank=True)
    alternative_email = models.EmailField(null=True, blank=True)

    # Address Information
    address = models.CharField(max_length=255, default='Address not provided')
    city = models.CharField(max_length=100, null=True, blank=True)
    county = models.CharField(max_length=100, null=True, blank=True)
    postal_address = models.CharField(max_length=255, null=True, blank=True)
    postal_code = models.CharField(max_length=10, null=True, blank=True)

    # Emergency Contact
    emergency_contact_name = models.CharField(max_length=255, null=True, blank=True)
    emergency_contact_relationship = models.CharField(max_length=100, null=True, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, null=True, blank=True)
    emergency_contact_address = models.TextField(null=True, blank=True)

    # Employment Information
    employment_type = models.CharField(max_length=50, choices=EMPLOYMENT_TYPE_CHOICES, default='TSC')
    employment_status = models.CharField(max_length=20, choices=EMPLOYMENT_STATUS_CHOICES, default='probation')
    employment_date = models.DateField(default=date.today, help_text="Date of first employment")
    confirmation_date = models.DateField(null=True, blank=True, help_text="Date of confirmation")
    probation_end_date = models.DateField(null=True, blank=True)
    contract_start_date = models.DateField(null=True, blank=True)
    contract_end_date = models.DateField(null=True, blank=True)

    # Professional Information
    tsc_number = models.CharField(max_length=50, null=True, blank=True, help_text="TSC registration number")
    teacher_registration_number = models.CharField(max_length=50, null=True, blank=True)
    professional_qualification = models.CharField(max_length=255, null=True, blank=True)
    teaching_experience_years = models.PositiveIntegerField(default=0)
    previous_schools = models.TextField(null=True, blank=True, help_text="Previous teaching experience")

    # Current Assignment
    department = models.ForeignKey('academics.Department', on_delete=models.SET_NULL, null=True, blank=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    is_department_head = models.BooleanField(default=False)
    is_class_teacher = models.BooleanField(default=False)
    current_class = models.ForeignKey('academics.ClassRoom', on_delete=models.SET_NULL, null=True, blank=True, related_name='current_class_teacher')

    # Salary and Benefits
    basic_salary = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    house_allowance = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    transport_allowance = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    other_allowances = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    total_salary = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True, editable=False)

    # Performance and Workload
    current_teaching_load = models.PositiveIntegerField(default=0, help_text="Number of lessons per week")
    maximum_teaching_load = models.PositiveIntegerField(default=30, help_text="Maximum lessons per week")
    performance_rating = models.DecimalField(max_digits=3, decimal_places=1, null=True, blank=True, help_text="Performance rating out of 5")
    last_evaluation_date = models.DateField(null=True, blank=True)

    # Status and Registration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    reg_date = models.DateField(default=date.today)  # Legacy field
    index_number = models.BigIntegerField(unique=True, null=True, blank=True)  # Legacy field

    # Photo and Documents
    photo = models.ImageField(upload_to=teacher_photo_upload_path, null=True, blank=True)
    image_name = models.CharField(max_length=255, null=True, blank=True)  # Legacy field

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='teachers_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='teachers_updated')

    class Meta:
        verbose_name = "Teacher"
        verbose_name_plural = "Teachers"
        ordering = ['last_name', 'first_name']

    def save(self, *args, **kwargs):
        # Auto-generate full name
        if self.first_name and self.last_name:
            if self.middle_name:
                self.full_name = f"{self.first_name} {self.middle_name} {self.last_name}"
            else:
                self.full_name = f"{self.first_name} {self.last_name}"

        # Auto-generate employee ID if not provided
        if not self.employee_id:
            self.employee_id = self.generate_employee_id()

        # Calculate total salary
        if self.basic_salary:
            total = self.basic_salary
            if self.house_allowance:
                total += self.house_allowance
            if self.transport_allowance:
                total += self.transport_allowance
            if self.other_allowances:
                total += self.other_allowances
            self.total_salary = total

        super().save(*args, **kwargs)

    def generate_employee_id(self):
        """Generate unique employee ID"""
        import random
        import string
        while True:
            emp_id = f"EMP{random.randint(1000, 9999)}"
            if not Teacher.objects.filter(employee_id=emp_id).exists():
                return emp_id

    def get_age(self):
        """Calculate teacher's age"""
        from datetime import date
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None

    def get_years_of_service(self):
        """Calculate years of service"""
        from datetime import date
        if self.employment_date:
            today = date.today()
            return today.year - self.employment_date.year
        return 0

    def get_workload_percentage(self):
        """Calculate workload as percentage of maximum"""
        if self.maximum_teaching_load > 0:
            return (self.current_teaching_load / self.maximum_teaching_load) * 100
        return 0

    def is_overloaded(self):
        """Check if teacher is overloaded"""
        return self.current_teaching_load > self.maximum_teaching_load

    def __str__(self):
        return self.full_name or f"{self.first_name} {self.last_name}"


class TeacherDocument(models.Model):
    """
    Teacher document management system
    """
    DOCUMENT_TYPE_CHOICES = [
        ('cv', 'Curriculum Vitae'),
        ('certificate', 'Educational Certificate'),
        ('id_copy', 'ID Copy'),
        ('passport_copy', 'Passport Copy'),
        ('tsc_certificate', 'TSC Certificate'),
        ('kra_pin', 'KRA PIN Certificate'),
        ('nhif_card', 'NHIF Card'),
        ('nssf_card', 'NSSF Card'),
        ('contract', 'Employment Contract'),
        ('recommendation', 'Recommendation Letter'),
        ('medical_report', 'Medical Report'),
        ('police_clearance', 'Police Clearance'),
        ('other', 'Other Document'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPE_CHOICES)
    document_name = models.CharField(max_length=255)
    document_file = models.FileField(upload_to=teacher_document_upload_path)
    description = models.TextField(null=True, blank=True)
    upload_date = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_teacher_documents')
    verification_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Teacher Document"
        verbose_name_plural = "Teacher Documents"
        ordering = ['-upload_date']

    def __str__(self):
        return f"{self.teacher.full_name} - {self.get_document_type_display()}"


class TeacherSubjectSpecialization(models.Model):
    """
    Teacher subject specialization and expertise tracking
    """
    EXPERTISE_LEVEL_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ]

    CERTIFICATION_STATUS_CHOICES = [
        ('certified', 'Certified'),
        ('not_certified', 'Not Certified'),
        ('pending', 'Pending Certification'),
        ('expired', 'Expired'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='subject_specializations')
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    expertise_level = models.CharField(max_length=20, choices=EXPERTISE_LEVEL_CHOICES, default='intermediate')
    certification_status = models.CharField(max_length=20, choices=CERTIFICATION_STATUS_CHOICES, default='not_certified')
    years_of_experience = models.PositiveIntegerField(default=0)
    certification_date = models.DateField(null=True, blank=True)
    certification_body = models.CharField(max_length=255, null=True, blank=True)
    certification_number = models.CharField(max_length=100, null=True, blank=True)
    can_teach_grade_levels = models.CharField(max_length=255, null=True, blank=True, help_text="Comma-separated grade levels")
    notes = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Teacher Subject Specialization"
        verbose_name_plural = "Teacher Subject Specializations"
        unique_together = ['teacher', 'subject']
        ordering = ['subject__name']

    def __str__(self):
        return f"{self.teacher.full_name} - {self.subject.name} ({self.expertise_level})"


class TeacherAssignment(models.Model):
    """
    Teacher assignment to subjects and classes
    """
    ASSIGNMENT_TYPE_CHOICES = [
        ('primary', 'Primary Assignment'),
        ('secondary', 'Secondary Assignment'),
        ('substitute', 'Substitute Assignment'),
        ('temporary', 'Temporary Assignment'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='assignments')
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    section = models.CharField(max_length=10, null=True, blank=True)
    assignment_type = models.CharField(max_length=20, choices=ASSIGNMENT_TYPE_CHOICES, default='primary')
    academic_year = models.PositiveIntegerField(default=2024)
    term = models.PositiveIntegerField(default=1)
    lessons_per_week = models.PositiveIntegerField(default=1)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = "Teacher Assignment"
        verbose_name_plural = "Teacher Assignments"
        unique_together = ['teacher', 'subject', 'grade', 'academic_year', 'term']
        ordering = ['-academic_year', '-term', 'subject__name']

    def __str__(self):
        return f"{self.teacher.full_name} - {self.subject.name} ({self.grade.name})"


class TeacherPerformanceEvaluation(models.Model):
    """
    Teacher performance evaluation and tracking
    """
    EVALUATION_TYPE_CHOICES = [
        ('annual', 'Annual Evaluation'),
        ('mid_year', 'Mid-Year Review'),
        ('probation', 'Probation Review'),
        ('promotion', 'Promotion Review'),
        ('disciplinary', 'Disciplinary Review'),
    ]

    PERFORMANCE_RATING_CHOICES = [
        (1, 'Poor'),
        (2, 'Below Average'),
        (3, 'Average'),
        (4, 'Good'),
        (5, 'Excellent'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='performance_evaluations')
    evaluation_type = models.CharField(max_length=20, choices=EVALUATION_TYPE_CHOICES, default='annual')
    evaluation_period_start = models.DateField()
    evaluation_period_end = models.DateField()
    evaluation_date = models.DateField()
    evaluator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Performance Metrics
    teaching_effectiveness = models.PositiveIntegerField(choices=PERFORMANCE_RATING_CHOICES, null=True, blank=True)
    classroom_management = models.PositiveIntegerField(choices=PERFORMANCE_RATING_CHOICES, null=True, blank=True)
    student_engagement = models.PositiveIntegerField(choices=PERFORMANCE_RATING_CHOICES, null=True, blank=True)
    professional_development = models.PositiveIntegerField(choices=PERFORMANCE_RATING_CHOICES, null=True, blank=True)
    collaboration = models.PositiveIntegerField(choices=PERFORMANCE_RATING_CHOICES, null=True, blank=True)
    punctuality = models.PositiveIntegerField(choices=PERFORMANCE_RATING_CHOICES, null=True, blank=True)
    overall_rating = models.DecimalField(max_digits=3, decimal_places=1, null=True, blank=True)

    # Comments and Goals
    strengths = models.TextField(null=True, blank=True)
    areas_for_improvement = models.TextField(null=True, blank=True)
    goals_for_next_period = models.TextField(null=True, blank=True)
    evaluator_comments = models.TextField(null=True, blank=True)
    teacher_comments = models.TextField(null=True, blank=True)

    # Status
    is_approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_evaluations')
    approval_date = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Teacher Performance Evaluation"
        verbose_name_plural = "Teacher Performance Evaluations"
        ordering = ['-evaluation_date']

    def save(self, *args, **kwargs):
        # Calculate overall rating
        ratings = [
            self.teaching_effectiveness,
            self.classroom_management,
            self.student_engagement,
            self.professional_development,
            self.collaboration,
            self.punctuality,
        ]
        valid_ratings = [r for r in ratings if r is not None]
        if valid_ratings:
            self.overall_rating = sum(valid_ratings) / len(valid_ratings)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.teacher.full_name} - {self.evaluation_type} ({self.evaluation_date})"


class TeacherEmploymentHistory(models.Model):
    """
    Teacher employment history and status changes
    """
    STATUS_CHANGE_CHOICES = [
        ('hired', 'Hired'),
        ('confirmed', 'Confirmed'),
        ('promoted', 'Promoted'),
        ('transferred', 'Transferred'),
        ('suspended', 'Suspended'),
        ('terminated', 'Terminated'),
        ('resigned', 'Resigned'),
        ('retired', 'Retired'),
        ('leave_start', 'Leave Started'),
        ('leave_end', 'Leave Ended'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='employment_history')
    status_change = models.CharField(max_length=20, choices=STATUS_CHANGE_CHOICES)
    effective_date = models.DateField()
    previous_status = models.CharField(max_length=20, null=True, blank=True)
    new_status = models.CharField(max_length=20)
    reason = models.TextField(null=True, blank=True)
    reference_number = models.CharField(max_length=100, null=True, blank=True)
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Teacher Employment History"
        verbose_name_plural = "Teacher Employment History"
        ordering = ['-effective_date']

    def __str__(self):
        return f"{self.teacher.full_name} - {self.status_change} ({self.effective_date})"


class TeacherDepartmentTransfer(models.Model):
    """
    Teacher department transfer tracking
    """
    TRANSFER_TYPE_CHOICES = [
        ('promotion', 'Promotion Transfer'),
        ('lateral', 'Lateral Transfer'),
        ('disciplinary', 'Disciplinary Transfer'),
        ('request', 'Request Transfer'),
        ('administrative', 'Administrative Transfer'),
    ]

    TRANSFER_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]

    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='department_transfers')
    from_department = models.ForeignKey('academics.Department', on_delete=models.SET_NULL, null=True, blank=True, related_name='transfers_from')
    to_department = models.ForeignKey('academics.Department', on_delete=models.CASCADE, related_name='transfers_to')
    transfer_type = models.CharField(max_length=20, choices=TRANSFER_TYPE_CHOICES, default='lateral')
    transfer_status = models.CharField(max_length=20, choices=TRANSFER_STATUS_CHOICES, default='pending')
    request_date = models.DateField()
    effective_date = models.DateField(null=True, blank=True)
    reason = models.TextField()
    requested_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='requested_transfers')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_transfers')
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Teacher Department Transfer"
        verbose_name_plural = "Teacher Department Transfers"
        ordering = ['-request_date']

    def __str__(self):
        return f"{self.teacher.full_name} - {self.from_department} to {self.to_department}"


class TeacherWorkload(models.Model):
    """
    Teacher workload tracking and management
    """
    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='workloads')
    academic_year = models.PositiveIntegerField(default=2024)
    term = models.PositiveIntegerField(default=1)

    # Teaching Load
    total_lessons_per_week = models.PositiveIntegerField(default=0)
    total_contact_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    number_of_subjects = models.PositiveIntegerField(default=0)
    number_of_classes = models.PositiveIntegerField(default=0)

    # Additional Responsibilities
    is_class_teacher = models.BooleanField(default=False)
    is_department_head = models.BooleanField(default=False)
    is_games_master = models.BooleanField(default=False)
    is_club_patron = models.BooleanField(default=False)
    other_responsibilities = models.TextField(null=True, blank=True)

    # Workload Analysis
    workload_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Percentage of maximum workload")
    is_overloaded = models.BooleanField(default=False)
    overtime_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Dates
    calculation_date = models.DateField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Teacher Workload"
        verbose_name_plural = "Teacher Workloads"
        unique_together = ['teacher', 'academic_year', 'term']
        ordering = ['-academic_year', '-term']

    def save(self, *args, **kwargs):
        # Calculate workload percentage
        if self.teacher.maximum_teaching_load > 0:
            self.workload_percentage = (self.total_lessons_per_week / self.teacher.maximum_teaching_load) * 100
            self.is_overloaded = self.total_lessons_per_week > self.teacher.maximum_teaching_load
            if self.is_overloaded:
                self.overtime_hours = self.total_lessons_per_week - self.teacher.maximum_teaching_load
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.teacher.full_name} - {self.academic_year} Term {self.term} ({self.total_lessons_per_week} lessons)"
