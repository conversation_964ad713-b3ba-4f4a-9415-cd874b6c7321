from django.urls import path
from . import views

app_name = 'communication'

urlpatterns = [
    # Dashboard
    path('', views.communication_dashboard, name='dashboard'),

    # Notifications
    path('notifications/', views.notifications_list, name='notifications_list'),
    path('notifications/<int:pk>/', views.notification_detail, name='notification_detail'),
    path('notifications/create/', views.create_notification, name='create_notification'),
    path('notifications/preferences/', views.notification_preferences, name='notification_preferences'),
    path('chat/history/', views.chat_history, name='chat_history'),
    path('chat/online/', views.online_chat, name='online_chat'),
    path('chat/group/<int:group_id>/', views.group_chat, name='group_chat'),
    path('feedback/', views.feedback, name='feedback'),
    path('feedback/success/', views.feedback_success, name='feedback_success'),

    # Messaging
    path('compose/', views.compose_message, name='compose_message'),

    # API endpoints
    path('api/notifications/', views.api_notifications, name='api_notifications'),
]