# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("students", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Hostel",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("capacity", models.IntegerField()),
                (
                    "gender",
                    models.Char<PERSON>ield(
                        choices=[
                            ("boys", "Boys"),
                            ("girls", "Girls"),
                            ("mixed", "Mixed"),
                        ],
                        max_length=50,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "warden_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "warden_contact",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("building", models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ("floor", models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                (
                    "status",
                    models.Char<PERSON><PERSON>(
                        choices=[
                            ("active", "Active"),
                            ("maintenance", "Maintenance"),
                            ("inactive", "Inactive"),
                        ],
                        default="active",
                        max_length=50,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HostelRoom",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("room_number", models.CharField(max_length=20)),
                ("capacity", models.IntegerField()),
                ("current_occupancy", models.IntegerField(default=0)),
                (
                    "room_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("single", "Single"),
                            ("double", "Double"),
                            ("dormitory", "Dormitory"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("floor", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("full", "Full"),
                            ("maintenance", "Maintenance"),
                        ],
                        default="available",
                        max_length=50,
                    ),
                ),
                (
                    "hostel",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="hostel.hostel"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HostelAllocation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("bed_number", models.IntegerField(blank=True, null=True)),
                ("allocation_date", models.DateField()),
                ("release_date", models.DateField(blank=True, null=True)),
                ("fee", models.DecimalField(decimal_places=2, max_digits=11)),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("inactive", "Inactive")],
                        default="active",
                        max_length=50,
                    ),
                ),
                ("allocated_by", models.IntegerField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "hostel",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="hostel.hostel"
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="hostel.hostelroom",
                    ),
                ),
            ],
        ),
    ]
