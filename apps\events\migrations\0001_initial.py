# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="EventCategory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.Char<PERSON>ield(max_length=255)),
                ("color", models.CharField(default="#3788d8", max_length=7)),
            ],
        ),
        migrations.CreateModel(
            name="Event",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                ("note", models.Char<PERSON>ield(max_length=255)),
                ("color", models.CharField(max_length=255)),
                ("grade_id", models.CharField(max_length=255)),
                ("create_by", models.BigIntegerField()),
                ("creator_type", models.Char<PERSON>ield(max_length=255)),
                ("start_date_time", models.DateTimeField()),
                ("end_date_time", models.DateTimeField()),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                ("is_all_day", models.BooleanField(default=False)),
                ("notification_sent", models.BooleanField(default=False)),
                ("year", models.IntegerField()),
                ("month", models.IntegerField()),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="events.eventcategory",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EventCategoryType",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="events.eventcategory",
                    ),
                ),
            ],
        ),
    ]
