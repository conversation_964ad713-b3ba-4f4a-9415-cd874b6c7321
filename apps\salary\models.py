from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta
import uuid

# Enhanced Salary Management System

class TeacherSalary(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('Bank', 'Bank'),
        ('Cash', 'Cash'),
        ('M-Pesa', 'M-Pesa'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    month = models.CharField(max_length=255)
    year = models.PositiveIntegerField()
    date = models.DateField()
    basic_salary = models.DecimalField(max_digits=11, decimal_places=2)
    allowances = models.DecimalField(max_digits=11, decimal_places=2, default=0)
    deductions = models.DecimalField(max_digits=11, decimal_places=2, default=0)
    paid = models.DecimalField(max_digits=11, decimal_places=2)
    payment_method = models.Char<PERSON>ield(max_length=50, default='Bank', choices=PAYMENT_METHOD_CHOICES)
    bank_name = models.CharField(max_length=100, null=True, blank=True)
    account_number = models.CharField(max_length=50, null=True, blank=True)
    transaction_id = models.CharField(max_length=100, null=True, blank=True)
    _status = models.CharField(max_length=255)

    def __str__(self):
        return f"Salary - {self.index_number}"

class TeacherSalaryHistory(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('Bank', 'Bank'),
        ('Cash', 'Cash'),
        ('M-Pesa', 'M-Pesa'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    grade_id = models.IntegerField()
    subject_id = models.IntegerField()
    subject_fee = models.DecimalField(max_digits=11, decimal_places=2)
    student_count = models.IntegerField()
    hall_charge = models.IntegerField()
    subtotal = models.DecimalField(max_digits=11, decimal_places=2)
    paid = models.DecimalField(max_digits=11, decimal_places=2)
    _status = models.CharField(max_length=255)
    month = models.CharField(max_length=255)
    year = models.PositiveIntegerField()
    date = models.DateField()
    invoice_number = models.IntegerField()
    payment_method = models.CharField(max_length=50, default='Bank', choices=PAYMENT_METHOD_CHOICES)
    transaction_id = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"Salary History - {self.index_number}"

# Enhanced Salary Management Models

class SalaryGrade(models.Model):
    """Salary grades/scales for different positions"""
    GRADE_TYPES = [
        ('teaching', 'Teaching Staff'),
        ('administrative', 'Administrative Staff'),
        ('support', 'Support Staff'),
        ('management', 'Management'),
    ]

    id = models.AutoField(primary_key=True)
    grade_name = models.CharField(max_length=100, unique=True)
    grade_type = models.CharField(max_length=20, choices=GRADE_TYPES)
    minimum_salary = models.DecimalField(max_digits=12, decimal_places=2)
    maximum_salary = models.DecimalField(max_digits=12, decimal_places=2)
    annual_increment = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['grade_type', 'minimum_salary']

    def __str__(self):
        return f"{self.grade_name} ({self.get_grade_type_display()})"

class SalaryComponent(models.Model):
    """Salary components (allowances, deductions, etc.)"""
    COMPONENT_TYPES = [
        ('allowance', 'Allowance'),
        ('deduction', 'Deduction'),
        ('bonus', 'Bonus'),
        ('overtime', 'Overtime'),
    ]

    CALCULATION_METHODS = [
        ('fixed', 'Fixed Amount'),
        ('percentage', 'Percentage of Basic Salary'),
        ('hourly', 'Hourly Rate'),
        ('daily', 'Daily Rate'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    component_type = models.CharField(max_length=20, choices=COMPONENT_TYPES)
    calculation_method = models.CharField(max_length=20, choices=CALCULATION_METHODS)

    # Calculation values
    fixed_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    percentage_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    hourly_rate = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    daily_rate = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)

    # Applicability
    is_mandatory = models.BooleanField(default=False)
    is_taxable = models.BooleanField(default=True)
    applies_to_overtime = models.BooleanField(default=False)

    # Status
    is_active = models.BooleanField(default=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_amount(self, basic_salary=None, hours=None, days=None):
        """Calculate component amount based on calculation method"""
        if self.calculation_method == 'fixed':
            return self.fixed_amount or Decimal('0.00')
        elif self.calculation_method == 'percentage' and basic_salary:
            return (basic_salary * self.percentage_rate) / 100
        elif self.calculation_method == 'hourly' and hours:
            return self.hourly_rate * hours
        elif self.calculation_method == 'daily' and days:
            return self.daily_rate * days
        return Decimal('0.00')

    def __str__(self):
        return f"{self.name} ({self.get_component_type_display()})"

class EmployeeSalaryStructure(models.Model):
    """Individual employee salary structure"""
    id = models.AutoField(primary_key=True)
    employee = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE, related_name='salary_structures')
    salary_grade = models.ForeignKey(SalaryGrade, on_delete=models.CASCADE)

    # Basic salary details
    basic_salary = models.DecimalField(max_digits=12, decimal_places=2)
    effective_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)

    # Bank details
    bank_name = models.CharField(max_length=200, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    account_name = models.CharField(max_length=200, blank=True)
    bank_branch = models.CharField(max_length=200, blank=True)

    # Tax information
    tax_number = models.CharField(max_length=50, blank=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Status
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-effective_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.salary_grade.grade_name}"

class EmployeeSalaryComponent(models.Model):
    """Employee-specific salary components"""
    id = models.AutoField(primary_key=True)
    salary_structure = models.ForeignKey(EmployeeSalaryStructure, on_delete=models.CASCADE, related_name='components')
    component = models.ForeignKey(SalaryComponent, on_delete=models.CASCADE)

    # Override values
    custom_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    custom_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    custom_rate = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)

    # Applicability
    is_active = models.BooleanField(default=True)
    effective_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def get_calculated_amount(self, basic_salary=None, hours=None, days=None):
        """Get calculated amount using custom values if available"""
        if self.custom_amount:
            return self.custom_amount
        elif self.custom_percentage and basic_salary:
            return (basic_salary * self.custom_percentage) / 100
        elif self.custom_rate and (hours or days):
            if self.component.calculation_method == 'hourly' and hours:
                return self.custom_rate * hours
            elif self.component.calculation_method == 'daily' and days:
                return self.custom_rate * days

        # Fall back to component's default calculation
        return self.component.calculate_amount(basic_salary, hours, days)

    def __str__(self):
        return f"{self.salary_structure.employee.full_name} - {self.component.name}"

class PayrollPeriod(models.Model):
    """Payroll processing periods"""
    PERIOD_TYPES = [
        ('monthly', 'Monthly'),
        ('bi_weekly', 'Bi-Weekly'),
        ('weekly', 'Weekly'),
        ('quarterly', 'Quarterly'),
    ]

    PAYROLL_STATUSES = [
        ('draft', 'Draft'),
        ('processing', 'Processing'),
        ('approved', 'Approved'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    period_name = models.CharField(max_length=200)
    period_type = models.CharField(max_length=20, choices=PERIOD_TYPES)
    start_date = models.DateField()
    end_date = models.DateField()
    pay_date = models.DateField()

    # Processing details
    status = models.CharField(max_length=20, choices=PAYROLL_STATUSES, default='draft')
    total_employees = models.IntegerField(default=0)
    total_gross_salary = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_deductions = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_net_salary = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Processing tracking
    processed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_payrolls')
    approved_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-start_date']
        unique_together = ['period_type', 'start_date', 'end_date']

    def __str__(self):
        return f"{self.period_name} ({self.start_date} - {self.end_date})"

class EnhancedPayroll(models.Model):
    """Enhanced payroll records"""
    PAYROLL_STATUSES = [
        ('draft', 'Draft'),
        ('calculated', 'Calculated'),
        ('approved', 'Approved'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    payroll_reference = models.CharField(max_length=100, unique=True)
    employee = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE, related_name='enhanced_payrolls')
    payroll_period = models.ForeignKey(PayrollPeriod, on_delete=models.CASCADE, related_name='payrolls')
    salary_structure = models.ForeignKey(EmployeeSalaryStructure, on_delete=models.CASCADE)

    # Salary calculations
    basic_salary = models.DecimalField(max_digits=12, decimal_places=2)
    total_allowances = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_deductions = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    gross_salary = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_salary = models.DecimalField(max_digits=12, decimal_places=2)

    # Working details
    working_days = models.IntegerField(default=0)
    days_worked = models.IntegerField(default=0)
    overtime_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    leave_days = models.IntegerField(default=0)

    # Payment details
    payment_method = models.CharField(max_length=50, default='Bank Transfer', choices=TeacherSalary.PAYMENT_METHOD_CHOICES)
    bank_name = models.CharField(max_length=200, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    transaction_reference = models.CharField(max_length=200, blank=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=PAYROLL_STATUSES, default='draft')
    calculated_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    paid_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-payroll_period__start_date', 'employee__full_name']
        unique_together = ['employee', 'payroll_period']

    def save(self, *args, **kwargs):
        if not self.payroll_reference:
            self.payroll_reference = self.generate_payroll_reference()

        # Calculate totals
        self.gross_salary = self.basic_salary + self.total_allowances
        self.net_salary = self.gross_salary - self.total_deductions - self.tax_amount

        super().save(*args, **kwargs)

    def generate_payroll_reference(self):
        """Generate unique payroll reference"""
        year = self.payroll_period.start_date.year
        month = self.payroll_period.start_date.month
        count = EnhancedPayroll.objects.filter(
            payroll_period__start_date__year=year,
            payroll_period__start_date__month=month
        ).count() + 1
        return f"PAY-{year}{month:02d}-{count:06d}"

    def __str__(self):
        return f"{self.payroll_reference} - {self.employee.full_name}"

class PayrollComponent(models.Model):
    """Individual payroll component records"""
    id = models.AutoField(primary_key=True)
    payroll = models.ForeignKey(EnhancedPayroll, on_delete=models.CASCADE, related_name='components')
    component = models.ForeignKey(SalaryComponent, on_delete=models.CASCADE)

    # Calculation details
    base_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    calculated_amount = models.DecimalField(max_digits=12, decimal_places=2)
    units = models.DecimalField(max_digits=8, decimal_places=2, default=0)  # hours, days, etc.
    rate = models.DecimalField(max_digits=8, decimal_places=2, default=0)

    # Metadata
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.payroll.payroll_reference} - {self.component.name}"

class PaySlip(models.Model):
    """Pay slip generation and management"""
    PAYSLIP_STATUSES = [
        ('generated', 'Generated'),
        ('sent', 'Sent'),
        ('viewed', 'Viewed'),
        ('downloaded', 'Downloaded'),
    ]

    id = models.AutoField(primary_key=True)
    payroll = models.OneToOneField(EnhancedPayroll, on_delete=models.CASCADE, related_name='payslip')
    payslip_number = models.CharField(max_length=100, unique=True)

    # Generation details
    generated_at = models.DateTimeField(auto_now_add=True)
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)

    # Delivery tracking
    status = models.CharField(max_length=20, choices=PAYSLIP_STATUSES, default='generated')
    sent_at = models.DateTimeField(null=True, blank=True)
    viewed_at = models.DateTimeField(null=True, blank=True)
    downloaded_at = models.DateTimeField(null=True, blank=True)

    # File details
    pdf_file = models.FileField(upload_to='payslips/', blank=True)
    file_size = models.IntegerField(default=0)

    def save(self, *args, **kwargs):
        if not self.payslip_number:
            self.payslip_number = self.generate_payslip_number()
        super().save(*args, **kwargs)

    def generate_payslip_number(self):
        """Generate unique payslip number"""
        year = self.payroll.payroll_period.start_date.year
        month = self.payroll.payroll_period.start_date.month
        count = PaySlip.objects.filter(
            payroll__payroll_period__start_date__year=year,
            payroll__payroll_period__start_date__month=month
        ).count() + 1
        return f"PS-{year}{month:02d}-{count:06d}"

    def __str__(self):
        return f"{self.payslip_number} - {self.payroll.employee.full_name}"
