{% extends 'dashboard/dashboard_base.html' %}
{% block dashboard_content %}
<!-- Teacher Dashboard Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Teacher Dashboard</h1>
            <p class="mt-2 text-gray-600">Welcome back, {{ user.get_full_name }}! Manage your classes and students.</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-wrap gap-2">
            <button onclick="markAttendance()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-calendar-check mr-2"></i>Mark Attendance
            </button>
            <button onclick="openGradebook()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-book mr-2"></i>Gradebook
            </button>
            <button onclick="sendMessage()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-envelope mr-2"></i>Send Message
            </button>
        </div>
    </div>
</div>

<!-- Quick Stats Overview -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- My Classes -->
    <div class="bg-blue-600 text-white rounded-lg p-6 shadow hover:shadow-lg transition-shadow cursor-pointer"
         onclick="{% if teacher %}window.location.href='{% url 'teachers:assignments' teacher.id %}'{% else %}window.location.href='{% url 'teachers:list' %}'{% endif %}">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-lg font-semibold">My Classes</div>
                <div class="text-3xl font-bold my-2">{{ my_classes_count }}</div>
                <div class="text-xs">Assigned Classes</div>
            </div>
            <div class="text-4xl opacity-80">
                <i class="fas fa-chalkboard"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center justify-between text-xs">
            <span>Active Today</span>
            <span class="font-semibold">{{ active_classes_today }}</span>
        </div>
    </div>

    <!-- My Students -->
    <div class="bg-green-600 text-white rounded-lg p-6 shadow hover:shadow-lg transition-shadow cursor-pointer"
         onclick="window.location.href='{% url 'students:students_list' %}'">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-lg font-semibold">My Students</div>
                <div class="text-3xl font-bold my-2">{{ my_students_count }}</div>
                <div class="text-xs">Total Students</div>
            </div>
            <div class="text-4xl opacity-80">
                <i class="fas fa-users"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center justify-between text-xs">
            <span>Present Today</span>
            <span class="font-semibold">{{ students_present_today }}</span>
        </div>
    </div>

    <!-- Pending Grades -->
    <div class="bg-orange-600 text-white rounded-lg p-6 shadow hover:shadow-lg transition-shadow cursor-pointer"
         onclick="window.location.href='{% url 'exams:dashboard' %}'">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-lg font-semibold">Pending Grades</div>
                <div class="text-3xl font-bold my-2">{{ pending_grades_count }}</div>
                <div class="text-xs">Need Grading</div>
            </div>
            <div class="text-4xl opacity-80">
                <i class="fas fa-clipboard-list"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center justify-between text-xs">
            <span>Due This Week</span>
            <span class="font-semibold">{{ grades_due_week }}</span>
        </div>
    </div>

    <!-- Messages -->
    <div class="bg-purple-600 text-white rounded-lg p-6 shadow hover:shadow-lg transition-shadow cursor-pointer"
         onclick="window.location.href='/communication/messages/'">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-lg font-semibold">Messages</div>
                <div class="text-3xl font-bold my-2">{{ unread_messages_count }}</div>
                <div class="text-xs">Unread Messages</div>
            </div>
            <div class="text-4xl opacity-80">
                <i class="fas fa-envelope"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center justify-between text-xs">
            <span>New Today</span>
            <span class="font-semibold">{{ new_messages_today }}</span>
        </div>
    </div>
</div>
<!-- Class and Subject Information -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- My Classes Panel -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">My Classes & Subjects</h3>
            <i class="fas fa-chalkboard-teacher text-2xl text-blue-600"></i>
        </div>
        <div class="space-y-4">
            {% for class_assignment in my_class_assignments %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold text-lg">{{ class_assignment.grade.name }}</h4>
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                        {{ class_assignment.students_count }} students
                    </span>
                </div>
                <div class="text-sm text-gray-600 mb-2">
                    <strong>Subject:</strong> {{ class_assignment.subject.name }}
                </div>
                <div class="text-sm text-gray-600 mb-3">
                    <strong>Schedule:</strong> {{ class_assignment.schedule }}
                </div>
                <div class="flex space-x-2">
                    <button onclick="viewClassDetails('{{ class_assignment.id }}')"
                            class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                        <i class="fas fa-eye mr-1"></i>View Details
                    </button>
                    <button onclick="markClassAttendance('{{ class_assignment.id }}')"
                            class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                        <i class="fas fa-calendar-check mr-1"></i>Attendance
                    </button>
                    <button onclick="manageGrades('{{ class_assignment.id }}')"
                            class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors">
                        <i class="fas fa-star mr-1"></i>Grades
                    </button>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-chalkboard text-4xl text-gray-300 mb-2"></i>
                <p>No class assignments yet</p>
            </div>
            {% endfor %}
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200">
            {% if teacher %}
            <button onclick="window.location.href='{% url 'teachers:assignments' teacher.id %}'"
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>View All Class Assignments
            </button>
            {% else %}
            <button onclick="window.location.href='{% url 'teachers:list' %}'"
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-users mr-2"></i>View All Teachers
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Today's Schedule Panel -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">Today's Schedule</h3>
            <i class="fas fa-clock text-2xl text-green-600"></i>
        </div>
        <div class="space-y-3">
            {% for schedule in todays_schedule %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <div class="font-medium">{{ schedule.subject.name }}</div>
                    <div class="text-sm text-gray-600">{{ schedule.grade.name }}</div>
                    <div class="text-xs text-gray-500">Room: {{ schedule.classroom }}</div>
                </div>
                <div class="text-right">
                    <div class="font-semibold text-blue-600">{{ schedule.start_time }} - {{ schedule.end_time }}</div>
                    <div class="text-xs text-gray-500">{{ schedule.duration }} mins</div>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-calendar text-4xl text-gray-300 mb-2"></i>
                <p>No classes scheduled for today</p>
            </div>
            {% endfor %}
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200">
            <button onclick="window.location.href='{% url 'timetable:dashboard' %}'"
                    class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-calendar-alt mr-2"></i>View Full Schedule
            </button>
        </div>
    </div>
</div>
<!-- Student Management & Attendance Tracking -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- Student Management Panel -->
    <div class="lg:col-span-2 bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">Student Management</h3>
            <i class="fas fa-users text-2xl text-blue-600"></i>
        </div>

        <!-- Quick Attendance -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
            <h4 class="font-semibold mb-3">Quick Attendance</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <select id="attendanceClass" class="form-select rounded-lg border-gray-300">
                    <option value="">Select Class</option>
                    {% for class_assignment in my_class_assignments %}
                    <option value="{{ class_assignment.id }}">{{ class_assignment.grade.name }} - {{ class_assignment.subject.name }}</option>
                    {% endfor %}
                </select>
                <button onclick="openAttendanceModal()"
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-calendar-check mr-2"></i>Mark Attendance
                </button>
            </div>
        </div>

        <!-- Recent Student Activities -->
        <div class="space-y-4">
            <h4 class="font-semibold">Recent Student Activities</h4>
            {% for activity in recent_student_activities %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-{{ activity.color }}-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-{{ activity.icon }} text-{{ activity.color }}-600 text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium">{{ activity.student_name }}</div>
                        <div class="text-sm text-gray-600">{{ activity.description }}</div>
                    </div>
                </div>
                <div class="text-xs text-gray-500">{{ activity.timestamp }}</div>
            </div>
            {% empty %}
            <div class="text-center text-gray-500 py-4">
                <p>No recent activities</p>
            </div>
            {% endfor %}
        </div>

        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-2 gap-2">
                <button onclick="window.location.href='{% url 'students:students_list' %}'"
                        class="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                    <i class="fas fa-users mr-1"></i>All Students
                </button>
                <button onclick="window.location.href='/attendance/teacher-view/'"
                        class="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-sm">
                    <i class="fas fa-chart-line mr-1"></i>Attendance Reports
                </button>
            </div>
        </div>
    </div>

    <!-- Attendance Summary Panel -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">Attendance Summary</h3>
            <i class="fas fa-chart-pie text-2xl text-green-600"></i>
        </div>

        <!-- Today's Attendance Status -->
        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 class="font-medium mb-2">My Attendance Today</h4>
            {% if today_attendance %}
                <div class="flex items-center justify-between">
                    <span class="text-sm">Status:</span>
                    <span class="px-2 py-1 rounded-full text-xs font-medium
                        {% if today_attendance.status == 'present' %}bg-green-100 text-green-800
                        {% elif today_attendance.status == 'absent' %}bg-red-100 text-red-800
                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ today_attendance.status|capfirst }}
                    </span>
                </div>
                {% if today_attendance.check_in_time %}
                <div class="text-xs text-gray-600 mt-1">
                    Check-in: {{ today_attendance.check_in_time }}
                </div>
                {% endif %}
            {% else %}
                <div class="text-center text-yellow-600">
                    <i class="fas fa-clock text-2xl mb-2"></i>
                    <p class="text-sm">Not marked yet</p>
                    <button onclick="markMyAttendance()"
                            class="mt-2 bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700">
                        Mark Now
                    </button>
                </div>
            {% endif %}
        </div>

        <!-- Class Attendance Stats -->
        <div class="space-y-3">
            <h4 class="font-medium">Today's Class Attendance</h4>
            {% for class_stat in class_attendance_stats %}
            <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex justify-between items-center mb-2">
                    <span class="font-medium text-sm">{{ class_stat.class_name }}</span>
                    <span class="text-xs text-gray-500">{{ class_stat.attendance_rate }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: {{ class_stat.attendance_rate }}%"></div>
                </div>
                <div class="flex justify-between text-xs text-gray-600 mt-1">
                    <span>Present: {{ class_stat.present_count }}</span>
                    <span>Total: {{ class_stat.total_count }}</span>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-gray-500 py-4">
                <p class="text-sm">No attendance data</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
<!-- Exam Management & Grading -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Exam Management Panel -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">Exam Management & Grading</h3>
            <i class="fas fa-clipboard-list text-2xl text-orange-600"></i>
        </div>

        <!-- Upcoming Exams -->
        <div class="mb-6">
            <h4 class="font-semibold mb-3">Upcoming Exams</h4>
            <div class="space-y-3">
                {% for exam in upcoming_exams %}
                <div class="p-3 border border-gray-200 rounded-lg">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <div class="font-medium">{{ exam.subject.name }}</div>
                            <div class="text-sm text-gray-600">{{ exam.grade.name }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-orange-600">{{ exam.date }}</div>
                            <div class="text-xs text-gray-500">{{ exam.time }}</div>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600 mb-2">
                        Duration: {{ exam.duration }} minutes | Total Marks: {{ exam.total_marks }}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="viewExamDetails('{{ exam.id }}')"
                                class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">
                            <i class="fas fa-eye mr-1"></i>Details
                        </button>
                        <button onclick="manageExamStudents('{{ exam.id }}')"
                                class="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700">
                            <i class="fas fa-users mr-1"></i>Students
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-gray-500 py-4">
                    <i class="fas fa-calendar text-2xl text-gray-300 mb-2"></i>
                    <p class="text-sm">No upcoming exams</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pending Grades -->
        <div class="mb-4">
            <h4 class="font-semibold mb-3">Pending Grades</h4>
            <div class="space-y-2">
                {% for pending in pending_grades %}
                <div class="flex items-center justify-between p-2 bg-yellow-50 rounded">
                    <div>
                        <span class="text-sm font-medium">{{ pending.exam_name }}</span>
                        <span class="text-xs text-gray-600 ml-2">{{ pending.student_count }} students</span>
                    </div>
                    <button onclick="gradeExam('{{ pending.exam_id }}')"
                            class="bg-orange-600 text-white px-3 py-1 rounded text-xs hover:bg-orange-700">
                        <i class="fas fa-star mr-1"></i>Grade
                    </button>
                </div>
                {% empty %}
                <div class="text-center text-gray-500 py-4">
                    <p class="text-sm">No pending grades</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="pt-4 border-t border-gray-200">
            <div class="grid grid-cols-2 gap-2">
                <button onclick="window.location.href='{% url 'teachers:exam_dashboard' %}'"
                        class="bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors text-sm">
                    <i class="fas fa-clipboard-list mr-1"></i>All Exams
                </button>
                <button onclick="window.location.href='{% url 'exams:add_exam' %}'"
                        class="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-sm">
                    <i class="fas fa-plus mr-1"></i>Create Exam
                </button>
            </div>
        </div>
    </div>

    <!-- Communication Panel -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">Communication</h3>
            <i class="fas fa-comments text-2xl text-purple-600"></i>
        </div>

        <!-- Quick Message -->
        <div class="mb-6 p-4 bg-purple-50 rounded-lg">
            <h4 class="font-semibold mb-3">Send Quick Message</h4>
            <form id="quickMessageForm" onsubmit="sendQuickMessage(event)">
                <div class="mb-3">
                    <select id="messageRecipient" class="w-full form-select rounded-lg border-gray-300 text-sm">
                        <option value="">Select Recipients</option>
                        <option value="all_students">All My Students</option>
                        <option value="all_parents">All Parents</option>
                        <option value="specific_class">Specific Class</option>
                        <option value="individual">Individual Student/Parent</option>
                    </select>
                </div>
                <div class="mb-3">
                    <textarea id="messageContent"
                              placeholder="Type your message here..."
                              class="w-full form-textarea rounded-lg border-gray-300 text-sm"
                              rows="3"></textarea>
                </div>
                <button type="submit"
                        class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors text-sm">
                    <i class="fas fa-paper-plane mr-2"></i>Send Message
                </button>
            </form>
        </div>

        <!-- Recent Messages -->
        <div class="mb-4">
            <h4 class="font-semibold mb-3">Recent Messages</h4>
            <div class="space-y-3 max-h-48 overflow-y-auto">
                {% for message in recent_messages %}
                <div class="p-3 border border-gray-200 rounded-lg">
                    <div class="flex justify-between items-start mb-1">
                        <span class="font-medium text-sm">{{ message.sender_name }}</span>
                        <span class="text-xs text-gray-500">{{ message.timestamp }}</span>
                    </div>
                    <p class="text-sm text-gray-700">{{ message.content|truncatechars:60 }}</p>
                    <div class="flex space-x-2 mt-2">
                        <button onclick="viewMessage('{{ message.id }}')"
                                class="text-blue-600 text-xs hover:underline">
                            <i class="fas fa-eye mr-1"></i>View
                        </button>
                        <button onclick="replyMessage('{{ message.id }}')"
                                class="text-green-600 text-xs hover:underline">
                            <i class="fas fa-reply mr-1"></i>Reply
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-gray-500 py-4">
                    <p class="text-sm">No recent messages</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="pt-4 border-t border-gray-200">
            <button onclick="window.location.href='{% url 'communication:notifications_list' %}'"
                    class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-envelope mr-2"></i>View All Messages
            </button>
        </div>
    </div>
</div>
<!-- Petty Cash & Administrative -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Petty Cash Management -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">Petty Cash Requests</h3>
            <i class="fas fa-wallet text-2xl text-green-600"></i>
        </div>

        <!-- Recent Requests -->
        <div class="mb-6">
            <h4 class="font-semibold mb-3">Recent Requests</h4>
            <div class="space-y-3 max-h-48 overflow-y-auto">
                {% for request in petty_cash_requests %}
                <div class="p-3 border border-gray-200 rounded-lg">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <div class="font-medium">${{ request.amount }}</div>
                            <div class="text-sm text-gray-600">{{ request.purpose|truncatechars:30 }}</div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                {% if request.status == 'approved' %}bg-green-100 text-green-800
                                {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {{ request.status|capfirst }}
                            </span>
                            <div class="text-xs text-gray-500 mt-1">{{ request.date }}</div>
                        </div>
                    </div>
                    {% if request.admin_notes %}
                    <div class="text-xs text-gray-600 mt-2">
                        <strong>Notes:</strong> {{ request.admin_notes }}
                    </div>
                    {% endif %}
                </div>
                {% empty %}
                <div class="text-center text-gray-500 py-4">
                    <p class="text-sm">No petty cash requests</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- New Request Form -->
        <div class="p-4 bg-green-50 rounded-lg">
            <h4 class="font-semibold mb-3">Submit New Request</h4>
            <form method="post" id="pettyCashForm">
                {% csrf_token %}
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Amount ($)</label>
                    {{ petty_cash_form.paid }}
                </div>
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Purpose</label>
                    {{ petty_cash_form.purpose }}
                </div>
                <button type="submit" name="petty_cash_submit"
                        class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-paper-plane mr-2"></i>Submit Request
                </button>
            </form>
        </div>
    </div>

    <!-- Performance & Analytics -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-gray-900">My Performance</h3>
            <i class="fas fa-chart-line text-2xl text-indigo-600"></i>
        </div>

        <!-- Performance Metrics -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="bg-blue-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-blue-600">{{ attendance_rate }}%</div>
                <div class="text-sm text-blue-700">Attendance Rate</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-green-600">{{ class_average }}%</div>
                <div class="text-sm text-green-700">Class Average</div>
            </div>
            <div class="bg-orange-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-orange-600">{{ grading_efficiency }}%</div>
                <div class="text-sm text-orange-700">Grading Efficiency</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-purple-600">{{ parent_satisfaction }}%</div>
                <div class="text-sm text-purple-700">Parent Satisfaction</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="space-y-2">
            <h4 class="font-semibold mb-3">Quick Actions</h4>
            <button onclick="generateClassReport()"
                    class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="fas fa-chart-bar mr-2 text-blue-600"></i>Generate Class Report
            </button>
            <button onclick="viewStudentProgress()"
                    class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="fas fa-user-graduate mr-2 text-green-600"></i>View Student Progress
            </button>
            <button onclick="scheduleParentMeeting()"
                    class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="fas fa-calendar-plus mr-2 text-purple-600"></i>Schedule Parent Meeting
            </button>
            <button onclick="requestProfessionalDevelopment()"
                    class="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="fas fa-graduation-cap mr-2 text-indigo-600"></i>Professional Development
            </button>
        </div>
    </div>
</div>
<!-- Modals and JavaScript -->
<script>
// Teacher Dashboard Functions

function markAttendance() {
    const classSelect = document.getElementById('attendanceClass');
    if (classSelect.value) {
        window.location.href = `/attendance/mark/${classSelect.value}/`;
    } else {
        addNotification({
            id: 'attendance_error_' + Date.now(),
            title: 'Selection Required',
            message: 'Please select a class first.',
            type: 'warning'
        });
    }
}

function openGradebook() {
    window.location.href = '{% url 'exams:dashboard' %}';
}

function sendMessage() {
    window.location.href = '/communication/compose/';
}

function viewClassDetails(classId) {
    // Redirect to class management or student list
    window.location.href = '{% url 'students:students_list' %}';
}

function markClassAttendance(classId) {
    window.location.href = '{% url 'attendance:dashboard' %}';
}

function manageGrades(classId) {
    window.location.href = '{% url 'exams:dashboard' %}';
}

function openAttendanceModal() {
    const classSelect = document.getElementById('attendanceClass');
    if (classSelect.value) {
        // Open attendance marking modal or redirect
        window.location.href = `/attendance/mark/${classSelect.value}/`;
    } else {
        addNotification({
            id: 'class_required_' + Date.now(),
            title: 'Class Selection Required',
            message: 'Please select a class to mark attendance.',
            type: 'warning'
        });
    }
}

function markMyAttendance() {
    ajax.post('/api/teachers/mark-attendance/', {})
        .then(response => {
            addNotification({
                id: 'attendance_marked_' + Date.now(),
                title: 'Attendance Marked',
                message: 'Your attendance has been marked successfully.',
                type: 'success'
            });
            // Refresh the page to update attendance status
            setTimeout(() => window.location.reload(), 1500);
        })
        .catch(error => {
            addNotification({
                id: 'attendance_error_' + Date.now(),
                title: 'Error',
                message: 'Failed to mark attendance. Please try again.',
                type: 'error'
            });
        });
}

function viewExamDetails(examId) {
    window.location.href = `/exams/${examId}/`;
}

function manageExamStudents(examId) {
    window.location.href = `/exams/${examId}/students/`;
}

function gradeExam(examId) {
    window.location.href = `/exams/${examId}/grade/`;
}

function sendQuickMessage(event) {
    event.preventDefault();

    const recipient = document.getElementById('messageRecipient').value;
    const content = document.getElementById('messageContent').value;

    if (!recipient || !content) {
        addNotification({
            id: 'message_error_' + Date.now(),
            title: 'Missing Information',
            message: 'Please select recipients and enter a message.',
            type: 'warning'
        });
        return;
    }

    ajax.post('/api/communication/send-message/', {
        recipient_type: recipient,
        content: content
    })
    .then(response => {
        addNotification({
            id: 'message_sent_' + Date.now(),
            title: 'Message Sent',
            message: 'Your message has been sent successfully.',
            type: 'success'
        });
        // Clear form
        document.getElementById('messageRecipient').value = '';
        document.getElementById('messageContent').value = '';
    })
    .catch(error => {
        addNotification({
            id: 'message_error_' + Date.now(),
            title: 'Send Failed',
            message: 'Failed to send message. Please try again.',
            type: 'error'
        });
    });
}

function viewMessage(messageId) {
    window.location.href = `/communication/message/${messageId}/`;
}

function replyMessage(messageId) {
    window.location.href = `/communication/reply/${messageId}/`;
}

function generateClassReport() {
    window.location.href = '/reporting/generate/class-performance/';
}

function viewStudentProgress() {
    window.location.href = '{% url 'students:students_list' %}';
}

function scheduleParentMeeting() {
    window.location.href = '{% url 'communication:notifications_list' %}';
}

function requestProfessionalDevelopment() {
    window.location.href = '{% url 'teachers:list' %}';
}

// Auto-refresh dashboard data every 5 minutes
setInterval(() => {
    ajax.get('/api/dashboard/teacher-stats/')
        .then(response => {
            if (response.data) {
                // Update statistics
                Object.keys(response.data).forEach(key => {
                    const element = document.querySelector(`[data-stat="${key}"]`);
                    if (element) {
                        element.textContent = response.data[key];
                    }
                });
            }
        })
        .catch(error => {
            console.warn('Failed to refresh dashboard data:', error);
        });
}, 300000); // 5 minutes

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('Teacher Dashboard initialized');

    // Add form validation
    const pettyCashForm = document.getElementById('pettyCashForm');
    if (pettyCashForm) {
        pettyCashForm.addEventListener('submit', function(e) {
            const amount = this.querySelector('[name="paid"]').value;
            const purpose = this.querySelector('[name="purpose"]').value;

            if (!amount || !purpose) {
                e.preventDefault();
                addNotification({
                    id: 'form_error_' + Date.now(),
                    title: 'Form Incomplete',
                    message: 'Please fill in all required fields.',
                    type: 'warning'
                });
            }
        });
    }
});
</script>
{% endblock %}