# Generated by Django 5.2.1 on 2025-05-21 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="TeacherSalary",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("month", models.Char<PERSON>ield(max_length=255)),
                ("year", models.PositiveIntegerField()),
                ("date", models.DateField()),
                ("basic_salary", models.DecimalField(decimal_places=2, max_digits=11)),
                (
                    "allowances",
                    models.DecimalField(decimal_places=2, default=0, max_digits=11),
                ),
                (
                    "deductions",
                    models.DecimalField(decimal_places=2, default=0, max_digits=11),
                ),
                ("paid", models.DecimalField(decimal_places=2, max_digits=11)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Bank", "Bank"),
                            ("Cash", "Cash"),
                            ("M-Pesa", "M-Pesa"),
                        ],
                        default="Bank",
                        max_length=50,
                    ),
                ),
                ("bank_name", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "account_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "transaction_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("_status", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="TeacherSalaryHistory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("grade_id", models.IntegerField()),
                ("subject_id", models.IntegerField()),
                ("subject_fee", models.DecimalField(decimal_places=2, max_digits=11)),
                ("student_count", models.IntegerField()),
                ("hall_charge", models.IntegerField()),
                ("subtotal", models.DecimalField(decimal_places=2, max_digits=11)),
                ("paid", models.DecimalField(decimal_places=2, max_digits=11)),
                ("_status", models.CharField(max_length=255)),
                ("month", models.CharField(max_length=255)),
                ("year", models.PositiveIntegerField()),
                ("date", models.DateField()),
                ("invoice_number", models.IntegerField()),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Bank", "Bank"),
                            ("Cash", "Cash"),
                            ("M-Pesa", "M-Pesa"),
                        ],
                        default="Bank",
                        max_length=50,
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
            ],
        ),
    ]
