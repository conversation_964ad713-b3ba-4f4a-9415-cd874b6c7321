from django.db import models
from datetime import date
from django.utils import timezone

# Create your models here.

class StudentHealthRecords(models.Model):
    BLOOD_GROUPS = [
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    ]
    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    blood_group = models.CharField(max_length=10, null=True, blank=True, choices=BLOOD_GROUPS)
    allergies = models.TextField(null=True, blank=True)
    chronic_conditions = models.TextField(null=True, blank=True)
    medications = models.TextField(null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=255, null=True, blank=True)
    emergency_contact_phone = models.Char<PERSON><PERSON>(max_length=20, null=True, blank=True)
    emergency_contact_relationship = models.CharField(max_length=50, null=True, blank=True)
    insurance_provider = models.CharField(max_length=100, null=True, blank=True)
    insurance_number = models.CharField(max_length=50, null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Health Record - {self.student.index_number}"

class StudentHealthVisit(models.Model):
    VISIT_STATUSES = [
        ('open', 'Open'),
        ('closed', 'Closed'),
    ]
    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    visit_date = models.DateTimeField()
    complaint = models.TextField()
    diagnosis = models.TextField(null=True, blank=True)
    treatment = models.TextField(null=True, blank=True)
    medication = models.TextField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    attended_by = models.CharField(max_length=255, null=True, blank=True)
    referred_to_hospital = models.BooleanField(default=False)
    hospital_name = models.CharField(max_length=255, null=True, blank=True)
    parent_notified = models.BooleanField(default=False)
    notification_date = models.DateTimeField(null=True, blank=True)
    follow_up_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=50, default='open', choices=VISIT_STATUSES)

    def __str__(self):
        return f"Health Visit - {self.id}"

# Enhanced Models for Complete Health Management

class MedicalCondition(models.Model):
    """Track specific medical conditions and chronic illnesses"""
    CONDITION_TYPES = [
        ('chronic', 'Chronic Condition'),
        ('acute', 'Acute Condition'),
        ('allergy', 'Allergy'),
        ('disability', 'Disability'),
        ('mental_health', 'Mental Health'),
        ('other', 'Other'),
    ]

    SEVERITY_LEVELS = [
        ('mild', 'Mild'),
        ('moderate', 'Moderate'),
        ('severe', 'Severe'),
        ('critical', 'Critical'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='medical_conditions')
    condition_name = models.CharField(max_length=200)
    condition_type = models.CharField(max_length=20, choices=CONDITION_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS)
    description = models.TextField(blank=True)
    diagnosed_date = models.DateField(null=True, blank=True)
    diagnosed_by = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)
    requires_medication = models.BooleanField(default=False)
    requires_monitoring = models.BooleanField(default=False)
    emergency_action = models.TextField(blank=True, help_text="Emergency action to take if condition flares up")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student.full_name} - {self.condition_name}"

class Medication(models.Model):
    """Track medications and prescriptions"""
    MEDICATION_TYPES = [
        ('prescription', 'Prescription'),
        ('over_counter', 'Over-the-Counter'),
        ('supplement', 'Supplement'),
        ('emergency', 'Emergency Medication'),
    ]

    FREQUENCY_CHOICES = [
        ('once_daily', 'Once Daily'),
        ('twice_daily', 'Twice Daily'),
        ('three_times_daily', 'Three Times Daily'),
        ('four_times_daily', 'Four Times Daily'),
        ('as_needed', 'As Needed'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='medications')
    condition = models.ForeignKey(MedicalCondition, on_delete=models.SET_NULL, null=True, blank=True)
    medication_name = models.CharField(max_length=200)
    medication_type = models.CharField(max_length=20, choices=MEDICATION_TYPES)
    dosage = models.CharField(max_length=100)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    frequency_details = models.CharField(max_length=200, blank=True, help_text="Specific timing details")
    prescribed_by = models.CharField(max_length=200)
    prescribed_date = models.DateField()
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    side_effects = models.TextField(blank=True)
    special_instructions = models.TextField(blank=True)
    stored_at_school = models.BooleanField(default=False)
    storage_location = models.CharField(max_length=100, blank=True)
    emergency_medication = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.student.full_name} - {self.medication_name}"

class Immunization(models.Model):
    """Track immunization and vaccination records"""
    VACCINE_TYPES = [
        ('routine', 'Routine Childhood Vaccine'),
        ('travel', 'Travel Vaccine'),
        ('seasonal', 'Seasonal Vaccine'),
        ('emergency', 'Emergency Vaccine'),
        ('booster', 'Booster Shot'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='immunizations')
    vaccine_name = models.CharField(max_length=200)
    vaccine_type = models.CharField(max_length=20, choices=VACCINE_TYPES)
    date_administered = models.DateField()
    administered_by = models.CharField(max_length=200)
    clinic_hospital = models.CharField(max_length=200, blank=True)
    batch_number = models.CharField(max_length=100, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    next_due_date = models.DateField(null=True, blank=True)
    dose_number = models.IntegerField(default=1)
    total_doses_required = models.IntegerField(default=1)
    side_effects_observed = models.TextField(blank=True)
    certificate_number = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def is_due_for_next_dose(self):
        """Check if next dose is due"""
        if self.next_due_date and self.next_due_date <= date.today():
            return True
        return False

    def __str__(self):
        return f"{self.student.full_name} - {self.vaccine_name} (Dose {self.dose_number})"

class EmergencyContact(models.Model):
    """Enhanced emergency contact information"""
    RELATIONSHIP_CHOICES = [
        ('parent', 'Parent'),
        ('guardian', 'Guardian'),
        ('sibling', 'Sibling'),
        ('grandparent', 'Grandparent'),
        ('aunt_uncle', 'Aunt/Uncle'),
        ('family_friend', 'Family Friend'),
        ('other', 'Other'),
    ]

    CONTACT_PRIORITIES = [
        ('primary', 'Primary Contact'),
        ('secondary', 'Secondary Contact'),
        ('tertiary', 'Tertiary Contact'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='emergency_contacts')
    full_name = models.CharField(max_length=200)
    relationship = models.CharField(max_length=20, choices=RELATIONSHIP_CHOICES)
    priority = models.CharField(max_length=20, choices=CONTACT_PRIORITIES)
    phone_primary = models.CharField(max_length=20)
    phone_secondary = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    workplace = models.CharField(max_length=200, blank=True)
    work_phone = models.CharField(max_length=20, blank=True)
    is_authorized_pickup = models.BooleanField(default=False)
    is_medical_decision_maker = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['student', 'priority']

    def __str__(self):
        return f"{self.student.full_name} - {self.full_name} ({self.get_priority_display()})"

class HealthInsurance(models.Model):
    """Track health insurance details"""
    INSURANCE_TYPES = [
        ('private', 'Private Insurance'),
        ('public', 'Public/Government Insurance'),
        ('school', 'School Insurance'),
        ('family', 'Family Plan'),
        ('individual', 'Individual Plan'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='health_insurance')
    insurance_provider = models.CharField(max_length=200)
    insurance_type = models.CharField(max_length=20, choices=INSURANCE_TYPES)
    policy_number = models.CharField(max_length=100)
    group_number = models.CharField(max_length=100, blank=True)
    policy_holder_name = models.CharField(max_length=200)
    policy_holder_relationship = models.CharField(max_length=50)
    effective_date = models.DateField()
    expiry_date = models.DateField(null=True, blank=True)
    coverage_details = models.TextField(blank=True)
    copay_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    deductible_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    emergency_coverage = models.BooleanField(default=True)
    prescription_coverage = models.BooleanField(default=True)
    dental_coverage = models.BooleanField(default=False)
    vision_coverage = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def is_expired(self):
        """Check if insurance is expired"""
        if self.expiry_date and self.expiry_date < date.today():
            return True
        return False

    def __str__(self):
        return f"{self.student.full_name} - {self.insurance_provider}"

class HealthAlert(models.Model):
    """Health alerts and reminders system"""
    ALERT_TYPES = [
        ('medication_reminder', 'Medication Reminder'),
        ('appointment_reminder', 'Appointment Reminder'),
        ('immunization_due', 'Immunization Due'),
        ('condition_monitoring', 'Condition Monitoring'),
        ('emergency_alert', 'Emergency Alert'),
        ('insurance_expiry', 'Insurance Expiry'),
        ('follow_up_required', 'Follow-up Required'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='health_alerts')
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    due_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    acknowledged_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-priority', '-created_at']

    def is_overdue(self):
        """Check if alert is overdue"""
        if self.due_date and self.due_date < timezone.now() and self.status == 'active':
            return True
        return False

    def __str__(self):
        return f"{self.student.full_name} - {self.title}"


