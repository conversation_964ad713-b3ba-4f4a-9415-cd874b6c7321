from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login as auth_login
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.views import View
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import csv
import io
from .forms import (
    LoginForm, UserRegistrationForm, ParentChildRelationshipForm,
    AccountUnlockForm, BulkParentChildLinkForm
)
from .models import User, LoginAttempt, ParentChildRelationship
from apps.students.models import Student

# Create your views here.

class LoginView(View):
    template_name = 'accounts/login.html'

    def get_dashboard_url(self, user):
        if user.type == 'admin':
            return 'dashboard:admin_dashboard'
        elif user.type == 'teacher':
            return 'dashboard:teacher_dashboard'
        elif user.type == 'student':
            return 'dashboard:student_dashboard'
        elif user.type == 'parent':
            return 'dashboard:parent_dashboard'
        elif user.type == 'accountant':
            return 'dashboard:accountant_dashboard'
        return 'dashboard:admin_dashboard'

    def get(self, request):
        form = LoginForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = LoginForm(request.POST)
        email = request.POST.get('email', '')

        # Get client IP and user agent for logging
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        if form.is_valid():
            user = form.cleaned_data['user']

            # Log successful login
            LoginAttempt.objects.create(
                user=user,
                email=email,
                ip_address=ip_address,
                user_agent=user_agent,
                attempt_type='success'
            )

            auth_login(request, user)
            messages.success(request, 'Login successful!')
            return redirect(self.get_dashboard_url(user))
        else:
            # Log failed login attempt
            try:
                user = User.objects.get(email=email)
                failure_reason = str(form.errors.get('__all__', ['Unknown error'])[0])
            except User.DoesNotExist:
                user = None
                failure_reason = 'User does not exist'

            LoginAttempt.objects.create(
                user=user,
                email=email,
                ip_address=ip_address,
                user_agent=user_agent,
                attempt_type='failed',
                failure_reason=failure_reason
            )

        return render(request, self.template_name, {'form': form})

    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class RegisterView(View):
    template_name = 'accounts/registration.html'
    user_type = None  # Should be set in subclass

    def get_dashboard_url(self, user):
        """Get the appropriate dashboard URL based on user type"""
        if user.type == 'admin':
            return 'dashboard:admin_dashboard'
        elif user.type == 'teacher':
            return 'dashboard:teacher_dashboard'
        elif user.type == 'student':
            return 'dashboard:student_dashboard'
        elif user.type == 'parent':
            return 'dashboard:parent_dashboard'
        elif user.type == 'accountant':
            return 'dashboard:accountant_dashboard'
        else:
            return 'dashboard:admin_dashboard'  # Default fallback

    def get(self, request):
        form = UserRegistrationForm(initial={'type': self.user_type})
        return render(request, self.template_name, {'form': form, 'user_type': self.user_type})

    def post(self, request):
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.type = self.user_type
            user.set_password(form.cleaned_data['password1'])
            user.save()
            auth_login(request, user)
            messages.success(request, f'{self.user_type.capitalize()} registration successful!')
            return redirect(self.get_dashboard_url(user))
        return render(request, self.template_name, {'form': form, 'user_type': self.user_type})

class AdminRegisterView(RegisterView):
    user_type = 'admin'

class TeacherRegisterView(RegisterView):
    user_type = 'teacher'

class StudentRegisterView(RegisterView):
    user_type = 'student'

class ParentRegisterView(RegisterView):
    user_type = 'parent'

class AccountantRegisterView(RegisterView):
    user_type = 'accountant'


@login_required
def parent_child_relationships(request):
    """
    View to manage parent-child relationships
    """
    relationships = ParentChildRelationship.objects.select_related('parent', 'student').all()

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        relationships = relationships.filter(
            Q(parent__email__icontains=search_query) |
            Q(student__full_name__icontains=search_query) |
            Q(relationship_type__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(relationships, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_relationships': relationships.count(),
    }

    return render(request, 'accounts/parent_child_relationships.html', context)


@login_required
def add_parent_child_relationship(request):
    """
    View to add a new parent-child relationship
    """
    if request.method == 'POST':
        form = ParentChildRelationshipForm(request.POST)
        if form.is_valid():
            relationship = form.save()
            messages.success(request, f'Successfully linked {relationship.parent.email} to {relationship.student.full_name}')
            return redirect('accounts:parent_child_relationships')
    else:
        form = ParentChildRelationshipForm()

    context = {
        'form': form,
        'title': 'Add Parent-Child Relationship'
    }

    return render(request, 'accounts/add_parent_child_relationship.html', context)


@login_required
def edit_parent_child_relationship(request, pk):
    """
    View to edit an existing parent-child relationship
    """
    relationship = get_object_or_404(ParentChildRelationship, pk=pk)

    if request.method == 'POST':
        form = ParentChildRelationshipForm(request.POST, instance=relationship)
        if form.is_valid():
            form.save()
            messages.success(request, 'Relationship updated successfully')
            return redirect('accounts:parent_child_relationships')
    else:
        form = ParentChildRelationshipForm(instance=relationship)

    context = {
        'form': form,
        'relationship': relationship,
        'title': 'Edit Parent-Child Relationship'
    }

    return render(request, 'accounts/edit_parent_child_relationship.html', context)


@staff_member_required
def unlock_account(request):
    """
    View for administrators to unlock user accounts
    """
    if request.method == 'POST':
        form = AccountUnlockForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            unlock_reason = form.cleaned_data['unlock_reason']

            try:
                user = User.objects.get(email=email)
                user.unlock_account()

                # Log the unlock action
                LoginAttempt.objects.create(
                    user=user,
                    email=email,
                    ip_address=request.META.get('REMOTE_ADDR', ''),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    attempt_type='success',
                    failure_reason=f'Account unlocked by admin: {unlock_reason}'
                )

                messages.success(request, f'Account for {email} has been unlocked successfully.')
                return redirect('accounts:unlock_account')

            except User.DoesNotExist:
                messages.error(request, 'User not found.')
    else:
        form = AccountUnlockForm()

    # Get locked accounts for display
    locked_accounts = User.objects.filter(
        Q(account_locked_until__gt=timezone.now()) | Q(status='locked')
    ).order_by('-account_locked_until')

    context = {
        'form': form,
        'locked_accounts': locked_accounts,
        'title': 'Unlock User Accounts'
    }

    return render(request, 'accounts/unlock_account.html', context)


@login_required
def login_attempts_log(request):
    """
    View to display login attempts log
    """
    attempts = LoginAttempt.objects.select_related('user').order_by('-timestamp')

    # Filter by attempt type if provided
    attempt_type = request.GET.get('type', '')
    if attempt_type:
        attempts = attempts.filter(attempt_type=attempt_type)

    # Filter by date range if provided
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    if date_from:
        attempts = attempts.filter(timestamp__date__gte=date_from)
    if date_to:
        attempts = attempts.filter(timestamp__date__lte=date_to)

    # Pagination
    paginator = Paginator(attempts, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'attempt_type': attempt_type,
        'date_from': date_from,
        'date_to': date_to,
        'total_attempts': attempts.count(),
    }

    return render(request, 'accounts/login_attempts_log.html', context)


@login_required
def bulk_parent_child_link(request):
    """
    View for bulk linking parents to children via CSV upload
    """
    if request.method == 'POST':
        form = BulkParentChildLinkForm(request.POST, request.FILES)
        if form.is_valid():
            csv_file = request.FILES['csv_file']
            overwrite_existing = form.cleaned_data['overwrite_existing']

            try:
                # Process CSV file
                decoded_file = csv_file.read().decode('utf-8')
                csv_data = csv.reader(io.StringIO(decoded_file))

                # Skip header row
                next(csv_data)

                created_count = 0
                updated_count = 0
                error_count = 0
                errors = []

                for row_num, row in enumerate(csv_data, start=2):
                    if len(row) < 3:
                        errors.append(f"Row {row_num}: Insufficient data")
                        error_count += 1
                        continue

                    parent_email, student_id, relationship_type = row[:3]

                    try:
                        parent = User.objects.get(email=parent_email, type='parent')
                        student = Student.objects.get(id=student_id)

                        relationship, created = ParentChildRelationship.objects.get_or_create(
                            parent=parent,
                            student=student,
                            defaults={'relationship_type': relationship_type}
                        )

                        if created:
                            created_count += 1
                        elif overwrite_existing:
                            relationship.relationship_type = relationship_type
                            relationship.save()
                            updated_count += 1

                    except User.DoesNotExist:
                        errors.append(f"Row {row_num}: Parent with email {parent_email} not found")
                        error_count += 1
                    except Student.DoesNotExist:
                        errors.append(f"Row {row_num}: Student with ID {student_id} not found")
                        error_count += 1
                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        error_count += 1

                # Show results
                if created_count > 0:
                    messages.success(request, f'Successfully created {created_count} relationships')
                if updated_count > 0:
                    messages.success(request, f'Successfully updated {updated_count} relationships')
                if error_count > 0:
                    messages.warning(request, f'{error_count} errors occurred. See details below.')
                    for error in errors[:10]:  # Show first 10 errors
                        messages.error(request, error)

                return redirect('accounts:parent_child_relationships')

            except Exception as e:
                messages.error(request, f'Error processing CSV file: {str(e)}')
    else:
        form = BulkParentChildLinkForm()

    context = {
        'form': form,
        'title': 'Bulk Parent-Child Linking'
    }

    return render(request, 'accounts/bulk_parent_child_link.html', context)
