# Generated by Django 5.2.1 on 2025-06-09 13:26

import apps.students.models
import datetime
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0002_initial"),
        ("students", "0001_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Guardian",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("full_name", models.CharField(max_length=255)),
                (
                    "gender",
                    models.CharField(
                        choices=[("Male", "Male"), ("Female", "Female")], max_length=10
                    ),
                ),
                (
                    "relationship",
                    models.CharField(
                        choices=[
                            ("Father", "Father"),
                            ("<PERSON>", "<PERSON>"),
                            ("<PERSON>", "<PERSON>"),
                            ("Grandfather", "Grandfather"),
                            ("Grandmother", "Grandmother"),
                            ("Uncle", "Uncle"),
                            ("Aunt", "Aunt"),
                            ("Brother", "Brother"),
                            ("Sister", "Sister"),
                            ("Other", "Other"),
                        ],
                        max_length=50,
                    ),
                ),
                ("phone", models.CharField(max_length=20)),
                ("alt_phone", models.CharField(blank=True, max_length=20, null=True)),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                ("address", models.TextField()),
                ("occupation", models.CharField(blank=True, max_length=100, null=True)),
                ("employer", models.CharField(blank=True, max_length=100, null=True)),
                ("national_id", models.CharField(blank=True, max_length=20, null=True)),
                ("is_primary_contact", models.BooleanField(default=False)),
                ("is_emergency_contact", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Guardian",
                "verbose_name_plural": "Guardians",
            },
        ),
        migrations.AlterModelOptions(
            name="student",
            options={
                "ordering": ["last_name", "first_name"],
                "verbose_name": "Student",
                "verbose_name_plural": "Students",
            },
        ),
        migrations.AlterModelOptions(
            name="studentgrade",
            options={
                "ordering": ["-academic_year", "-term", "grade__name"],
                "verbose_name": "Student Grade Assignment",
                "verbose_name_plural": "Student Grade Assignments",
            },
        ),
        migrations.AlterModelOptions(
            name="studentsubject",
            options={
                "ordering": ["academic_year", "term", "subject__name"],
                "verbose_name": "Student Subject Assignment",
                "verbose_name_plural": "Student Subject Assignments",
            },
        ),
        migrations.AddField(
            model_name="student",
            name="admission_date",
            field=models.DateField(default=datetime.date.today),
        ),
        migrations.AddField(
            model_name="student",
            name="admission_number",
            field=models.CharField(blank=True, max_length=20, null=True, unique=True),
        ),
        migrations.AddField(
            model_name="student",
            name="allergies",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="blood_group",
            field=models.CharField(blank=True, max_length=5, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="city",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="county",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="students_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="student",
            name="current_grade",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="academics.grade",
            ),
        ),
        migrations.AddField(
            model_name="student",
            name="current_medications",
            field=models.TextField(
                blank=True, help_text="Current medications", null=True
            ),
        ),
        migrations.AddField(
            model_name="student",
            name="current_section",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="date_of_birth",
            field=models.DateField(default=datetime.date.today),
        ),
        migrations.AddField(
            model_name="student",
            name="first_name",
            field=models.CharField(default="", max_length=100),
        ),
        migrations.AddField(
            model_name="student",
            name="graduation_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="last_name",
            field=models.CharField(default="", max_length=100),
        ),
        migrations.AddField(
            model_name="student",
            name="middle_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="photo",
            field=models.ImageField(
                blank=True,
                null=True,
                upload_to=apps.students.models.student_photo_upload_path,
            ),
        ),
        migrations.AddField(
            model_name="student",
            name="place_of_birth",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="postal_address",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="postal_code",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="preferred_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="previous_class",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="special_needs",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("inactive", "Inactive"),
                    ("transferred", "Transferred"),
                    ("graduated", "Graduated"),
                    ("suspended", "Suspended"),
                    ("expelled", "Expelled"),
                    ("withdrawn", "Withdrawn"),
                ],
                default="active",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="student",
            name="student_id",
            field=models.CharField(
                default="STU2024TEMP", editable=False, max_length=20, unique=True
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="student",
            name="transfer_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="transfer_documents",
            field=models.TextField(
                blank=True, help_text="List of transfer documents received", null=True
            ),
        ),
        migrations.AddField(
            model_name="student",
            name="transfer_reason",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="students_updated",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="academic_year",
            field=models.PositiveIntegerField(default=2024),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="assignment_date",
            field=models.DateField(default=datetime.date(2025, 6, 9)),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="assignment_type",
            field=models.CharField(
                choices=[
                    ("new_admission", "New Admission"),
                    ("promotion", "Promotion"),
                    ("transfer", "Transfer"),
                    ("repeat", "Repeat"),
                    ("readmission", "Readmission"),
                ],
                default="new_admission",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="class_position",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="is_current",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="next_grade",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="promoted_from",
                to="academics.grade",
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="promotion_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="promotion_reason",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="student",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="grade_assignments",
                to="students.student",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="term_average",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=5, null=True
            ),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="total_students",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="studentgrade",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="academic_year",
            field=models.PositiveIntegerField(default=2024),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="current_average",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=5, null=True
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="grade",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to="academics.grade",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="highest_score",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=5, null=True
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="lowest_score",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=5, null=True
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="registration_date",
            field=models.DateField(default=datetime.date(2025, 6, 9)),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="registration_status",
            field=models.CharField(
                choices=[
                    ("registered", "Registered"),
                    ("dropped", "Dropped"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("transferred", "Transferred"),
                ],
                default="registered",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="student",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="subject_assignments",
                to="students.student",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="subject",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to="academics.subject",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="subject_type",
            field=models.CharField(
                choices=[
                    ("compulsory", "Compulsory"),
                    ("elective", "Elective"),
                    ("optional", "Optional"),
                    ("remedial", "Remedial"),
                ],
                default="compulsory",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="teacher",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="teachers.teacher",
            ),
        ),
        migrations.AddField(
            model_name="studentsubject",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="TSC_number",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="_status",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="address",
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name="student",
            name="b_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="boarding_status",
            field=models.CharField(
                choices=[("Day", "Day"), ("Boarding", "Boarding")],
                default="Day",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="student",
            name="email",
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="full_name",
            field=models.CharField(editable=False, max_length=255),
        ),
        migrations.AlterField(
            model_name="student",
            name="gender",
            field=models.CharField(
                choices=[("Male", "Male"), ("Female", "Female")], max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="student",
            name="health_conditions",
            field=models.TextField(
                blank=True, help_text="Known medical conditions", null=True
            ),
        ),
        migrations.AlterField(
            model_name="student",
            name="i_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="image_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="kcpe_index",
            field=models.CharField(
                blank=True, max_length=50, null=True, verbose_name="KCPE Index Number"
            ),
        ),
        migrations.AlterField(
            model_name="student",
            name="kcpe_score",
            field=models.IntegerField(blank=True, null=True, verbose_name="KCPE Score"),
        ),
        migrations.AlterField(
            model_name="student",
            name="kcpe_year",
            field=models.PositiveIntegerField(
                blank=True, null=True, verbose_name="KCPE Year"
            ),
        ),
        migrations.AlterField(
            model_name="student",
            name="nationality",
            field=models.CharField(
                choices=[("Kenyan", "Kenyan"), ("Other", "Other")],
                default="Kenyan",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="student",
            name="phone",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="reg_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="reg_month",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="reg_year",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="student",
            name="transfer_status",
            field=models.CharField(
                choices=[
                    ("new", "New Student"),
                    ("transfer_in", "Transfer In"),
                    ("transfer_out", "Transfer Out"),
                    ("readmission", "Readmission"),
                ],
                default="new",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="studentgrade",
            name="index_number",
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="studentgrade",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("promoted", "Promoted"),
                    ("repeated", "Repeated"),
                    ("transferred", "Transferred"),
                    ("graduated", "Graduated"),
                    ("withdrawn", "Withdrawn"),
                ],
                default="active",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="studentgrade",
            name="year",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="studentsubject",
            name="_status",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="studentsubject",
            name="index_number",
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="studentsubject",
            name="reg_month",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="studentsubject",
            name="sr",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="academics.subjectrouting",
            ),
        ),
        migrations.AlterField(
            model_name="studentsubject",
            name="year",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterUniqueTogether(
            name="studentgrade",
            unique_together={("student", "grade", "academic_year", "term")},
        ),
        migrations.AlterUniqueTogether(
            name="studentsubject",
            unique_together={("student", "subject", "academic_year", "term")},
        ),
        migrations.CreateModel(
            name="StudentDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("birth_certificate", "Birth Certificate"),
                            ("kcpe_certificate", "KCPE Certificate"),
                            ("transfer_letter", "Transfer Letter"),
                            ("medical_report", "Medical Report"),
                            ("passport_photo", "Passport Photo"),
                            ("immunization_card", "Immunization Card"),
                            ("other", "Other"),
                        ],
                        max_length=50,
                    ),
                ),
                ("document_name", models.CharField(max_length=255)),
                (
                    "document_file",
                    models.FileField(
                        upload_to=apps.students.models.student_document_upload_path
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="students.student",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Document",
                "verbose_name_plural": "Student Documents",
            },
        ),
        migrations.CreateModel(
            name="StudentGraduation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("graduation_year", models.PositiveIntegerField()),
                ("graduation_date", models.DateField()),
                (
                    "graduation_type",
                    models.CharField(
                        choices=[
                            ("regular", "Regular Graduation"),
                            ("early", "Early Graduation"),
                            ("delayed", "Delayed Graduation"),
                            ("certificate", "Certificate Completion"),
                        ],
                        default="regular",
                        max_length=20,
                    ),
                ),
                (
                    "graduation_status",
                    models.CharField(
                        choices=[
                            ("eligible", "Eligible"),
                            ("graduated", "Graduated"),
                            ("deferred", "Deferred"),
                            ("incomplete", "Incomplete"),
                        ],
                        default="eligible",
                        max_length=20,
                    ),
                ),
                (
                    "final_average",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("final_grade", models.CharField(blank=True, max_length=5, null=True)),
                ("class_position", models.PositiveIntegerField(blank=True, null=True)),
                ("total_graduates", models.PositiveIntegerField(blank=True, null=True)),
                ("completed_all_subjects", models.BooleanField(default=False)),
                ("meets_attendance_requirements", models.BooleanField(default=False)),
                ("cleared_all_fees", models.BooleanField(default=False)),
                ("returned_all_books", models.BooleanField(default=False)),
                (
                    "certificate_number",
                    models.CharField(blank=True, max_length=50, null=True, unique=True),
                ),
                (
                    "awards",
                    models.TextField(
                        blank=True,
                        help_text="Awards and recognitions received",
                        null=True,
                    ),
                ),
                (
                    "next_school",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("next_level", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "career_path",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("approval_date", models.DateField(blank=True, null=True)),
                ("certificate_issued", models.BooleanField(default=False)),
                ("certificate_issue_date", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="graduations_approved",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="graduations_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="graduation",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Graduation",
                "verbose_name_plural": "Student Graduations",
                "ordering": ["-graduation_year", "student__last_name"],
            },
        ),
        migrations.CreateModel(
            name="StudentGuardian",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("relationship_notes", models.TextField(blank=True, null=True)),
                ("is_authorized_pickup", models.BooleanField(default=True)),
                ("is_fee_payer", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "guardian",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.guardian",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Guardian",
                "verbose_name_plural": "Student Guardians",
                "unique_together": {("student", "guardian")},
            },
        ),
        migrations.AddField(
            model_name="student",
            name="guardians",
            field=models.ManyToManyField(
                related_name="students",
                through="students.StudentGuardian",
                to="students.guardian",
            ),
        ),
        migrations.CreateModel(
            name="StudentStatusHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "previous_status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("transferred", "Transferred"),
                            ("graduated", "Graduated"),
                            ("suspended", "Suspended"),
                            ("expelled", "Expelled"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "new_status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("transferred", "Transferred"),
                            ("graduated", "Graduated"),
                            ("suspended", "Suspended"),
                            ("expelled", "Expelled"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        max_length=20,
                    ),
                ),
                ("change_date", models.DateField()),
                ("reason", models.TextField()),
                (
                    "supporting_documents",
                    models.TextField(
                        blank=True, help_text="List of supporting documents", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_status_changes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="status_history",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Status History",
                "verbose_name_plural": "Student Status Histories",
                "ordering": ["-change_date"],
            },
        ),
        migrations.CreateModel(
            name="StudentPromotion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("academic_year", models.PositiveIntegerField()),
                (
                    "promotion_type",
                    models.CharField(
                        choices=[
                            ("automatic", "Automatic Promotion"),
                            ("conditional", "Conditional Promotion"),
                            ("merit", "Merit Promotion"),
                            ("repeat", "Repeat Class"),
                            ("skip", "Skip Grade"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "promotion_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "overall_average",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("class_position", models.PositiveIntegerField(blank=True, null=True)),
                ("total_students", models.PositiveIntegerField(blank=True, null=True)),
                ("meets_academic_requirements", models.BooleanField(default=False)),
                ("meets_attendance_requirements", models.BooleanField(default=False)),
                ("meets_discipline_requirements", models.BooleanField(default=False)),
                ("promotion_date", models.DateField()),
                ("effective_date", models.DateField(blank=True, null=True)),
                ("approval_date", models.DateField(blank=True, null=True)),
                (
                    "conditions",
                    models.TextField(
                        blank=True,
                        help_text="Conditions for conditional promotion",
                        null=True,
                    ),
                ),
                ("remarks", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="promotions_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "from_grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promoted_from_grade",
                        to="academics.grade",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promotions",
                        to="students.student",
                    ),
                ),
                (
                    "to_grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promoted_to_grade",
                        to="academics.grade",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Promotion",
                "verbose_name_plural": "Student Promotions",
                "ordering": ["-academic_year", "student__last_name"],
                "unique_together": {("student", "academic_year")},
            },
        ),
    ]
