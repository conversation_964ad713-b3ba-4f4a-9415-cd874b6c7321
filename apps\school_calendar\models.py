from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, timedelta
import uuid

# Enhanced School Calendar Management System

class AcademicYearConfiguration(models.Model):
    """Academic year configuration and management"""
    YEAR_STATUSES = [
        ('upcoming', 'Upcoming'),
        ('current', 'Current'),
        ('completed', 'Completed'),
        ('archived', 'Archived'),
    ]

    id = models.AutoField(primary_key=True)
    year_name = models.CharField(max_length=50, unique=True, help_text="e.g., 2024-2025")
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=YEAR_STATUSES, default='upcoming')
    total_weeks = models.IntegerField(help_text="Total academic weeks")
    total_days = models.IntegerField(help_text="Total academic days")
    description = models.TextField(blank=True, help_text="Academic year description")
    is_current = models.Bo<PERSON>an<PERSON>ield(default=False, help_text="Mark as current academic year")
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-start_date']
        verbose_name = "Academic Year Configuration"
        verbose_name_plural = "Academic Year Configurations"

    def clean(self):
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date.")

        # Ensure only one current academic year
        if self.is_current:
            existing_current = AcademicYearConfiguration.objects.filter(is_current=True)
            if self.pk:
                existing_current = existing_current.exclude(pk=self.pk)
            if existing_current.exists():
                raise ValidationError("Only one academic year can be marked as current.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.year_name} ({self.get_status_display()})"

class TermConfiguration(models.Model):
    """Term dates management and configuration"""
    TERM_TYPES = [
        ('term_1', 'Term 1'),
        ('term_2', 'Term 2'),
        ('term_3', 'Term 3'),
        ('semester_1', 'Semester 1'),
        ('semester_2', 'Semester 2'),
        ('quarter_1', 'Quarter 1'),
        ('quarter_2', 'Quarter 2'),
        ('quarter_3', 'Quarter 3'),
        ('quarter_4', 'Quarter 4'),
    ]

    TERM_STATUSES = [
        ('upcoming', 'Upcoming'),
        ('current', 'Current'),
        ('completed', 'Completed'),
        ('break', 'Break Period'),
    ]

    id = models.AutoField(primary_key=True)
    academic_year = models.ForeignKey(AcademicYearConfiguration, on_delete=models.CASCADE, related_name='terms')
    term_name = models.CharField(max_length=20, choices=TERM_TYPES)
    display_name = models.CharField(max_length=100, help_text="Custom display name")
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=TERM_STATUSES, default='upcoming')
    total_weeks = models.IntegerField(help_text="Total term weeks")
    total_days = models.IntegerField(help_text="Total term days")
    break_start = models.DateField(null=True, blank=True, help_text="Term break start date")
    break_end = models.DateField(null=True, blank=True, help_text="Term break end date")
    is_current = models.BooleanField(default=False, help_text="Mark as current term")
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['academic_year', 'term_name']
        ordering = ['academic_year', 'start_date']
        verbose_name = "Term Configuration"
        verbose_name_plural = "Term Configurations"

    def clean(self):
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date.")

        if self.break_start and self.break_end and self.break_end <= self.break_start:
            raise ValidationError("Break end date must be after break start date.")

        # Ensure only one current term per academic year
        if self.is_current:
            existing_current = TermConfiguration.objects.filter(
                academic_year=self.academic_year,
                is_current=True
            )
            if self.pk:
                existing_current = existing_current.exclude(pk=self.pk)
            if existing_current.exists():
                raise ValidationError("Only one term can be marked as current per academic year.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.academic_year.year_name} - {self.display_name}"

class HolidaySchedule(models.Model):
    """Holiday scheduling and management"""
    HOLIDAY_TYPES = [
        ('national', 'National Holiday'),
        ('religious', 'Religious Holiday'),
        ('school', 'School Holiday'),
        ('break', 'School Break'),
        ('weekend', 'Weekend'),
        ('custom', 'Custom Holiday'),
    ]

    RECURRENCE_TYPES = [
        ('none', 'No Recurrence'),
        ('yearly', 'Yearly'),
        ('monthly', 'Monthly'),
        ('weekly', 'Weekly'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    holiday_type = models.CharField(max_length=20, choices=HOLIDAY_TYPES)
    start_date = models.DateField()
    end_date = models.DateField()
    is_recurring = models.BooleanField(default=False)
    recurrence_type = models.CharField(max_length=20, choices=RECURRENCE_TYPES, default='none')
    academic_year = models.ForeignKey(AcademicYearConfiguration, on_delete=models.CASCADE, related_name='holidays')
    affects_attendance = models.BooleanField(default=True, help_text="Does this holiday affect attendance tracking?")
    is_active = models.BooleanField(default=True)
    color_code = models.CharField(max_length=7, default='#FF6B6B', help_text="Hex color for calendar display")
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['start_date', 'name']
        verbose_name = "Holiday Schedule"
        verbose_name_plural = "Holiday Schedules"

    def clean(self):
        if self.start_date and self.end_date and self.end_date < self.start_date:
            raise ValidationError("End date cannot be before start date.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

class ExamPeriodSchedule(models.Model):
    """Exam period scheduling and management"""
    EXAM_TYPES = [
        ('continuous_assessment', 'Continuous Assessment'),
        ('mid_term', 'Mid-Term Exam'),
        ('end_term', 'End of Term Exam'),
        ('final', 'Final Exam'),
        ('mock', 'Mock Exam'),
        ('national', 'National Exam'),
        ('entrance', 'Entrance Exam'),
        ('placement', 'Placement Test'),
    ]

    EXAM_STATUSES = [
        ('scheduled', 'Scheduled'),
        ('ongoing', 'Ongoing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('postponed', 'Postponed'),
    ]

    id = models.AutoField(primary_key=True)
    exam_name = models.CharField(max_length=200)
    exam_type = models.CharField(max_length=30, choices=EXAM_TYPES)
    description = models.TextField(blank=True)
    academic_year = models.ForeignKey(AcademicYearConfiguration, on_delete=models.CASCADE, related_name='exam_periods')
    term = models.ForeignKey(TermConfiguration, on_delete=models.CASCADE, related_name='exam_periods')
    start_date = models.DateField()
    end_date = models.DateField()
    registration_start = models.DateField(null=True, blank=True)
    registration_end = models.DateField(null=True, blank=True)
    grades = models.ManyToManyField('academics.Grade', related_name='exam_periods')
    subjects = models.ManyToManyField('academics.Subject', related_name='exam_periods', blank=True)
    status = models.CharField(max_length=20, choices=EXAM_STATUSES, default='scheduled')
    instructions = models.TextField(blank=True, help_text="Special instructions for this exam period")
    color_code = models.CharField(max_length=7, default='#4ECDC4', help_text="Hex color for calendar display")
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['start_date', 'exam_name']
        verbose_name = "Exam Period Schedule"
        verbose_name_plural = "Exam Period Schedules"

    def clean(self):
        if self.start_date and self.end_date and self.end_date < self.start_date:
            raise ValidationError("End date cannot be before start date.")

        if self.registration_start and self.registration_end:
            if self.registration_end < self.registration_start:
                raise ValidationError("Registration end date cannot be before start date.")
            if self.registration_end > self.start_date:
                raise ValidationError("Registration must end before exam starts.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1

    def __str__(self):
        return f"{self.exam_name} - {self.term.display_name}"

class SchoolCalendar(models.Model):
    """Enhanced school calendar with event integration"""
    CALENDAR_TYPES = [
        ('academic', 'Academic Event'),
        ('holiday', 'Holiday'),
        ('exam', 'Exam'),
        ('event', 'School Event'),
        ('meeting', 'Meeting'),
        ('deadline', 'Deadline'),
        ('reminder', 'Reminder'),
        ('maintenance', 'Maintenance'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    VISIBILITY_LEVELS = [
        ('public', 'Public'),
        ('students', 'Students Only'),
        ('teachers', 'Teachers Only'),
        ('staff', 'Staff Only'),
        ('admin', 'Admin Only'),
        ('parents', 'Parents Only'),
        ('custom', 'Custom Groups'),
    ]

    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    is_all_day = models.BooleanField(default=False)
    event_type = models.CharField(max_length=50, choices=CALENDAR_TYPES)
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS, default='medium')
    visibility = models.CharField(max_length=20, choices=VISIBILITY_LEVELS, default='public')
    location = models.CharField(max_length=200, blank=True)

    # Academic Integration
    academic_year = models.ForeignKey(AcademicYearConfiguration, on_delete=models.CASCADE, null=True, blank=True)
    term = models.ForeignKey(TermConfiguration, on_delete=models.CASCADE, null=True, blank=True)
    grades = models.ManyToManyField('academics.Grade', blank=True, related_name='calendar_events')
    subjects = models.ManyToManyField('academics.Subject', blank=True, related_name='calendar_events')

    # Event Properties
    is_recurring = models.BooleanField(default=False)
    recurrence_rule = models.TextField(blank=True, help_text="RRULE for recurring events")
    is_holiday = models.BooleanField(default=False)
    affects_attendance = models.BooleanField(default=False)
    requires_preparation = models.BooleanField(default=False)
    preparation_days = models.IntegerField(default=0, help_text="Days needed for preparation")

    # Display Properties
    color = models.CharField(max_length=7, default='#3B82F6', help_text="Hex color code")
    icon = models.CharField(max_length=50, blank=True, help_text="Icon class name")

    # Metadata
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Legacy fields for backward compatibility
    year = models.PositiveIntegerField(null=True, blank=True)

    class Meta:
        ordering = ['start_date', 'start_time', 'title']
        verbose_name = "School Calendar Event"
        verbose_name_plural = "School Calendar Events"

    def clean(self):
        if self.start_date and self.end_date and self.end_date < self.start_date:
            raise ValidationError("End date cannot be before start date.")

        if not self.is_all_day and self.start_time and self.end_time:
            if self.start_date == self.end_date and self.end_time <= self.start_time:
                raise ValidationError("End time must be after start time for same-day events.")

    def save(self, *args, **kwargs):
        # Auto-set year from academic_year if available
        if self.academic_year and not self.year:
            self.year = self.academic_year.start_date.year
        elif not self.year:
            self.year = self.start_date.year

        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1

    @property
    def is_multi_day(self):
        return self.start_date != self.end_date

    def __str__(self):
        return f"{self.title} ({self.start_date})"

class CalendarNotification(models.Model):
    """Calendar notifications and reminders"""
    NOTIFICATION_TYPES = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('push', 'Push Notification'),
        ('in_app', 'In-App Notification'),
        ('dashboard', 'Dashboard Alert'),
    ]

    NOTIFICATION_STATUSES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    REMINDER_INTERVALS = [
        ('15_minutes', '15 Minutes Before'),
        ('30_minutes', '30 Minutes Before'),
        ('1_hour', '1 Hour Before'),
        ('2_hours', '2 Hours Before'),
        ('1_day', '1 Day Before'),
        ('2_days', '2 Days Before'),
        ('1_week', '1 Week Before'),
        ('2_weeks', '2 Weeks Before'),
    ]

    id = models.AutoField(primary_key=True)
    calendar_event = models.ForeignKey(SchoolCalendar, on_delete=models.CASCADE, related_name='notifications')
    recipient = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='calendar_notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    reminder_interval = models.CharField(max_length=20, choices=REMINDER_INTERVALS)
    scheduled_time = models.DateTimeField()
    sent_time = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=NOTIFICATION_STATUSES, default='pending')
    message_title = models.CharField(max_length=200)
    message_body = models.TextField()
    error_message = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['scheduled_time']
        unique_together = ['calendar_event', 'recipient', 'reminder_interval']
        verbose_name = "Calendar Notification"
        verbose_name_plural = "Calendar Notifications"

    def __str__(self):
        return f"{self.message_title} - {self.recipient.username}"

class CalendarSyncConfiguration(models.Model):
    """Calendar synchronization settings"""
    SYNC_PROVIDERS = [
        ('google', 'Google Calendar'),
        ('outlook', 'Microsoft Outlook'),
        ('apple', 'Apple iCloud'),
        ('ical', 'iCal/CalDAV'),
        ('exchange', 'Microsoft Exchange'),
    ]

    SYNC_DIRECTIONS = [
        ('import', 'Import Only'),
        ('export', 'Export Only'),
        ('bidirectional', 'Bidirectional'),
    ]

    SYNC_STATUSES = [
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('error', 'Error'),
        ('disabled', 'Disabled'),
    ]

    id = models.AutoField(primary_key=True)
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='calendar_syncs')
    provider = models.CharField(max_length=20, choices=SYNC_PROVIDERS)
    sync_direction = models.CharField(max_length=20, choices=SYNC_DIRECTIONS, default='export')
    calendar_name = models.CharField(max_length=200)
    external_calendar_id = models.CharField(max_length=500)
    access_token = models.TextField(blank=True)
    refresh_token = models.TextField(blank=True)
    sync_frequency = models.IntegerField(default=60, help_text="Sync frequency in minutes")
    last_sync = models.DateTimeField(null=True, blank=True)
    next_sync = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=SYNC_STATUSES, default='active')
    error_message = models.TextField(blank=True)
    sync_filters = models.JSONField(default=dict, blank=True, help_text="Filters for what to sync")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'provider', 'external_calendar_id']
        verbose_name = "Calendar Sync Configuration"
        verbose_name_plural = "Calendar Sync Configurations"

    def __str__(self):
        return f"{self.user.username} - {self.get_provider_display()}"

class UserCalendarPreferences(models.Model):
    """User-specific calendar preferences and views"""
    VIEW_TYPES = [
        ('month', 'Month View'),
        ('week', 'Week View'),
        ('day', 'Day View'),
        ('agenda', 'Agenda View'),
        ('year', 'Year View'),
    ]

    TIME_FORMATS = [
        ('12', '12-hour (AM/PM)'),
        ('24', '24-hour'),
    ]

    WEEK_STARTS = [
        ('sunday', 'Sunday'),
        ('monday', 'Monday'),
    ]

    id = models.AutoField(primary_key=True)
    user = models.OneToOneField('accounts.User', on_delete=models.CASCADE, related_name='calendar_preferences')
    default_view = models.CharField(max_length=20, choices=VIEW_TYPES, default='month')
    time_format = models.CharField(max_length=2, choices=TIME_FORMATS, default='12')
    week_starts_on = models.CharField(max_length=10, choices=WEEK_STARTS, default='monday')
    show_weekends = models.BooleanField(default=True)
    show_holidays = models.BooleanField(default=True)
    show_exams = models.BooleanField(default=True)
    show_events = models.BooleanField(default=True)

    # Notification preferences
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    push_notifications = models.BooleanField(default=True)
    default_reminder_time = models.CharField(max_length=20, choices=CalendarNotification.REMINDER_INTERVALS, default='1_day')

    # Role-based visibility
    visible_event_types = models.JSONField(default=list, help_text="List of event types visible to this user")
    visible_grades = models.ManyToManyField('academics.Grade', blank=True, related_name='calendar_viewers')
    visible_subjects = models.ManyToManyField('academics.Subject', blank=True, related_name='calendar_viewers')

    # Display preferences
    theme_color = models.CharField(max_length=7, default='#3B82F6')
    compact_view = models.BooleanField(default=False)
    show_event_details = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Calendar Preferences"
        verbose_name_plural = "User Calendar Preferences"

    def __str__(self):
        return f"{self.user.username} - Calendar Preferences"

class CalendarExportImport(models.Model):
    """Calendar export and import tracking"""
    OPERATION_TYPES = [
        ('export', 'Export'),
        ('import', 'Import'),
    ]

    EXPORT_FORMATS = [
        ('ical', 'iCal (.ics)'),
        ('csv', 'CSV (.csv)'),
        ('excel', 'Excel (.xlsx)'),
        ('pdf', 'PDF (.pdf)'),
        ('json', 'JSON (.json)'),
    ]

    OPERATION_STATUSES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    operation_type = models.CharField(max_length=10, choices=OPERATION_TYPES)
    format_type = models.CharField(max_length=20, choices=EXPORT_FORMATS)
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='calendar_operations')

    # Filter criteria
    start_date = models.DateField()
    end_date = models.DateField()
    event_types = models.JSONField(default=list, help_text="List of event types to include")
    grades = models.ManyToManyField('academics.Grade', blank=True, related_name='calendar_exports')
    subjects = models.ManyToManyField('academics.Subject', blank=True, related_name='calendar_exports')
    include_holidays = models.BooleanField(default=True)
    include_exams = models.BooleanField(default=True)
    include_events = models.BooleanField(default=True)

    # Operation details
    status = models.CharField(max_length=20, choices=OPERATION_STATUSES, default='pending')
    file_path = models.FileField(upload_to='calendar_exports/', null=True, blank=True)
    file_size = models.BigIntegerField(null=True, blank=True, help_text="File size in bytes")
    records_count = models.IntegerField(default=0, help_text="Number of records processed")
    error_message = models.TextField(blank=True)
    processing_time = models.DurationField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="When the export file expires")

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Calendar Export/Import"
        verbose_name_plural = "Calendar Exports/Imports"

    def clean(self):
        if self.start_date and self.end_date and self.end_date < self.start_date:
            raise ValidationError("End date cannot be before start date.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1

    def __str__(self):
        return f"{self.get_operation_type_display()} - {self.user.username} ({self.created_at.date()})"
