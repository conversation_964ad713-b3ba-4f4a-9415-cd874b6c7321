from django.core.management.base import BaseCommand
from django.db import transaction
from apps.teachers.models import Teacher
from datetime import date
import random
import string


class Command(BaseCommand):
    help = 'Populate required fields for existing teachers'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        teachers = Teacher.objects.all()
        
        if not teachers.exists():
            self.stdout.write(
                self.style.SUCCESS('No teachers found to update.')
            )
            return
        
        self.stdout.write(f'Found {teachers.count()} teachers to update...')
        
        updated_count = 0
        
        with transaction.atomic():
            for teacher in teachers:
                updates_made = False
                
                # Generate employee_id if missing
                if not hasattr(teacher, 'employee_id') or not teacher.employee_id:
                    teacher.employee_id = self.generate_unique_employee_id()
                    updates_made = True
                    self.stdout.write(f'  - Generated employee_id: {teacher.employee_id}')
                
                # Split full_name into first_name and last_name if missing
                if not hasattr(teacher, 'first_name') or not teacher.first_name:
                    if hasattr(teacher, 'full_name') and teacher.full_name:
                        name_parts = teacher.full_name.strip().split()
                        teacher.first_name = name_parts[0] if name_parts else 'Unknown'
                        teacher.last_name = name_parts[-1] if len(name_parts) > 1 else 'Teacher'
                        if len(name_parts) > 2:
                            teacher.middle_name = ' '.join(name_parts[1:-1])
                    else:
                        teacher.first_name = 'Unknown'
                        teacher.last_name = 'Teacher'
                    updates_made = True
                    self.stdout.write(f'  - Set names: {teacher.first_name} {teacher.last_name}')
                
                # Set default date_of_birth if missing
                if not hasattr(teacher, 'date_of_birth') or not teacher.date_of_birth:
                    # Set a reasonable default (30 years ago)
                    teacher.date_of_birth = date(date.today().year - 30, 1, 1)
                    updates_made = True
                    self.stdout.write(f'  - Set default date_of_birth: {teacher.date_of_birth}')
                
                # Set default employment_date if missing
                if not hasattr(teacher, 'employment_date') or not teacher.employment_date:
                    # Use reg_date if available, otherwise use today
                    if hasattr(teacher, 'reg_date') and teacher.reg_date:
                        teacher.employment_date = teacher.reg_date
                    else:
                        teacher.employment_date = date.today()
                    updates_made = True
                    self.stdout.write(f'  - Set employment_date: {teacher.employment_date}')
                
                # Generate national_id if missing
                if not hasattr(teacher, 'national_id') or not teacher.national_id:
                    teacher.national_id = self.generate_unique_national_id()
                    updates_made = True
                    self.stdout.write(f'  - Generated national_id: {teacher.national_id}')
                
                # Set default gender if missing
                if not hasattr(teacher, 'gender') or not teacher.gender:
                    # Try to guess from name or set default
                    teacher.gender = 'Male'  # Default, can be updated later
                    updates_made = True
                    self.stdout.write(f'  - Set default gender: {teacher.gender}')
                
                # Ensure email is unique and valid
                if not hasattr(teacher, 'email') or not teacher.email or '@' not in teacher.email:
                    base_email = f"{teacher.first_name.lower()}.{teacher.last_name.lower()}"
                    teacher.email = self.generate_unique_email(base_email)
                    updates_made = True
                    self.stdout.write(f'  - Generated email: {teacher.email}')
                
                # Set default phone if missing
                if not hasattr(teacher, 'phone') or not teacher.phone:
                    teacher.phone = f"0700{random.randint(100000, 999999)}"
                    updates_made = True
                    self.stdout.write(f'  - Generated phone: {teacher.phone}')
                
                # Set default address if missing
                if not hasattr(teacher, 'address') or not teacher.address:
                    teacher.address = "Address not provided"
                    updates_made = True
                    self.stdout.write(f'  - Set default address')
                
                # Set employment status defaults
                if not hasattr(teacher, 'employment_status') or not teacher.employment_status:
                    teacher.employment_status = 'confirmed'
                    updates_made = True
                
                if not hasattr(teacher, 'employment_type') or not teacher.employment_type:
                    teacher.employment_type = 'TSC'
                    updates_made = True
                
                # Set workload defaults
                if not hasattr(teacher, 'current_teaching_load') or teacher.current_teaching_load is None:
                    teacher.current_teaching_load = 0
                    updates_made = True
                
                if not hasattr(teacher, 'maximum_teaching_load') or teacher.maximum_teaching_load is None:
                    teacher.maximum_teaching_load = 30
                    updates_made = True
                
                # Set boolean defaults
                if not hasattr(teacher, 'is_department_head'):
                    teacher.is_department_head = False
                    updates_made = True
                
                if not hasattr(teacher, 'is_class_teacher'):
                    teacher.is_class_teacher = False
                    updates_made = True
                
                if updates_made:
                    if not dry_run:
                        teacher.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Updated teacher: {teacher.full_name or teacher.first_name + " " + teacher.last_name}')
                    )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: Would have updated {updated_count} teachers')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {updated_count} teachers')
            )
    
    def generate_unique_employee_id(self):
        """Generate a unique employee ID"""
        while True:
            emp_id = f"EMP{random.randint(1000, 9999)}"
            if not Teacher.objects.filter(employee_id=emp_id).exists():
                return emp_id
    
    def generate_unique_national_id(self):
        """Generate a unique national ID (temporary)"""
        while True:
            national_id = f"{random.randint(10000000, 99999999)}"
            if not Teacher.objects.filter(national_id=national_id).exists():
                return national_id
    
    def generate_unique_email(self, base_email):
        """Generate a unique email address"""
        domain = "@school.edu"
        counter = 1
        
        # Clean the base email
        base_email = ''.join(c for c in base_email if c.isalnum() or c in '._-').lower()
        
        while True:
            if counter == 1:
                email = f"{base_email}{domain}"
            else:
                email = f"{base_email}{counter}{domain}"
            
            if not Teacher.objects.filter(email=email).exists():
                return email
            counter += 1
