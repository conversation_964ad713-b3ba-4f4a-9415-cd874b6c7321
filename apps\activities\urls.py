from django.urls import path
from . import views

app_name = 'activities'

urlpatterns = [
    # Dashboard
    path('', views.activities_dashboard, name='dashboard'),
    
    # Activity Management
    path('activities/', views.activities_list, name='activities_list'),
    path('activities/<int:activity_id>/', views.activity_detail, name='activity_detail'),
    path('activities/add/', views.add_activity, name='add_activity'),
    path('activities/<int:activity_id>/edit/', views.edit_activity, name='edit_activity'),
    
    # Student Participation Management
    path('participants/', views.participants_list, name='participants_list'),
    path('participants/<int:participant_id>/', views.participant_detail, name='participant_detail'),
]
