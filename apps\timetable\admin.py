from django.contrib import admin
from .models import Timetable

@admin.register(Timetable)
class TimetableAdmin(admin.ModelAdmin):
    list_display = ('id', 'grade', 'day', 'subject', 'teacher', 'classroom', 'start_time', 'end_time', 'period_number', 'is_break')
    search_fields = ('grade__name', 'day', 'subject__name', 'teacher__full_name', 'classroom__name')
    list_filter = ('grade', 'day', 'subject', 'teacher', 'classroom', 'is_break')
    ordering = ('grade', 'day', 'period_number')
