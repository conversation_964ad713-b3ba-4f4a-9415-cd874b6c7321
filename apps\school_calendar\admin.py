from django.contrib import admin
from .models import (
    AcademicYearConfiguration, TermConfiguration, HolidaySchedule,
    ExamPeriodSchedule, SchoolCalendar, CalendarNotification,
    CalendarSyncConfiguration, UserCalendarPreferences, CalendarExportImport
)

@admin.register(AcademicYearConfiguration)
class AcademicYearConfigurationAdmin(admin.ModelAdmin):
    list_display = ('id', 'year_name', 'start_date', 'end_date', 'status', 'is_current', 'total_weeks', 'total_days', 'created_at')
    search_fields = ('year_name', 'description')
    list_filter = ('status', 'is_current', 'created_at')
    ordering = ('-start_date', 'year_name')

@admin.register(TermConfiguration)
class TermConfigurationAdmin(admin.ModelAdmin):
    list_display = ('id', 'academic_year', 'term_name', 'display_name', 'start_date', 'end_date', 'status', 'is_current', 'total_weeks')
    search_fields = ('display_name', 'description')
    list_filter = ('term_name', 'status', 'is_current', 'academic_year', 'created_at')
    ordering = ('academic_year', 'start_date')

@admin.register(HolidaySchedule)
class HolidayScheduleAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'holiday_type', 'start_date', 'end_date', 'is_recurring', 'affects_attendance', 'is_active', 'academic_year')
    search_fields = ('name', 'description')
    list_filter = ('holiday_type', 'is_recurring', 'affects_attendance', 'is_active', 'academic_year', 'created_at')
    ordering = ('-start_date', 'name')

@admin.register(ExamPeriodSchedule)
class ExamPeriodScheduleAdmin(admin.ModelAdmin):
    list_display = ('id', 'exam_name', 'exam_type', 'academic_year', 'term', 'start_date', 'end_date', 'status')
    search_fields = ('exam_name', 'description', 'instructions')
    list_filter = ('exam_type', 'status', 'academic_year', 'term', 'created_at')
    filter_horizontal = ('grades', 'subjects')
    ordering = ('-start_date', 'exam_name')

@admin.register(SchoolCalendar)
class SchoolCalendarAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'start_date', 'end_date', 'event_type', 'priority', 'visibility', 'is_all_day', 'academic_year', 'created_at')
    search_fields = ('title', 'description', 'location')
    list_filter = ('event_type', 'priority', 'visibility', 'is_all_day', 'is_holiday', 'affects_attendance', 'academic_year', 'term', 'created_at')
    filter_horizontal = ('grades', 'subjects')
    ordering = ('-created_at', 'start_date')

@admin.register(CalendarNotification)
class CalendarNotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'calendar_event', 'recipient', 'notification_type', 'reminder_interval', 'scheduled_time', 'status', 'sent_time')
    search_fields = ('message_title', 'message_body', 'recipient__username')
    list_filter = ('notification_type', 'reminder_interval', 'status', 'created_at')
    ordering = ('-scheduled_time', 'created_at')

@admin.register(CalendarSyncConfiguration)
class CalendarSyncConfigurationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'provider', 'sync_direction', 'calendar_name', 'status', 'last_sync', 'next_sync')
    search_fields = ('user__username', 'calendar_name', 'external_calendar_id')
    list_filter = ('provider', 'sync_direction', 'status', 'created_at')
    ordering = ('-created_at', 'user')

@admin.register(UserCalendarPreferences)
class UserCalendarPreferencesAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'default_view', 'time_format', 'week_starts_on', 'email_notifications', 'push_notifications', 'created_at')
    search_fields = ('user__username',)
    list_filter = ('default_view', 'time_format', 'week_starts_on', 'email_notifications', 'push_notifications', 'created_at')
    filter_horizontal = ('visible_grades', 'visible_subjects')
    ordering = ('user__username',)

@admin.register(CalendarExportImport)
class CalendarExportImportAdmin(admin.ModelAdmin):
    list_display = ('id', 'operation_type', 'format_type', 'user', 'start_date', 'end_date', 'status', 'records_count', 'created_at')
    search_fields = ('user__username',)
    list_filter = ('operation_type', 'format_type', 'status', 'include_holidays', 'include_exams', 'include_events', 'created_at')
    filter_horizontal = ('grades', 'subjects')
    ordering = ('-created_at', 'user')
