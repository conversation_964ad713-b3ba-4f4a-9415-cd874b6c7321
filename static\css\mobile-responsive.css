/* Mobile-Responsive Design Optimization */

/* Base responsive breakpoints */
:root {
    --mobile-breakpoint: 768px;
    --tablet-breakpoint: 1024px;
    --desktop-breakpoint: 1200px;
}

/* Mobile-first approach */
@media (max-width: 767px) {
    /* Navigation adjustments */
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        position: fixed;
        z-index: 1000;
        height: 100vh;
        width: 280px;
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.5rem;
        border-radius: 0.25rem;
        cursor: pointer;
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    /* Dashboard cards */
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dashboard-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    /* Tables */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table {
        min-width: 600px;
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        white-space: nowrap;
    }
    
    /* Forms */
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    /* Buttons */
    .btn-group {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Modal adjustments */
    .modal {
        padding: 1rem;
    }
    
    .modal-content {
        margin: 0;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    /* Search and filters */
    .search-filters {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-input {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    /* Statistics cards */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .stat-card {
        padding: 1rem;
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .sidebar {
        width: 240px;
    }
    
    .main-content {
        margin-left: 240px;
    }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .large-stats-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .form-input,
    .form-select {
        min-height: 44px; /* iOS recommended touch target */
    }
    
    .table-row {
        cursor: pointer;
    }
    
    .table-row:hover {
        background-color: var(--hover-color);
    }
}

/* Print styles */
@media print {
    .sidebar,
    .mobile-menu-toggle,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .table {
        font-size: 12px;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
        --border-color: #404040;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card,
    .dashboard-card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .form-input,
    .form-select {
        border: 2px solid currentColor;
    }
    
    .table th,
    .table td {
        border: 1px solid currentColor;
    }
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive utilities */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

@media (max-width: 767px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
}

/* Flexible grid system */
.responsive-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 767px) {
    .responsive-grid {
        grid-template-columns: 1fr;
    }
}

/* Responsive typography */
.responsive-text {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
}

.responsive-heading {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
}

/* Swipe gestures for mobile */
.swipeable {
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
}
