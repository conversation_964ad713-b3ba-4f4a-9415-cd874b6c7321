from django.db import models

# Create your models here.

class Grade(models.Model):
    """
    Represents a grade/class level with section, level, and fee details.
    """
    SECTION_CHOICES = [
        ('A', 'A'),
        ('B', 'B'),
        ('C', 'C'),
    ]
    LEVEL_CHOICES = [
        ('Primary', 'Primary'),
        ('Secondary', 'Secondary'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    admission_fee = models.DecimalField(max_digits=11, decimal_places=2)
    hall_charge = models.IntegerField()
    section = models.CharField(max_length=50, null=True, blank=True, choices=SECTION_CHOICES)
    class_teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, blank=True, related_name='class_teacher_grades')
    minimum_subjects = models.IntegerField(default=8)
    level = models.CharField(max_length=50, null=True, blank=True, choices=LEVEL_CHOICES)
    boarding_fee = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    is_boarding = models.BooleanField(default=False)

    def __str__(self):
        return self.name

class Department(models.Model):
    """
    Represents an academic department with a head teacher and description.
    """
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    head_teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, blank=True, related_name='headed_departments')
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class Subject(models.Model):
    """
    Represents a subject with code, description, and department.
    """
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=20, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    is_optional = models.BooleanField(default=False)
    lessons_per_week = models.IntegerField(default=5)

    def __str__(self):
        return self.name

class SubjectRouting(models.Model):
    """
    Maps a subject to a grade and teacher, with fee and mandatory status.
    """
    id = models.AutoField(primary_key=True)
    grade = models.ForeignKey(Grade, on_delete=models.CASCADE)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE)
    fee = models.DecimalField(max_digits=11, decimal_places=2)
    is_mandatory = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.grade.name} - {self.subject.name}"

class ClassRoom(models.Model):
    """
    Represents a physical classroom with capacity and facilities.
    """
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    student_count = models.IntegerField(default=0)
    capacity = models.IntegerField(default=40)
    section = models.CharField(max_length=50, null=True, blank=True)
    building = models.CharField(max_length=100, null=True, blank=True)
    floor = models.CharField(max_length=20, null=True, blank=True)
    room_number = models.CharField(max_length=20, null=True, blank=True)
    has_projector = models.BooleanField(default=False)
    has_ac = models.BooleanField(default=False)
    class_teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, blank=True, related_name='classrooms')

    def __str__(self):
        return self.name
