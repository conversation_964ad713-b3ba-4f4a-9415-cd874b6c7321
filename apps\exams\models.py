from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta
import uuid

# Enhanced Exam Management System

class ExamType(models.Model):
    """Enhanced exam type configuration"""
    TYPE_CATEGORIES = [
        ('continuous_assessment', 'Continuous Assessment'),
        ('mid_term', 'Mid-Term Examination'),
        ('end_term', 'End-Term Examination'),
        ('annual', 'Annual Examination'),
        ('mock', 'Mock Examination'),
        ('national', 'National Examination'),
        ('entrance', 'Entrance Examination'),
        ('placement', 'Placement Test'),
        ('diagnostic', 'Diagnostic Test'),
        ('remedial', 'Remedial Test'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, unique=True)
    category = models.CharField(max_length=30, choices=TYPE_CATEGORIES)
    description = models.TextField(blank=True)
    default_duration_minutes = models.IntegerField(default=120)
    default_total_marks = models.IntegerField(default=100)
    weight_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    requires_timetable = models.BooleanField(default=True)
    allows_retake = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"

class Exam(models.Model):
    """Enhanced exam model with comprehensive configuration"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('postponed', 'Postponed'),
    ]

    MARKING_SCHEMES = [
        ('percentage', 'Percentage (0-100)'),
        ('points', 'Points System'),
        ('letter_grade', 'Letter Grades'),
        ('gpa', 'GPA System'),
    ]

    id = models.AutoField(primary_key=True)
    exam_code = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=255)
    exam_type = models.ForeignKey(ExamType, on_delete=models.CASCADE, related_name='exams')
    academic_year = models.IntegerField()
    term = models.IntegerField(default=1)

    # Exam configuration
    description = models.TextField(blank=True)
    instructions = models.TextField(blank=True)
    total_marks = models.IntegerField(default=100)
    pass_marks = models.IntegerField(default=40)
    duration_minutes = models.IntegerField(default=120)
    marking_scheme = models.CharField(max_length=20, choices=MARKING_SCHEMES, default='percentage')

    # Dates and timing
    start_date = models.DateField()
    end_date = models.DateField()
    registration_start = models.DateField(null=True, blank=True)
    registration_end = models.DateField(null=True, blank=True)
    results_release_date = models.DateField(null=True, blank=True)

    # Scope and applicability
    grades = models.ManyToManyField('academics.Grade', related_name='exams')
    subjects = models.ManyToManyField('academics.Subject', related_name='exams')

    # Status and publishing
    status = models.CharField(max_length=20, default='draft', choices=STATUS_CHOICES)
    is_published = models.BooleanField(default=False)
    results_published = models.BooleanField(default=False)

    # Security and integrity
    allow_calculator = models.BooleanField(default=False)
    allow_reference_materials = models.BooleanField(default=False)
    requires_signature = models.BooleanField(default=True)

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-academic_year', '-term', 'start_date']
        unique_together = ['exam_code', 'academic_year']

    def save(self, *args, **kwargs):
        if not self.exam_code:
            self.exam_code = self.generate_exam_code()
        super().save(*args, **kwargs)

    def generate_exam_code(self):
        """Generate unique exam code"""
        year_short = str(self.academic_year)[-2:]
        type_code = self.exam_type.name[:3].upper()
        count = Exam.objects.filter(academic_year=self.academic_year).count() + 1
        return f"EX{year_short}{type_code}{count:03d}"

    @property
    def is_active(self):
        """Check if exam is currently active"""
        today = date.today()
        return self.status == 'active' and self.start_date <= today <= self.end_date

    @property
    def is_upcoming(self):
        """Check if exam is upcoming"""
        return self.start_date > date.today()

    @property
    def days_until_exam(self):
        """Calculate days until exam starts"""
        if self.is_upcoming:
            return (self.start_date - date.today()).days
        return 0

    def __str__(self):
        return f"{self.name} ({self.academic_year})"

class GradingSystem(models.Model):
    """Enhanced grading system configuration"""
    SYSTEM_TYPES = [
        ('percentage', 'Percentage System'),
        ('letter_grade', 'Letter Grade System'),
        ('gpa_4', '4-Point GPA System'),
        ('gpa_5', '5-Point GPA System'),
        ('points', 'Points System'),
        ('custom', 'Custom System'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, unique=True)
    system_type = models.CharField(max_length=20, choices=SYSTEM_TYPES)
    description = models.TextField(blank=True)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    # Grade calculation settings
    use_weighted_average = models.BooleanField(default=False)
    round_to_decimal_places = models.IntegerField(default=2)

    # Applicable to
    grades = models.ManyToManyField('academics.Grade', related_name='grading_systems')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def save(self, *args, **kwargs):
        # Ensure only one default grading system
        if self.is_default:
            GradingSystem.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.get_system_type_display()})"

class GradeScale(models.Model):
    """Individual grade scale entries for grading systems"""
    id = models.AutoField(primary_key=True)
    grading_system = models.ForeignKey(GradingSystem, on_delete=models.CASCADE, related_name='grade_scales')

    # Grade definition
    letter_grade = models.CharField(max_length=5)  # A+, A, B+, etc.
    grade_point = models.DecimalField(max_digits=4, decimal_places=2)
    min_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    max_percentage = models.DecimalField(max_digits=5, decimal_places=2)

    # Grade properties
    description = models.CharField(max_length=100, blank=True)  # Excellent, Good, etc.
    remarks = models.CharField(max_length=255, blank=True)
    is_passing_grade = models.BooleanField(default=True)
    color_code = models.CharField(max_length=7, default='#3B82F6')

    class Meta:
        ordering = ['-min_percentage']
        unique_together = ['grading_system', 'letter_grade']

    def __str__(self):
        return f"{self.letter_grade} ({self.min_percentage}-{self.max_percentage}%)"

class ExamRangeGrade(models.Model):
    """Legacy grading model - kept for backward compatibility"""
    id = models.AutoField(primary_key=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    mark_range = models.CharField(max_length=255)
    _from = models.IntegerField()
    _to = models.IntegerField()
    mark_grade = models.CharField(max_length=255)
    grade_point = models.DecimalField(max_digits=3, decimal_places=1, null=True, blank=True)
    remarks = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"{self.grade.name} - {self.mark_grade}"

class ExamTimetable(models.Model):
    """Enhanced exam timetable with comprehensive scheduling"""
    SESSION_TYPES = [
        ('morning', 'Morning Session'),
        ('afternoon', 'Afternoon Session'),
        ('evening', 'Evening Session'),
    ]

    TIMETABLE_STATUS = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('modified', 'Modified'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name='timetable_entries')
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)

    # Scheduling details
    exam_date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    duration_minutes = models.IntegerField()
    session_type = models.CharField(max_length=20, choices=SESSION_TYPES, default='morning')

    # Venue and supervision
    classroom = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    additional_venues = models.ManyToManyField('academics.ClassRoom', related_name='additional_exam_venues', blank=True)
    chief_invigilator = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, related_name='chief_invigilated_exams')
    assistant_invigilators = models.ManyToManyField('teachers.Teacher', related_name='assistant_invigilated_exams', blank=True)

    # Exam configuration
    total_marks = models.IntegerField(default=100)
    instructions = models.TextField(blank=True)
    special_requirements = models.TextField(blank=True)
    materials_allowed = models.TextField(blank=True)
    materials_provided = models.TextField(blank=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=TIMETABLE_STATUS, default='draft')
    is_published = models.BooleanField(default=False)
    published_at = models.DateTimeField(null=True, blank=True)

    # Attendance tracking
    expected_candidates = models.IntegerField(default=0)
    actual_candidates = models.IntegerField(default=0)
    absentees = models.IntegerField(default=0)

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['exam_date', 'start_time', 'grade', 'subject']
        unique_together = ['exam', 'grade', 'subject']

    def clean(self):
        """Validate timetable entry"""
        if self.start_time >= self.end_time:
            raise ValidationError("Start time must be before end time")

        if self.exam_date < self.exam.start_date or self.exam_date > self.exam.end_date:
            raise ValidationError("Exam date must be within exam period")

    @property
    def calculated_duration(self):
        """Calculate duration in minutes"""
        if self.start_time and self.end_time:
            start_minutes = self.start_time.hour * 60 + self.start_time.minute
            end_minutes = self.end_time.hour * 60 + self.end_time.minute
            return end_minutes - start_minutes
        return 0

    @property
    def attendance_percentage(self):
        """Calculate attendance percentage"""
        if self.expected_candidates > 0:
            return (self.actual_candidates / self.expected_candidates) * 100
        return 0

    def __str__(self):
        return f"{self.exam.name} - {self.subject.name} ({self.grade.name})"

class EnhancedStudentExamResult(models.Model):
    """Enhanced student exam results with comprehensive tracking"""
    RESULT_STATUS = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('verified', 'Verified'),
        ('published', 'Published'),
        ('disputed', 'Disputed'),
        ('corrected', 'Corrected'),
    ]

    ATTENDANCE_STATUS = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('excused', 'Excused'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='exam_results')
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name='student_results')
    timetable_entry = models.ForeignKey(ExamTimetable, on_delete=models.CASCADE, related_name='student_results')

    # Marks and grading
    marks_obtained = models.DecimalField(max_digits=6, decimal_places=2)
    total_marks = models.DecimalField(max_digits=6, decimal_places=2)
    percentage = models.DecimalField(max_digits=5, decimal_places=2)
    letter_grade = models.CharField(max_length=5, blank=True)
    grade_point = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    # Performance metrics
    subject_position = models.IntegerField(null=True, blank=True)
    grade_position = models.IntegerField(null=True, blank=True)
    percentile = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    # Exam attendance and conduct
    attendance_status = models.CharField(max_length=20, choices=ATTENDANCE_STATUS, default='present')
    start_time = models.TimeField(null=True, blank=True)
    submission_time = models.TimeField(null=True, blank=True)
    time_taken_minutes = models.IntegerField(null=True, blank=True)

    # Quality and verification
    status = models.CharField(max_length=20, choices=RESULT_STATUS, default='draft')
    is_verified = models.BooleanField(default=False)
    verification_notes = models.TextField(blank=True)

    # Marking details
    marked_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='marked_exams')
    verified_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_exams')
    marked_date = models.DateTimeField(null=True, blank=True)
    verified_date = models.DateTimeField(null=True, blank=True)

    # Additional information
    teacher_remarks = models.TextField(blank=True)
    special_considerations = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-exam__start_date', 'student__full_name']
        unique_together = ['student', 'exam', 'timetable_entry']

    def save(self, *args, **kwargs):
        # Calculate percentage
        if self.total_marks > 0:
            self.percentage = (self.marks_obtained / self.total_marks) * 100

        # Calculate time taken
        if self.start_time and self.submission_time:
            start_minutes = self.start_time.hour * 60 + self.start_time.minute
            end_minutes = self.submission_time.hour * 60 + self.submission_time.minute
            self.time_taken_minutes = end_minutes - start_minutes

        super().save(*args, **kwargs)

    @property
    def is_pass(self):
        """Check if student passed the exam"""
        return self.percentage >= self.exam.pass_marks

    @property
    def performance_level(self):
        """Get performance level description"""
        if self.percentage >= 90:
            return "Excellent"
        elif self.percentage >= 80:
            return "Very Good"
        elif self.percentage >= 70:
            return "Good"
        elif self.percentage >= 60:
            return "Satisfactory"
        elif self.percentage >= self.exam.pass_marks:
            return "Pass"
        else:
            return "Fail"

    def __str__(self):
        return f"{self.student.full_name} - {self.timetable_entry.subject.name} ({self.percentage}%)"

class StudentExam(models.Model):
    """Legacy student exam model - kept for backward compatibility"""
    POSITION_CHOICES = [
        ('First', 'First'),
        ('Second', 'Second'),
        ('Third', 'Third'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    marks = models.CharField(max_length=255)
    grade = models.CharField(max_length=10, null=True, blank=True)
    remarks = models.CharField(max_length=255, null=True, blank=True)
    position_in_subject = models.CharField(max_length=10, null=True, blank=True, choices=POSITION_CHOICES)
    year = models.PositiveIntegerField()
    date = models.DateField()
    recorded_by = models.BigIntegerField(null=True, blank=True)
    verified = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.index_number} - {self.subject.name}"

class StudentOverallExamResult(models.Model):
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE)
    total_marks = models.DecimalField(max_digits=11, decimal_places=2)
    average = models.DecimalField(max_digits=11, decimal_places=2)
    grade = models.CharField(max_length=10, null=True, blank=True)
    position_in_class = models.IntegerField(null=True, blank=True)
    class_teacher_remarks = models.TextField(null=True, blank=True)
    principal_remarks = models.TextField(null=True, blank=True)
    attendance_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    previous_average = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    deviation = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    year = models.PositiveIntegerField()
    term = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Overall Result - {self.index_number}"

# Enhanced Performance Analysis and Reporting Models

class StudentOverallPerformance(models.Model):
    """Comprehensive student overall performance tracking"""
    PERFORMANCE_TRENDS = [
        ('improving', 'Improving'),
        ('declining', 'Declining'),
        ('stable', 'Stable'),
        ('fluctuating', 'Fluctuating'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='overall_performances')
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name='overall_performances')

    # Overall performance metrics
    total_subjects = models.IntegerField()
    subjects_passed = models.IntegerField()
    subjects_failed = models.IntegerField()
    total_marks_obtained = models.DecimalField(max_digits=8, decimal_places=2)
    total_marks_possible = models.DecimalField(max_digits=8, decimal_places=2)
    overall_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    overall_grade = models.CharField(max_length=5, blank=True)
    overall_gpa = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    # Rankings and positions
    class_position = models.IntegerField(null=True, blank=True)
    grade_position = models.IntegerField(null=True, blank=True)
    school_position = models.IntegerField(null=True, blank=True)
    total_students_in_class = models.IntegerField(default=0)
    total_students_in_grade = models.IntegerField(default=0)

    # Performance analysis
    highest_subject_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    lowest_subject_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    best_subject = models.ForeignKey('academics.Subject', on_delete=models.SET_NULL, null=True, blank=True, related_name='best_performances')
    weakest_subject = models.ForeignKey('academics.Subject', on_delete=models.SET_NULL, null=True, blank=True, related_name='weakest_performances')

    # Trend analysis
    previous_exam_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    percentage_change = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    performance_trend = models.CharField(max_length=20, choices=PERFORMANCE_TRENDS, blank=True)

    # Attendance correlation
    attendance_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    # Remarks and recommendations
    class_teacher_remarks = models.TextField(blank=True)
    principal_remarks = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)

    # Status
    is_promoted = models.BooleanField(null=True, blank=True)
    promotion_status = models.CharField(max_length=100, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-exam__start_date', 'class_position']
        unique_together = ['student', 'exam']

    @property
    def pass_percentage(self):
        """Calculate percentage of subjects passed"""
        if self.total_subjects and self.total_subjects > 0 and self.subjects_passed is not None:
            return (self.subjects_passed / self.total_subjects) * 100
        return 0

    @property
    def performance_category(self):
        """Get performance category"""
        if self.overall_percentage is None:
            return "Not Calculated"
        elif self.overall_percentage >= 90:
            return "Distinction"
        elif self.overall_percentage >= 80:
            return "Credit"
        elif self.overall_percentage >= 70:
            return "Merit"
        elif self.overall_percentage >= 60:
            return "Pass"
        else:
            return "Fail"

    def __str__(self):
        percentage = self.overall_percentage if self.overall_percentage is not None else "N/A"
        return f"{self.student.full_name} - {self.exam.name} ({percentage}%)"

class SubjectPerformanceAnalysis(models.Model):
    """Subject-wise performance analysis"""
    id = models.AutoField(primary_key=True)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name='subject_analyses')
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, related_name='performance_analyses')
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, related_name='subject_analyses')

    # Subject statistics
    total_students = models.IntegerField()
    students_present = models.IntegerField()
    students_passed = models.IntegerField()
    students_failed = models.IntegerField()

    # Score statistics
    highest_score = models.DecimalField(max_digits=5, decimal_places=2)
    lowest_score = models.DecimalField(max_digits=5, decimal_places=2)
    average_score = models.DecimalField(max_digits=5, decimal_places=2)
    median_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    standard_deviation = models.DecimalField(max_digits=6, decimal_places=3, null=True, blank=True)

    # Performance distribution
    grade_a_count = models.IntegerField(default=0)
    grade_b_count = models.IntegerField(default=0)
    grade_c_count = models.IntegerField(default=0)
    grade_d_count = models.IntegerField(default=0)
    grade_f_count = models.IntegerField(default=0)

    # Analysis metrics
    pass_rate = models.DecimalField(max_digits=5, decimal_places=2)
    attendance_rate = models.DecimalField(max_digits=5, decimal_places=2)
    difficulty_index = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    # Teacher analysis
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, blank=True)
    teacher_remarks = models.TextField(blank=True)
    improvement_suggestions = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-exam__start_date', 'subject__name']
        unique_together = ['exam', 'subject', 'grade']

    @property
    def performance_rating(self):
        """Get subject performance rating"""
        if self.average_score is None:
            return "Not Calculated"
        elif self.average_score >= 85:
            return "Excellent"
        elif self.average_score >= 75:
            return "Very Good"
        elif self.average_score >= 65:
            return "Good"
        elif self.average_score >= 55:
            return "Satisfactory"
        else:
            return "Needs Improvement"

    def __str__(self):
        return f"{self.subject.name} - {self.grade.name} ({self.exam.name})"

class ReportCard(models.Model):
    """Report card generation and management"""
    REPORT_TYPES = [
        ('term_report', 'Term Report'),
        ('annual_report', 'Annual Report'),
        ('progress_report', 'Progress Report'),
        ('transcript', 'Academic Transcript'),
    ]

    REPORT_STATUS = [
        ('draft', 'Draft'),
        ('generated', 'Generated'),
        ('reviewed', 'Reviewed'),
        ('approved', 'Approved'),
        ('distributed', 'Distributed'),
    ]

    id = models.AutoField(primary_key=True)
    report_number = models.CharField(max_length=100, unique=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='report_cards')
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name='report_cards')
    overall_performance = models.OneToOneField(StudentOverallPerformance, on_delete=models.CASCADE, related_name='report_card')

    # Report configuration
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, default='term_report')
    academic_year = models.IntegerField()
    term = models.IntegerField()

    # Report content
    include_subject_details = models.BooleanField(default=True)
    include_attendance = models.BooleanField(default=True)
    include_conduct_grades = models.BooleanField(default=True)
    include_teacher_remarks = models.BooleanField(default=True)
    include_recommendations = models.BooleanField(default=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=REPORT_STATUS, default='draft')
    generated_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    distributed_at = models.DateTimeField(null=True, blank=True)

    # File management
    pdf_file = models.FileField(upload_to='report_cards/', blank=True)
    file_size = models.IntegerField(default=0)

    # People involved
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='generated_reports')
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_reports')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-academic_year', '-term', 'student__full_name']
        unique_together = ['student', 'exam', 'report_type']

    def save(self, *args, **kwargs):
        if not self.report_number:
            self.report_number = self.generate_report_number()
        super().save(*args, **kwargs)

    def generate_report_number(self):
        """Generate unique report number"""
        year_short = str(self.academic_year)[-2:]
        term_code = f"T{self.term}"
        count = ReportCard.objects.filter(academic_year=self.academic_year, term=self.term).count() + 1
        return f"RC{year_short}{term_code}{count:06d}"

    def __str__(self):
        return f"{self.report_number} - {self.student.full_name}"

class PerformanceComparison(models.Model):
    """Performance comparison across terms and years"""
    COMPARISON_TYPES = [
        ('term_to_term', 'Term to Term'),
        ('year_to_year', 'Year to Year'),
        ('subject_progression', 'Subject Progression'),
        ('peer_comparison', 'Peer Comparison'),
    ]

    id = models.AutoField(primary_key=True)
    comparison_name = models.CharField(max_length=200)
    comparison_type = models.CharField(max_length=20, choices=COMPARISON_TYPES)
    description = models.TextField(blank=True)

    # Comparison scope
    students = models.ManyToManyField('students.Student', related_name='performance_comparisons')
    exams = models.ManyToManyField(Exam, related_name='performance_comparisons')
    subjects = models.ManyToManyField('academics.Subject', related_name='performance_comparisons', blank=True)
    grades = models.ManyToManyField('academics.Grade', related_name='performance_comparisons', blank=True)

    # Analysis results
    total_students_analyzed = models.IntegerField(default=0)
    improvement_count = models.IntegerField(default=0)
    decline_count = models.IntegerField(default=0)
    stable_count = models.IntegerField(default=0)

    # Statistical analysis
    average_improvement = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    highest_improvement = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    highest_decline = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)

    # Report generation
    analysis_report = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)

    # Metadata
    analyzed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    analyzed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-analyzed_at']

    def __str__(self):
        return f"{self.comparison_name} ({self.get_comparison_type_display()})"

class ClassRanking(models.Model):
    """Class ranking system"""
    RANKING_CRITERIA = [
        ('overall_average', 'Overall Average'),
        ('total_marks', 'Total Marks'),
        ('gpa', 'GPA'),
        ('weighted_average', 'Weighted Average'),
    ]

    id = models.AutoField(primary_key=True)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name='class_rankings')
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, related_name='class_rankings')

    # Ranking configuration
    ranking_criteria = models.CharField(max_length=20, choices=RANKING_CRITERIA, default='overall_average')
    include_absent_students = models.BooleanField(default=False)
    minimum_subjects_required = models.IntegerField(default=1)

    # Ranking statistics
    total_students_ranked = models.IntegerField()
    highest_score = models.DecimalField(max_digits=6, decimal_places=2)
    lowest_score = models.DecimalField(max_digits=6, decimal_places=2)
    class_average = models.DecimalField(max_digits=6, decimal_places=2)

    # Generation details
    generated_at = models.DateTimeField(auto_now_add=True)
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)

    class Meta:
        ordering = ['-exam__start_date', 'grade']
        unique_together = ['exam', 'grade', 'ranking_criteria']

    def __str__(self):
        return f"{self.grade.name} Ranking - {self.exam.name}"

class StudentRanking(models.Model):
    """Individual student ranking records"""
    id = models.AutoField(primary_key=True)
    class_ranking = models.ForeignKey(ClassRanking, on_delete=models.CASCADE, related_name='student_rankings')
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='rankings')
    overall_performance = models.ForeignKey(StudentOverallPerformance, on_delete=models.CASCADE, related_name='rankings')

    # Ranking details
    position = models.IntegerField()
    score = models.DecimalField(max_digits=6, decimal_places=2)
    percentile = models.DecimalField(max_digits=5, decimal_places=2)

    # Performance indicators
    subjects_count = models.IntegerField()
    average_score = models.DecimalField(max_digits=5, decimal_places=2)
    improvement_from_previous = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)

    class Meta:
        ordering = ['position']
        unique_together = ['class_ranking', 'student']

    def __str__(self):
        return f"{self.student.full_name} - Position {self.position}"
