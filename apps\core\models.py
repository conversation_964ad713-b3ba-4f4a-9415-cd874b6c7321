from django.db import models

# Create your models here.

class SystemSettings(models.Model):
    """
    Stores key-value system settings for the school management system.
    """
    id = models.AutoField(primary_key=True)
    setting_key = models.CharField(max_length=100)
    setting_value = models.TextField()
    description = models.CharField(max_length=255, null=True, blank=True)
    is_public = models.BooleanField(default=False)

    def __str__(self):
        return self.setting_key

class SchoolInfo(models.Model):
    """
    Stores core information about the school, including type, curriculum, and contact details.
    """
    SCHOOL_TYPES = [
        ('Primary', 'Primary'),
        ('Secondary', 'Secondary'),
        ('College', 'College')
    ]
    CURRICULUM_TYPES = [
        ('CBC', 'CBC'),
        ('8-4-4', '8-4-4'),
        ('IGCSE', 'IGCSE'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.Char<PERSON>ield(max_length=255)
    motto = models.CharField(max_length=255, null=True, blank=True)
    logo = models.ImageField(upload_to='school/', null=True, blank=True)
    school_type = models.CharField(max_length=100, choices=SCHOOL_TYPES)
    curriculum_type = models.CharField(max_length=100, choices=CURRICULUM_TYPES)
    academic_year_start = models.DateField(null=True, blank=True)
    academic_year_end = models.DateField(null=True, blank=True)
    address = models.TextField()
    city = models.CharField(max_length=100)
    county = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20, null=True, blank=True)
    phone = models.CharField(max_length=20)
    alt_phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField()
    website = models.URLField(null=True, blank=True)
    working_hours = models.CharField(max_length=100, default='8:00 AM - 5:00 PM')
    principal_name = models.CharField(max_length=255, null=True, blank=True)
    principal_message = models.TextField(null=True, blank=True)
    theme_color = models.CharField(max_length=20, default='#4682B4')
    date_established = models.DateField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class SystemAnalytics(models.Model):
    """
    Model to store system-wide analytics and insights
    """
    METRIC_TYPES = [
        ('user_activity', 'User Activity'),
        ('attendance_trends', 'Attendance Trends'),
        ('academic_performance', 'Academic Performance'),
        ('financial_summary', 'Financial Summary'),
        ('system_usage', 'System Usage'),
        ('security_metrics', 'Security Metrics'),
    ]

    PERIOD_TYPES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('annual', 'Annual'),
    ]

    id = models.AutoField(primary_key=True)
    metric_type = models.CharField(max_length=30, choices=METRIC_TYPES)
    period_type = models.CharField(max_length=20, choices=PERIOD_TYPES)
    period_start = models.DateField()
    period_end = models.DateField()

    # Metric data (stored as JSON)
    data = models.JSONField(default=dict)

    # Summary statistics
    total_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    total_students = models.PositiveIntegerField(default=0)
    total_teachers = models.PositiveIntegerField(default=0)
    average_attendance_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)

    # System metrics
    total_logins = models.PositiveIntegerField(default=0)
    failed_logins = models.PositiveIntegerField(default=0)
    security_incidents = models.PositiveIntegerField(default=0)

    # Metadata
    calculated_at = models.DateTimeField(auto_now=True)
    calculated_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = "System Analytics"
        verbose_name_plural = "System Analytics"
        unique_together = ['metric_type', 'period_type', 'period_start', 'period_end']
        ordering = ['-period_start', 'metric_type']

    def __str__(self):
        return f"{self.metric_type} - {self.period_type} ({self.period_start} to {self.period_end})"


class SystemNotification(models.Model):
    """
    Model for system-wide notifications and announcements
    """
    NOTIFICATION_TYPES = [
        ('system_update', 'System Update'),
        ('maintenance', 'Maintenance'),
        ('security_alert', 'Security Alert'),
        ('announcement', 'Announcement'),
        ('reminder', 'Reminder'),
        ('warning', 'Warning'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    TARGET_AUDIENCES = [
        ('all', 'All Users'),
        ('admin', 'Administrators'),
        ('teacher', 'Teachers'),
        ('student', 'Students'),
        ('parent', 'Parents'),
        ('accountant', 'Accountants'),
    ]

    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS, default='medium')
    target_audience = models.CharField(max_length=20, choices=TARGET_AUDIENCES, default='all')

    # Display settings
    is_active = models.BooleanField(default=True)
    show_on_dashboard = models.BooleanField(default=True)
    show_as_popup = models.BooleanField(default=False)
    auto_dismiss = models.BooleanField(default=False)
    dismiss_after_days = models.PositiveIntegerField(default=7)

    # Scheduling
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)

    # Metadata
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='created_notifications'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Statistics
    view_count = models.PositiveIntegerField(default=0)
    dismiss_count = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = "System Notification"
        verbose_name_plural = "System Notifications"
        ordering = ['-created_at', '-priority']

    def __str__(self):
        return f"{self.title} ({self.notification_type})"

    @property
    def is_expired(self):
        """Check if notification has expired"""
        if self.end_date:
            from django.utils import timezone
            return timezone.now() > self.end_date
        return False

    class Meta:
        verbose_name_plural = "School Information"

class ContactMessage(models.Model):
    name = models.CharField(max_length=255)
    email = models.EmailField()
    subject = models.CharField(max_length=255)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    replied = models.BooleanField(default=False)
    reply_message = models.TextField(null=True, blank=True)
    replied_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Message from {self.name} - {self.subject}"

class Testimonial(models.Model):
    name = models.CharField(max_length=255)
    role = models.CharField(max_length=100)  # Student, Parent, Teacher, etc.
    message = models.TextField()
    image = models.ImageField(upload_to='testimonials/', null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Testimonial by {self.name}"

class FAQ(models.Model):
    question = models.CharField(max_length=255)
    answer = models.TextField()
    category = models.CharField(max_length=100, default='General')
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['order', 'category']
        verbose_name = "FAQ"
        verbose_name_plural = "FAQs"
    
    def __str__(self):
        return self.question
