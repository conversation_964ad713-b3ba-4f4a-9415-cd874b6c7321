from django.contrib import admin
from .models import (
    FeeStructure, StudentPayment, StudentPaymentHistory, StudentFeeBalance, Order,
    FeeCategory, EnhancedFeeStructure, PaymentMethod, Invoice, InvoiceItem,
    EnhancedPayment, Scholarship, StudentScholarship, PaymentReminder, FeeDiscount
)

@admin.register(FeeStructure)
class FeeStructureAdmin(admin.ModelAdmin):
    list_display = ('id', 'grade', 'fee_category', 'amount', 'term', 'year', 'is_mandatory', 'created_at', 'updated_at')
    search_fields = ('grade__name', 'fee_category')
    list_filter = ('grade', 'fee_category', 'is_mandatory', 'year', 'term')
    ordering = ('-year', 'grade', 'fee_category')

@admin.register(StudentPayment)
class StudentPaymentAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'year', 'term', 'month', 'date', 'paid', 'payment_method', 'transaction_id', 'phone_number', 'receipt_number', 'received_by', '_status', 'student_status')
    search_fields = ('index_number', 'month', 'transaction_id', 'receipt_number')
    list_filter = ('payment_method', 'year', 'term', '_status', 'student_status')
    ordering = ('-year', '-date', 'index_number')

@admin.register(StudentPaymentHistory)
class StudentPaymentHistoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'grade_id', 'subject_id', 'teacher_id', 'subject_fee', 'subtotal', '_status', 'month', 'year', 'date', 'invoice_number', 'payment_method', 'transaction_id')
    search_fields = ('index_number', 'month', 'invoice_number', 'transaction_id')
    list_filter = ('payment_method', 'year', 'month', '_status')
    ordering = ('-year', '-date', 'index_number')

@admin.register(StudentFeeBalance)
class StudentFeeBalanceAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'grade', 'year', 'term', 'total_amount', 'amount_paid', 'balance', 'last_payment_date', 'status', 'created_at', 'updated_at')
    search_fields = ('index_number', 'grade__name')
    list_filter = ('grade', 'status', 'year', 'term')
    ordering = ('-year', '-term', 'index_number')

admin.site.register(Order)

# Enhanced Finance Models Admin

@admin.register(FeeCategory)
class FeeCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'category_type', 'is_mandatory', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('category_type', 'is_mandatory', 'is_active', 'created_at')
    ordering = ('name',)

@admin.register(EnhancedFeeStructure)
class EnhancedFeeStructureAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'category', 'grade', 'academic_year', 'term', 'base_amount', 'fee_type', 'is_active')
    search_fields = ('name', 'category__name', 'grade__name')
    list_filter = ('fee_type', 'payment_frequency', 'is_mandatory', 'is_active', 'academic_year', 'term', 'category', 'grade')
    ordering = ('-academic_year', 'grade', 'category')

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'method_type', 'is_active', 'requires_reference', 'processing_fee_percentage', 'processing_fee_fixed')
    search_fields = ('name', 'description')
    list_filter = ('method_type', 'is_active', 'requires_reference', 'created_at')
    ordering = ('name',)

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('id', 'invoice_number', 'student', 'total_amount', 'amount_paid', 'balance_due', 'status', 'due_date', 'created_at')
    search_fields = ('invoice_number', 'student__full_name', 'student__index_number')
    list_filter = ('status', 'invoice_type', 'due_date', 'created_at')
    ordering = ('-created_at',)
    readonly_fields = ('invoice_number', 'tax_amount', 'total_amount', 'balance_due')

@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'invoice', 'description', 'quantity', 'unit_price', 'total_price')
    search_fields = ('description', 'invoice__invoice_number')
    list_filter = ('invoice__status', 'fee_structure__category')
    ordering = ('-invoice__created_at',)

@admin.register(EnhancedPayment)
class EnhancedPaymentAdmin(admin.ModelAdmin):
    list_display = ('id', 'payment_reference', 'student', 'amount', 'payment_method', 'status', 'payment_date', 'created_at')
    search_fields = ('payment_reference', 'student__full_name', 'student__index_number', 'transaction_id', 'external_reference')
    list_filter = ('status', 'payment_method', 'payment_date', 'created_at')
    ordering = ('-payment_date',)
    readonly_fields = ('payment_reference', 'processing_fee', 'net_amount')

@admin.register(Scholarship)
class ScholarshipAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'scholarship_type', 'coverage_type', 'coverage_percentage', 'coverage_amount', 'is_active', 'start_date', 'end_date')
    search_fields = ('name', 'description')
    list_filter = ('scholarship_type', 'coverage_type', 'is_active', 'is_renewable', 'start_date', 'end_date')
    filter_horizontal = ('eligible_grades',)
    ordering = ('-created_at',)

@admin.register(StudentScholarship)
class StudentScholarshipAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'scholarship', 'status', 'total_amount_awarded', 'amount_utilized', 'start_date', 'end_date')
    search_fields = ('student__full_name', 'student__index_number', 'scholarship__name')
    list_filter = ('status', 'scholarship__scholarship_type', 'is_renewable', 'start_date', 'end_date')
    ordering = ('-created_at',)

@admin.register(PaymentReminder)
class PaymentReminderAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'reminder_type', 'delivery_method', 'amount_due', 'status', 'scheduled_date', 'sent_date')
    search_fields = ('student__full_name', 'student__index_number', 'subject', 'message')
    list_filter = ('reminder_type', 'delivery_method', 'status', 'scheduled_date', 'sent_date')
    ordering = ('-scheduled_date',)

@admin.register(FeeDiscount)
class FeeDiscountAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'discount_type', 'discount_reason', 'percentage_value', 'fixed_amount', 'is_active', 'start_date', 'end_date')
    search_fields = ('name', 'description')
    list_filter = ('discount_type', 'discount_reason', 'is_active', 'requires_approval', 'start_date', 'end_date')
    filter_horizontal = ('applicable_categories', 'applicable_grades')
    ordering = ('-created_at',)
