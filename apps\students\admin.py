from django.contrib import admin
from .models import (
    Student, Guardian, Student<PERSON><PERSON>ian, StudentDocument,
    StudentGrade, StudentSubject, StudentStatusHistory,
    StudentPromotion, StudentGraduation
)

# Guardian Admin
@admin.register(Guardian)
class GuardianAdmin(admin.ModelAdmin):
    list_display = ('id', 'full_name', 'relationship', 'phone', 'email', 'is_primary_contact', 'is_emergency_contact')
    search_fields = ('full_name', 'phone', 'email', 'national_id')
    list_filter = ('relationship', 'gender', 'is_primary_contact', 'is_emergency_contact')
    ordering = ('full_name',)

# Student Guardian Through Model Admin
class StudentGuardianInline(admin.TabularInline):
    model = StudentGuardian
    extra = 1
    fields = ('guardian', 'relationship_notes', 'is_authorized_pickup', 'is_fee_payer')

# Student Document Inline
class StudentDocumentInline(admin.TabularInline):
    model = StudentDocument
    extra = 0
    fields = ('document_type', 'document_name', 'document_file', 'uploaded_at')
    readonly_fields = ('uploaded_at',)

# Student Grade Assignment Inline
class StudentGradeInline(admin.TabularInline):
    model = StudentGrade
    extra = 0
    fields = ('grade', 'section', 'academic_year', 'term', 'assignment_type', 'status', 'is_current')

# Enhanced Student Admin
@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = (
        'student_id', 'full_name', 'gender', 'current_grade', 'current_section',
        'boarding_status', 'status', 'admission_date'
    )
    search_fields = ('full_name', 'student_id', 'index_number', 'admission_number', 'phone', 'email')
    list_filter = (
        'gender', 'boarding_status', 'status', 'nationality', 'current_grade',
        'admission_date', 'transfer_status'
    )
    ordering = ('last_name', 'first_name')
    readonly_fields = ('student_id', 'full_name', 'created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('student_id', 'index_number', 'admission_number')
        }),
        ('Personal Details', {
            'fields': (
                ('first_name', 'middle_name', 'last_name'),
                'preferred_name', 'full_name',
                ('gender', 'date_of_birth', 'place_of_birth'),
                ('nationality', 'religion')
            )
        }),
        ('Contact Information', {
            'fields': (
                ('phone', 'email'),
                'address', 'postal_address',
                ('postal_code', 'city', 'county')
            )
        }),
        ('Academic Background', {
            'fields': (
                ('kcpe_index', 'kcpe_score', 'kcpe_year'),
                ('primary_school', 'previous_school', 'previous_class')
            )
        }),
        ('Transfer Information', {
            'fields': (
                ('transfer_status', 'transfer_date'),
                'transfer_reason', 'transfer_documents'
            )
        }),
        ('Health Information', {
            'fields': (
                'health_conditions', 'allergies', 'medications',
                ('blood_group', 'special_needs')
            )
        }),
        ('School Information', {
            'fields': (
                ('boarding_status', 'current_grade', 'current_section'),
                ('status', 'admission_date', 'graduation_date')
            )
        }),
        ('Photo', {
            'fields': ('photo',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        })
    )

    inlines = [StudentGuardianInline, StudentDocumentInline, StudentGradeInline]

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new student
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)
# Enhanced Student Grade Admin
@admin.register(StudentGrade)
class StudentGradeAdmin(admin.ModelAdmin):
    list_display = (
        'student', 'grade', 'section', 'academic_year', 'term',
        'assignment_type', 'status', 'is_current', 'assignment_date'
    )
    search_fields = ('student__full_name', 'student__student_id', 'grade__name')
    list_filter = (
        'grade', 'assignment_type', 'status', 'is_current',
        'academic_year', 'term', 'assignment_date'
    )
    ordering = ('-academic_year', '-term', 'grade__name', 'student__last_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Assignment Details', {
            'fields': (
                'student', 'grade', 'section',
                ('academic_year', 'term'),
                ('assignment_type', 'assignment_date'),
                ('status', 'is_current')
            )
        }),
        ('Performance', {
            'fields': (
                ('term_average', 'class_position', 'total_students')
            )
        }),
        ('Promotion Details', {
            'fields': (
                ('promotion_date', 'next_grade'),
                'promotion_reason'
            )
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        })
    )

# Enhanced Student Subject Admin
@admin.register(StudentSubject)
class StudentSubjectAdmin(admin.ModelAdmin):
    list_display = (
        'student', 'subject', 'grade', 'academic_year', 'term',
        'subject_type', 'registration_status', 'teacher'
    )
    search_fields = ('student__full_name', 'student__student_id', 'subject__name')
    list_filter = (
        'subject', 'grade', 'subject_type', 'registration_status',
        'academic_year', 'term', 'teacher'
    )
    ordering = ('-academic_year', '-term', 'student__last_name', 'subject__name')
    readonly_fields = ('created_at', 'updated_at')

# Student Document Admin
@admin.register(StudentDocument)
class StudentDocumentAdmin(admin.ModelAdmin):
    list_display = ('student', 'document_type', 'document_name', 'uploaded_at', 'uploaded_by')
    search_fields = ('student__full_name', 'student__student_id', 'document_name')
    list_filter = ('document_type', 'uploaded_at')
    ordering = ('-uploaded_at',)

# Student Status History Admin
@admin.register(StudentStatusHistory)
class StudentStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ('student', 'previous_status', 'new_status', 'change_date', 'changed_by')
    search_fields = ('student__full_name', 'student__student_id')
    list_filter = ('previous_status', 'new_status', 'change_date')
    ordering = ('-change_date',)

# Student Promotion Admin
@admin.register(StudentPromotion)
class StudentPromotionAdmin(admin.ModelAdmin):
    list_display = (
        'student', 'from_grade', 'to_grade', 'academic_year',
        'promotion_type', 'promotion_status', 'promotion_date'
    )
    search_fields = ('student__full_name', 'student__student_id')
    list_filter = (
        'from_grade', 'to_grade', 'promotion_type', 'promotion_status',
        'academic_year', 'promotion_date'
    )
    ordering = ('-academic_year', 'student__last_name')

# Student Graduation Admin
@admin.register(StudentGraduation)
class StudentGraduationAdmin(admin.ModelAdmin):
    list_display = (
        'student', 'graduation_year', 'graduation_date', 'graduation_type',
        'graduation_status', 'certificate_number', 'certificate_issued'
    )
    search_fields = ('student__full_name', 'student__student_id', 'certificate_number')
    list_filter = (
        'graduation_year', 'graduation_type', 'graduation_status',
        'certificate_issued', 'graduation_date'
    )
    ordering = ('-graduation_year', 'student__last_name')
    readonly_fields = ('certificate_number', 'created_at', 'updated_at')
