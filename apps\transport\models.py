from django.db import models

# Create your models here.

class TransportRoute(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    distance = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)  # in kilometers
    estimated_time = models.IntegerField(null=True, blank=True)  # in minutes
    fee = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)

    def __str__(self):
        return self.name

class TransportVehicle(models.Model):
    VEHICLE_TYPES = [
        ('bus', 'Bus'),
        ('van', 'Van'),
        ('car', 'Car'),
    ]
    VEHICLE_STATUSES = [
        ('active', 'Active'),
        ('maintenance', 'Maintenance'),
        ('inactive', 'Inactive'),
    ]
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    registration_number = models.CharField(max_length=50)
    vehicle_type = models.CharField(max_length=50, choices=VEHICLE_TYPES)
    capacity = models.IntegerField()
    driver_name = models.CharField(max_length=255, null=True, blank=True)
    driver_contact = models.CharField(max_length=20, null=True, blank=True)
    insurance_expiry = models.DateField(null=True, blank=True)
    service_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=50, default='active', choices=VEHICLE_STATUSES)

    def __str__(self):
        return self.name

class TransportStop(models.Model):
    id = models.AutoField(primary_key=True)
    route = models.ForeignKey(TransportRoute, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    sequence = models.IntegerField()
    arrival_time = models.TimeField(null=True, blank=True)
    departure_time = models.TimeField(null=True, blank=True)
    coordinates = models.CharField(max_length=100, null=True, blank=True)  # latitude,longitude

    def __str__(self):
        return self.name

class TransportAssignment(models.Model):
    ASSIGNMENT_STATUSES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    route = models.ForeignKey(TransportRoute, on_delete=models.CASCADE)
    stop = models.ForeignKey(TransportStop, on_delete=models.CASCADE)
    vehicle = models.ForeignKey(TransportVehicle, on_delete=models.CASCADE)
    fee = models.DecimalField(max_digits=11, decimal_places=2)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=50, default='active', choices=ASSIGNMENT_STATUSES)

    def __str__(self):
        return f"Transport Assignment - {self.id}"
