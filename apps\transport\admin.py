from django.contrib import admin
from .models import TransportRoute, TransportVehicle, TransportStop, TransportAssignment

@admin.register(TransportRoute)
class TransportRouteAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description', 'distance', 'estimated_time', 'fee')
    search_fields = ('name', 'description')
    list_filter = ('distance', 'estimated_time')
    ordering = ('name',)

@admin.register(TransportVehicle)
class TransportVehicleAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'registration_number', 'vehicle_type', 'capacity', 'driver_name', 'driver_contact', 'insurance_expiry', 'service_date', 'status')
    search_fields = ('name', 'registration_number', 'driver_name', 'driver_contact')
    list_filter = ('vehicle_type', 'status', 'insurance_expiry', 'service_date')
    ordering = ('name',)

@admin.register(TransportStop)
class TransportStopAdmin(admin.ModelAdmin):
    list_display = ('id', 'route', 'name', 'sequence', 'arrival_time', 'departure_time', 'coordinates')
    search_fields = ('name', 'route__name', 'coordinates')
    list_filter = ('route', 'arrival_time', 'departure_time')
    ordering = ('route', 'sequence')

@admin.register(TransportAssignment)
class TransportAssignmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'route', 'stop', 'vehicle', 'fee', 'start_date', 'end_date', 'status')
    search_fields = ('student__full_name', 'route__name', 'stop__name', 'vehicle__name')
    list_filter = ('route', 'vehicle', 'status', 'start_date', 'end_date')
    ordering = ('-start_date', 'student')
