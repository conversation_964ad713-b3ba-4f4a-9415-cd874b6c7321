from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from datetime import date, timedelta
import json

from .models import (
    ReportTemplate, GeneratedReport, Dashboard, DashboardWidget,
    ReportSchedule, DataVisualization
)

User = get_user_model()


class ReportTemplateForm(forms.ModelForm):
    """
    Form for creating and editing report templates
    """
    class Meta:
        model = ReportTemplate
        fields = [
            'name', 'description', 'report_type', 'output_format',
            'template_config', 'sql_query', 'chart_config',
            'is_automated', 'frequency', 'is_public'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-input'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
            'report_type': forms.Select(attrs={'class': 'form-select'}),
            'output_format': forms.Select(attrs={'class': 'form-select'}),
            'template_config': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 5}),
            'sql_query': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 8}),
            'chart_config': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 5}),
            'frequency': forms.Select(attrs={'class': 'form-select'}),
            'is_automated': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['template_config'].help_text = 'JSON configuration for template parameters'
        self.fields['chart_config'].help_text = 'JSON configuration for charts and visualizations'
        self.fields['sql_query'].help_text = 'SQL query for data extraction (optional)'
    
    def clean_template_config(self):
        config = self.cleaned_data['template_config']
        if config:
            try:
                json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError('Invalid JSON format')
        return config
    
    def clean_chart_config(self):
        config = self.cleaned_data['chart_config']
        if config:
            try:
                json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError('Invalid JSON format')
        return config


class ReportGenerationForm(forms.Form):
    """
    Dynamic form for report generation parameters
    """
    date_from = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'}),
        initial=lambda: date.today() - timedelta(days=30)
    )
    date_to = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'}),
        initial=date.today
    )
    
    def __init__(self, *args, **kwargs):
        self.template = kwargs.pop('template', None)
        super().__init__(*args, **kwargs)
        
        if self.template:
            # Add dynamic fields based on template configuration
            try:
                config = json.loads(self.template.template_config or '{}')
                self.add_dynamic_fields(config)
            except json.JSONDecodeError:
                pass
    
    def add_dynamic_fields(self, config):
        """
        Add dynamic fields based on template configuration
        """
        for field_name, field_config in config.get('parameters', {}).items():
            field_type = field_config.get('type', 'text')
            field_label = field_config.get('label', field_name.title())
            field_required = field_config.get('required', False)
            
            if field_type == 'text':
                self.fields[field_name] = forms.CharField(
                    label=field_label,
                    required=field_required,
                    widget=forms.TextInput(attrs={'class': 'form-input'})
                )
            elif field_type == 'number':
                self.fields[field_name] = forms.IntegerField(
                    label=field_label,
                    required=field_required,
                    widget=forms.NumberInput(attrs={'class': 'form-input'})
                )
            elif field_type == 'select':
                choices = [(opt, opt) for opt in field_config.get('options', [])]
                self.fields[field_name] = forms.ChoiceField(
                    label=field_label,
                    choices=choices,
                    required=field_required,
                    widget=forms.Select(attrs={'class': 'form-select'})
                )
            elif field_type == 'boolean':
                self.fields[field_name] = forms.BooleanField(
                    label=field_label,
                    required=False,
                    widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
                )


class DashboardForm(forms.ModelForm):
    """
    Form for creating and editing dashboards
    """
    class Meta:
        model = Dashboard
        fields = [
            'name', 'description', 'dashboard_type', 'layout_config',
            'refresh_interval', 'is_default', 'is_public'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-input'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
            'dashboard_type': forms.Select(attrs={'class': 'form-select'}),
            'layout_config': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 5}),
            'refresh_interval': forms.NumberInput(attrs={'class': 'form-input'}),
            'is_default': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['layout_config'].help_text = 'JSON configuration for dashboard layout'
        self.fields['refresh_interval'].help_text = 'Refresh interval in seconds'
    
    def clean_layout_config(self):
        config = self.cleaned_data['layout_config']
        if config:
            try:
                json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError('Invalid JSON format')
        return config


class DashboardWidgetForm(forms.ModelForm):
    """
    Form for creating and editing dashboard widgets
    """
    class Meta:
        model = DashboardWidget
        fields = [
            'title', 'widget_type', 'chart_type', 'position_x', 'position_y',
            'width', 'height', 'data_source', 'data_config', 'chart_config',
            'auto_refresh', 'refresh_interval'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-input'}),
            'widget_type': forms.Select(attrs={'class': 'form-select'}),
            'chart_type': forms.Select(attrs={'class': 'form-select'}),
            'position_x': forms.NumberInput(attrs={'class': 'form-input'}),
            'position_y': forms.NumberInput(attrs={'class': 'form-input'}),
            'width': forms.NumberInput(attrs={'class': 'form-input'}),
            'height': forms.NumberInput(attrs={'class': 'form-input'}),
            'data_source': forms.TextInput(attrs={'class': 'form-input'}),
            'data_config': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 4}),
            'chart_config': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 4}),
            'auto_refresh': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'refresh_interval': forms.NumberInput(attrs={'class': 'form-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['data_config'].help_text = 'JSON configuration for data source'
        self.fields['chart_config'].help_text = 'JSON configuration for chart appearance'
    
    def clean_data_config(self):
        config = self.cleaned_data['data_config']
        if config:
            try:
                json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError('Invalid JSON format')
        return config
    
    def clean_chart_config(self):
        config = self.cleaned_data['chart_config']
        if config:
            try:
                json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError('Invalid JSON format')
        return config


class ReportScheduleForm(forms.ModelForm):
    """
    Form for creating and editing report schedules
    """
    email_recipients_text = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
        help_text='Enter email addresses separated by commas',
        required=False
    )
    
    class Meta:
        model = ReportSchedule
        fields = [
            'name', 'cron_expression', 'timezone', 'email_recipients_text',
            'user_recipients', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-input'}),
            'cron_expression': forms.TextInput(attrs={'class': 'form-input'}),
            'timezone': forms.TextInput(attrs={'class': 'form-input'}),
            'user_recipients': forms.SelectMultiple(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['cron_expression'].help_text = 'Cron expression for scheduling (e.g., "0 9 * * 1" for every Monday at 9 AM)'
        self.fields['user_recipients'].queryset = User.objects.filter(is_active=True)
        
        # Populate email recipients if editing
        if self.instance and self.instance.pk:
            self.fields['email_recipients_text'].initial = ', '.join(self.instance.email_recipients)
    
    def clean_email_recipients_text(self):
        emails_text = self.cleaned_data['email_recipients_text']
        if emails_text:
            emails = [email.strip() for email in emails_text.split(',') if email.strip()]
            # Validate email addresses
            for email in emails:
                forms.EmailField().clean(email)
            return emails
        return []
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.email_recipients = self.cleaned_data['email_recipients_text']
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class DataVisualizationForm(forms.ModelForm):
    """
    Form for creating and editing data visualizations
    """
    class Meta:
        model = DataVisualization
        fields = [
            'name', 'description', 'visualization_type', 'data_query',
            'config', 'is_public'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-input'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
            'visualization_type': forms.Select(attrs={'class': 'form-select'}),
            'data_query': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 8}),
            'config': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 5}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['data_query'].help_text = 'SQL query or data source configuration'
        self.fields['config'].help_text = 'JSON configuration for visualization'
    
    def clean_config(self):
        config = self.cleaned_data['config']
        if config:
            try:
                json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError('Invalid JSON format')
        return config


class ReportFilterForm(forms.Form):
    """
    Form for filtering reports list
    """
    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search reports...'
        })
    )
    
    report_type = forms.ChoiceField(
        choices=[('', 'All Types')] + ReportTemplate.REPORT_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + GeneratedReport.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )
    
    created_by = forms.ModelChoiceField(
        queryset=User.objects.filter(status='active'),
        required=False,
        empty_label="All Users",
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class BulkReportActionForm(forms.Form):
    """
    Form for bulk actions on reports
    """
    ACTION_CHOICES = [
        ('delete', 'Delete Selected'),
        ('regenerate', 'Regenerate Selected'),
        ('export_list', 'Export List'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    selected_reports = forms.CharField(
        widget=forms.HiddenInput()
    )
    
    def clean_selected_reports(self):
        selected = self.cleaned_data['selected_reports']
        if selected:
            try:
                report_ids = [int(id) for id in selected.split(',') if id.strip()]
                return report_ids
            except ValueError:
                raise ValidationError('Invalid report selection')
        return []
