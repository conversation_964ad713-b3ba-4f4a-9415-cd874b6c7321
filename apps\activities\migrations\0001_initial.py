# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("students", "0001_initial"),
        ("teachers", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CoCurricularActivity",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.Char<PERSON>ield(max_length=255)),
                (
                    "category",
                    models.Char<PERSON>ield(
                        choices=[
                            ("sports", "Sports"),
                            ("music", "Music"),
                            ("drama", "Drama"),
                            ("clubs", "Clubs"),
                        ],
                        max_length=50,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("meeting_day", models.CharField(blank=True, max_length=20, null=True)),
                ("meeting_time", models.TimeField(blank=True, null=True)),
                ("venue", models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                (
                    "fee",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                ("status", models.CharField(default="active", max_length=50)),
                (
                    "teacher_in_charge",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="teachers.teacher",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentActivity",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("join_date", models.DateField()),
                ("position", models.CharField(blank=True, max_length=50, null=True)),
                ("achievements", models.TextField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("inactive", "Inactive")],
                        default="active",
                        max_length=50,
                    ),
                ),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="activities.cocurricularactivity",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
            ],
        ),
    ]
