from django.urls import path
from . import views

app_name = 'transport'

urlpatterns = [
    # Dashboard
    path('', views.transport_dashboard, name='dashboard'),
    
    # Route Management
    path('routes/', views.route_list, name='route_list'),
    path('routes/<int:route_id>/', views.route_detail, name='route_detail'),
    path('routes/add/', views.add_route, name='add_route'),
    path('routes/<int:route_id>/edit/', views.edit_route, name='edit_route'),
    
    # Vehicle Management
    path('vehicles/', views.vehicle_list, name='vehicle_list'),
    path('vehicles/<int:vehicle_id>/', views.vehicle_detail, name='vehicle_detail'),
    path('vehicles/add/', views.add_vehicle, name='add_vehicle'),
    path('vehicles/<int:vehicle_id>/edit/', views.edit_vehicle, name='edit_vehicle'),
    
    # Stop Management
    path('routes/<int:route_id>/stops/', views.stop_list, name='stop_list'),
    path('routes/<int:route_id>/stops/add/', views.add_stop, name='add_stop'),
    path('stops/<int:stop_id>/edit/', views.edit_stop, name='edit_stop'),
    
    # Student Assignment Management
    path('assignments/', views.assignment_list, name='assignment_list'),
    path('assignments/<int:assignment_id>/', views.assignment_detail, name='assignment_detail'),
    path('assignments/add/', views.add_assignment, name='add_assignment'),
    path('assignments/<int:assignment_id>/edit/', views.edit_assignment, name='edit_assignment'),
    
    # Fee Management
    path('assignments/<int:assignment_id>/payment/', views.fee_payment, name='fee_payment'),
    path('assignments/<int:assignment_id>/discount/', views.apply_discount, name='apply_discount'),
    
    # Reports and Analytics
    path('reports/', views.transport_reports, name='reports'),
    path('statistics/', views.transport_statistics, name='statistics'),
    
    # AJAX Endpoints
    path('api/routes/<int:route_id>/stops/', views.get_route_stops, name='get_route_stops'),
    path('api/vehicles/<int:vehicle_id>/capacity/', views.get_vehicle_capacity, name='get_vehicle_capacity'),
]
