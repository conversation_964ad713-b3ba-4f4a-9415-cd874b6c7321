# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("academics", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Teacher",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("full_name", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("i_name", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("gender", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("address", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("phone", models.Char<PERSON>ield(max_length=255)),
                ("email", models.<PERSON>ail<PERSON>ield(max_length=254)),
                ("image_name", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("index_number", models.BigIntegerField(unique=True)),
                ("national_id", models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                (
                    "employment_type",
                    models.Char<PERSON><PERSON>(
                        choices=[("TSC", "TSC"), ("BOM", "BOM")],
                        default="TSC",
                        max_length=50,
                    ),
                ),
                ("role", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "qualification",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "salary",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                ("reg_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("on leave", "On Leave"),
                        ],
                        default="active",
                        max_length=50,
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="academics.department",
                    ),
                ),
            ],
        ),
    ]
