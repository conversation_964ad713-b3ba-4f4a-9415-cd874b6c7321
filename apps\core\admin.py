from django.contrib import admin
from .models import SystemSettings, SchoolInfo, ContactMessage, Testimonial, FAQ, SystemNotification, SystemAnalytics

@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    list_display = ('id', 'setting_key', 'setting_value', 'is_public')
    search_fields = ('setting_key',)
    list_filter = ('is_public',)
    ordering = ('setting_key',)

@admin.register(SchoolInfo)
class SchoolInfoAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'school_type', 'curriculum_type']
    search_fields = ['name', 'email', 'phone']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'motto', 'logo', 'school_type', 'curriculum_type')
        }),
        ('Contact Information', {
            'fields': ('address', 'city', 'county', 'postal_code', 'phone', 'alt_phone', 'email', 'website')
        }),
        ('Principal Information', {
            'fields': ('principal_name', 'principal_message')
        }),
        ('Additional Information', {
            'fields': ('date_established', 'working_hours')
        }),
    )

@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'subject', 'created_at', 'is_read', 'replied']
    list_filter = ['is_read', 'replied', 'created_at']
    search_fields = ['name', 'email', 'subject', 'message']
    readonly_fields = ['created_at']
    
    def mark_as_read(self, request, queryset):
        queryset.update(is_read=True)
    mark_as_read.short_description = "Mark selected messages as read"
    
    actions = [mark_as_read]

@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['name', 'role', 'is_active', 'created_at']
    list_filter = ['role', 'is_active', 'created_at']
    search_fields = ['name', 'role', 'message']
    list_editable = ['is_active']

@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    list_display = ['question', 'category', 'is_active', 'order']
    list_filter = ['category', 'is_active']
    search_fields = ['question', 'answer']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'category']

@admin.register(SystemNotification)
class SystemNotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'notification_type', 'priority', 'target_audience', 'is_active', 'created_at']
    list_filter = ['notification_type', 'priority', 'target_audience', 'is_active', 'created_at']
    search_fields = ['title', 'message']
    list_editable = ['is_active']
    readonly_fields = ['created_at', 'updated_at', 'view_count', 'dismiss_count']
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'message', 'notification_type', 'priority', 'target_audience')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'show_on_dashboard', 'show_as_popup', 'auto_dismiss', 'dismiss_after_days')
        }),
        ('Scheduling', {
            'fields': ('start_date', 'end_date')
        }),
        ('Statistics', {
            'fields': ('view_count', 'dismiss_count'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(SystemAnalytics)
class SystemAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['metric_type', 'period_type', 'period_start', 'period_end', 'total_users', 'active_users', 'calculated_at']
    list_filter = ['metric_type', 'period_type', 'calculated_at']
    search_fields = ['metric_type']
    readonly_fields = ['calculated_at', 'calculated_by']
    fieldsets = (
        ('Period Information', {
            'fields': ('metric_type', 'period_type', 'period_start', 'period_end')
        }),
        ('User Statistics', {
            'fields': ('total_users', 'active_users', 'total_students', 'total_teachers')
        }),
        ('Performance Metrics', {
            'fields': ('average_attendance_rate', 'total_logins', 'failed_logins', 'security_incidents')
        }),
        ('Data', {
            'fields': ('data',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('calculated_at', 'calculated_by'),
            'classes': ('collapse',)
        }),
    )
