from django.urls import path
from . import views

app_name = 'petty_cash'

urlpatterns = [
    # Dashboard
    path('', views.petty_cash_dashboard, name='dashboard'),
    
    # Request management
    path('requests/', views.request_list, name='request_list'),
    path('requests/create/', views.create_request, name='create_request'),
    path('requests/<int:pk>/', views.request_detail, name='request_detail'),
    path('requests/<int:pk>/edit/', views.edit_request, name='edit_request'),
    path('requests/<int:pk>/cancel/', views.cancel_request, name='cancel_request'),
    
    # Admin/Approval views (for administrators)
    path('admin/requests/', views.admin_request_list, name='admin_request_list'),
    path('admin/requests/<int:pk>/approve/', views.approve_request, name='approve_request'),
    path('admin/requests/<int:pk>/reject/', views.reject_request, name='reject_request'),
    
    # Expense tracking
    path('expenses/', views.expense_list, name='expense_list'),
    path('expenses/create/', views.create_expense, name='create_expense'),
    path('expenses/<int:pk>/', views.expense_detail, name='expense_detail'),
    
    # Reports
    path('reports/', views.reports_dashboard, name='reports'),
    path('reports/summary/', views.summary_report, name='summary_report'),
    
    # API endpoints
    path('api/categories/', views.api_categories, name='api_categories'),
    path('api/funds/', views.api_funds, name='api_funds'),
]
