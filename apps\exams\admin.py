from django.contrib import admin
from django import forms
from .models import (
    ExamType, Exam, GradingSystem, GradeScale, ExamTimetable,
    EnhancedStudentExamResult, StudentOverallPerformance, SubjectPerformanceAnalysis,
    ReportCard, PerformanceComparison, ClassRanking, StudentRanking,
    ExamRangeGrade, StudentExam, StudentOverallExamResult
)

class ExamAdminForm(forms.ModelForm):
    """Custom form for Exam admin"""

    class Meta:
        model = Exam
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add help text for exam_type field
        if 'exam_type' in self.fields:
            self.fields['exam_type'].help_text = "Select the type of examination"

@admin.register(Exam)
class ExamAdmin(admin.ModelAdmin):
    form = ExamAdminForm
    list_display = ('id', 'exam_code', 'name', 'exam_type', 'academic_year', 'term', 'start_date', 'end_date', 'status', 'is_published')
    search_fields = ('name', 'exam_code', 'exam_type__name', 'description')
    list_filter = ('exam_type', 'status', 'is_published', 'academic_year', 'term', 'marking_scheme')
    filter_horizontal = ('grades', 'subjects')
    ordering = ('-academic_year', '-term', 'name')
    readonly_fields = ('exam_code',)

    fields = [
        'name', 'exam_type', 'academic_year', 'term', 'description', 'instructions',
        'total_marks', 'pass_marks', 'duration_minutes', 'marking_scheme',
        'start_date', 'end_date', 'registration_start', 'registration_end',
        'results_release_date', 'grades', 'subjects', 'allow_calculator',
        'allow_reference_materials', 'requires_signature', 'status',
        'is_published', 'results_published'
    ]

@admin.register(ExamRangeGrade)
class ExamRangeGradeAdmin(admin.ModelAdmin):
    list_display = ('id', 'grade', 'mark_range', '_from', '_to', 'mark_grade', 'grade_point', 'remarks')
    search_fields = ('grade__name', 'mark_grade')
    list_filter = ('grade', 'mark_grade')
    ordering = ('grade', 'mark_grade')

@admin.register(ExamTimetable)
class ExamTimetableAdmin(admin.ModelAdmin):
    list_display = ('id', 'exam', 'grade', 'subject', 'exam_date', 'start_time', 'end_time', 'classroom', 'chief_invigilator', 'status', 'is_published')
    search_fields = ('exam__name', 'grade__name', 'subject__name', 'classroom__name', 'chief_invigilator__full_name')
    list_filter = ('exam', 'grade', 'subject', 'session_type', 'status', 'is_published', 'exam_date')
    filter_horizontal = ('additional_venues', 'assistant_invigilators')
    ordering = ('-exam_date', 'start_time', 'grade')
    readonly_fields = ('calculated_duration', 'attendance_percentage')

@admin.register(StudentExam)
class StudentExamAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'grade', 'exam', 'subject', 'marks', 'grade', 'remarks', 'position_in_subject', 'year', 'date', 'recorded_by', 'verified')
    search_fields = ('index_number', 'grade__name', 'exam__name', 'subject__name')
    list_filter = ('grade', 'exam', 'subject', 'position_in_subject', 'verified', 'year')
    ordering = ('-year', '-date', 'index_number')

@admin.register(StudentOverallExamResult)
class StudentOverallExamResultAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'grade', 'exam', 'total_marks', 'average', 'grade', 'position_in_class', 'attendance_percentage', 'year', 'term', 'created_at', 'updated_at')
    search_fields = ('index_number', 'grade__name', 'exam__name')
    list_filter = ('grade', 'exam', 'year', 'term')
    ordering = ('-year', '-term', 'index_number')

# Enhanced Exam Management Admin

@admin.register(ExamType)
class ExamTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'category', 'default_duration_minutes', 'default_total_marks', 'weight_percentage', 'is_active')
    search_fields = ('name', 'description')
    list_filter = ('category', 'requires_timetable', 'allows_retake', 'is_active', 'created_at')
    ordering = ('category', 'name')

@admin.register(GradingSystem)
class GradingSystemAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'system_type', 'is_default', 'is_active', 'use_weighted_average', 'round_to_decimal_places')
    search_fields = ('name', 'description')
    list_filter = ('system_type', 'is_default', 'is_active', 'use_weighted_average', 'created_at')
    filter_horizontal = ('grades',)
    ordering = ('name',)

@admin.register(GradeScale)
class GradeScaleAdmin(admin.ModelAdmin):
    list_display = ('id', 'grading_system', 'letter_grade', 'grade_point', 'min_percentage', 'max_percentage', 'is_passing_grade')
    search_fields = ('letter_grade', 'description', 'remarks')
    list_filter = ('grading_system', 'is_passing_grade')
    ordering = ('grading_system', '-min_percentage')

@admin.register(EnhancedStudentExamResult)
class EnhancedStudentExamResultAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'exam', 'timetable_entry', 'marks_obtained', 'total_marks', 'percentage', 'letter_grade', 'status', 'is_verified')
    search_fields = ('student__full_name', 'student__index_number', 'exam__name', 'timetable_entry__subject__name')
    list_filter = ('status', 'attendance_status', 'is_verified', 'exam__academic_year', 'exam__term', 'timetable_entry__subject')
    ordering = ('-exam__start_date', 'student__full_name')
    readonly_fields = ('percentage', 'time_taken_minutes')

@admin.register(StudentOverallPerformance)
class StudentOverallPerformanceAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'exam', 'total_subjects', 'subjects_passed', 'overall_percentage', 'overall_grade', 'class_position', 'grade_position')
    search_fields = ('student__full_name', 'student__index_number', 'exam__name')
    list_filter = ('exam__academic_year', 'exam__term', 'performance_trend', 'is_promoted', 'created_at')
    ordering = ('-exam__start_date', 'class_position')
    readonly_fields = ('pass_percentage', 'performance_category')

@admin.register(SubjectPerformanceAnalysis)
class SubjectPerformanceAnalysisAdmin(admin.ModelAdmin):
    list_display = ('id', 'exam', 'subject', 'grade', 'total_students', 'students_passed', 'average_score', 'pass_rate', 'teacher')
    search_fields = ('exam__name', 'subject__name', 'grade__name', 'teacher__full_name')
    list_filter = ('exam__academic_year', 'exam__term', 'subject', 'grade', 'teacher', 'created_at')
    ordering = ('-exam__start_date', 'subject__name')
    readonly_fields = ('performance_rating',)

@admin.register(ReportCard)
class ReportCardAdmin(admin.ModelAdmin):
    list_display = ('id', 'report_number', 'student', 'exam', 'report_type', 'academic_year', 'term', 'status', 'generated_at')
    search_fields = ('report_number', 'student__full_name', 'student__index_number', 'exam__name')
    list_filter = ('report_type', 'status', 'academic_year', 'term', 'generated_at', 'approved_at')
    ordering = ('-academic_year', '-term', 'student__full_name')
    readonly_fields = ('report_number', 'file_size')

@admin.register(PerformanceComparison)
class PerformanceComparisonAdmin(admin.ModelAdmin):
    list_display = ('id', 'comparison_name', 'comparison_type', 'total_students_analyzed', 'improvement_count', 'decline_count', 'analyzed_at')
    search_fields = ('comparison_name', 'description')
    list_filter = ('comparison_type', 'analyzed_at')
    filter_horizontal = ('students', 'exams', 'subjects', 'grades')
    ordering = ('-analyzed_at',)

@admin.register(ClassRanking)
class ClassRankingAdmin(admin.ModelAdmin):
    list_display = ('id', 'exam', 'grade', 'ranking_criteria', 'total_students_ranked', 'highest_score', 'lowest_score', 'class_average')
    search_fields = ('exam__name', 'grade__name')
    list_filter = ('ranking_criteria', 'exam__academic_year', 'exam__term', 'grade', 'generated_at')
    ordering = ('-exam__start_date', 'grade')

@admin.register(StudentRanking)
class StudentRankingAdmin(admin.ModelAdmin):
    list_display = ('id', 'class_ranking', 'student', 'position', 'score', 'percentile', 'subjects_count', 'average_score')
    search_fields = ('student__full_name', 'student__index_number', 'class_ranking__exam__name')
    list_filter = ('class_ranking__exam__academic_year', 'class_ranking__exam__term', 'class_ranking__grade')
    ordering = ('class_ranking', 'position')
