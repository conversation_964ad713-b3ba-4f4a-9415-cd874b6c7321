from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON>User, BaseUserManager, PermissionsMixin
from django.utils import timezone
from datetime import timedelta

# Create your models here.

class CustomUserManager(BaseUserManager):
    """
    Custom manager for User model with email as unique identifier.
    """
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        is_staff = extra_fields.pop('is_staff', False)
        is_superuser = extra_fields.pop('is_superuser', False)
        user = self.model(email=email, is_staff=is_staff, is_superuser=is_superuser, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)

class User(AbstractBaseUser, PermissionsMixin):
    """
    Custom user model supporting multiple user types.
    """
    USER_TYPES = [
        ('admin', 'Admin'),
        ('teacher', 'Teacher'),
        ('student', 'Student'),
        ('parent', 'Parent'),
        ('accountant', 'Accountant'),
    ]
    id = models.AutoField(primary_key=True)
    email = models.EmailField(unique=True)
    password = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=USER_TYPES)
    index_number = models.BigIntegerField(null=True, blank=True)
    reset_token = models.CharField(max_length=255, null=True, blank=True)
    reset_token_expiry = models.DateTimeField(null=True, blank=True)
    last_login = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=50, default='active')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)

    # Failed login tracking
    failed_login_attempts = models.PositiveIntegerField(default=0)
    last_failed_login = models.DateTimeField(null=True, blank=True)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    lockout_reason = models.CharField(max_length=255, null=True, blank=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    def __str__(self):
        return f"{self.email} ({self.type})"

    def get_full_name(self):
        """
        Return the full name for the user.
        Since our User model doesn't have first_name/last_name fields,
        we'll try to get the name from related profile models.
        """
        try:
            if self.type == 'admin':
                admin = Admin.objects.get(email=self.email)
                return admin.full_name
            elif self.type == 'teacher':
                from apps.teachers.models import Teacher
                teacher = Teacher.objects.get(user=self)
                return teacher.full_name
            elif self.type == 'student':
                from apps.students.models import Student
                student = Student.objects.get(user=self)
                return student.full_name
            elif self.type == 'parent':
                # For parents, we'll use email as fallback since they might not have a profile
                return self.email.split('@')[0].replace('.', ' ').title()
            elif self.type == 'accountant':
                # For accountants, use email as fallback
                return self.email.split('@')[0].replace('.', ' ').title()
            else:
                return self.email.split('@')[0].replace('.', ' ').title()
        except Exception:
            # Fallback to email-based name if profile doesn't exist
            return self.email.split('@')[0].replace('.', ' ').title()

    def get_short_name(self):
        """Return the short name for the user."""
        return self.get_full_name().split()[0] if self.get_full_name() else self.email

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def increment_failed_login(self):
        """Increment failed login attempts and lock account if necessary"""
        self.failed_login_attempts += 1
        self.last_failed_login = timezone.now()

        # Lock account after 5 failed attempts for 30 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = timezone.now() + timedelta(minutes=30)
            self.lockout_reason = f"Account locked due to {self.failed_login_attempts} failed login attempts"
            self.status = 'locked'

        self.save()

    def reset_failed_login(self):
        """Reset failed login attempts after successful login"""
        self.failed_login_attempts = 0
        self.last_failed_login = None
        self.account_locked_until = None
        self.lockout_reason = None
        if self.status == 'locked':
            self.status = 'active'
        self.save()

    def unlock_account(self):
        """Manually unlock account (admin function)"""
        self.failed_login_attempts = 0
        self.last_failed_login = None
        self.account_locked_until = None
        self.lockout_reason = None
        self.status = 'active'
        self.save()

    @property
    def is_active(self):
        """Override is_active to include lockout check"""
        return self.status == 'active' and not self.is_account_locked()

class Admin(models.Model):
    """
    Admin profile model.
    """
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField(unique=True)
    full_name = models.CharField(max_length=255)
    i_name = models.CharField(max_length=255)
    gender = models.CharField(max_length=255, choices=GENDER_CHOICES)
    address = models.CharField(max_length=255)
    phone = models.CharField(max_length=255)
    email = models.EmailField()
    image_name = models.CharField(max_length=255)
    national_id = models.CharField(max_length=20, null=True, blank=True)
    role = models.CharField(max_length=100, default='admin')
    reg_date = models.DateField()

    def __str__(self):
        return self.full_name


class LoginAttempt(models.Model):
    """
    Model to track login attempts for security monitoring
    """
    ATTEMPT_TYPES = [
        ('success', 'Successful Login'),
        ('failed', 'Failed Login'),
        ('locked', 'Account Locked'),
    ]

    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_attempts', null=True, blank=True)
    email = models.EmailField()  # Store email even if user doesn't exist
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    attempt_type = models.CharField(max_length=20, choices=ATTEMPT_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    failure_reason = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = "Login Attempt"
        verbose_name_plural = "Login Attempts"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.email} - {self.attempt_type} at {self.timestamp}"


class ParentChildRelationship(models.Model):
    """
    Model to link parents with their children (students)
    """
    RELATIONSHIP_TYPES = [
        ('father', 'Father'),
        ('mother', 'Mother'),
        ('guardian', 'Guardian'),
        ('stepfather', 'Step Father'),
        ('stepmother', 'Step Mother'),
        ('grandfather', 'Grandfather'),
        ('grandmother', 'Grandmother'),
        ('uncle', 'Uncle'),
        ('aunt', 'Aunt'),
        ('other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    parent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='children', limit_choices_to={'type': 'parent'})
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='parents')
    relationship_type = models.CharField(max_length=20, choices=RELATIONSHIP_TYPES)
    is_primary_contact = models.BooleanField(default=False)
    is_emergency_contact = models.BooleanField(default=False)
    can_pick_up = models.BooleanField(default=True)
    notes = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Parent-Child Relationship"
        verbose_name_plural = "Parent-Child Relationships"
        unique_together = ['parent', 'student']
        ordering = ['student', '-is_primary_contact']

    def __str__(self):
        return f"{self.parent.email} ({self.relationship_type}) -> {self.student.full_name}"
