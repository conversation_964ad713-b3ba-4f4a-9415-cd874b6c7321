from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal
import json

from .models import (
    FeeCategory, EnhancedFeeStructure, PaymentMethod, Invoice, InvoiceItem,
    EnhancedPayment, Scholarship, StudentScholarship, PaymentReminder, FeeDiscount,
    FeeStructure, StudentPayment, StudentFeeBalance
)
from .forms import (
    FeeCategoryForm, EnhancedFeeStructureForm
)
from apps.students.models import Student
from apps.academics.models import Grade

@login_required
def finance_dashboard(request):
    """Finance management dashboard with statistics and overview."""
    today = date.today()
    current_month = today.month
    current_year = today.year

    # Calculate key financial metrics
    total_revenue = EnhancedPayment.objects.filter(
        status='completed',
        payment_date__year=current_year
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    monthly_revenue = EnhancedPayment.objects.filter(
        status='completed',
        payment_date__year=current_year,
        payment_date__month=current_month
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    outstanding_balance = Invoice.objects.filter(
        status__in=['sent', 'viewed', 'overdue', 'partially_paid']
    ).aggregate(total=Sum('balance_due'))['total'] or Decimal('0.00')

    overdue_amount = Invoice.objects.filter(
        status='overdue'
    ).aggregate(total=Sum('balance_due'))['total'] or Decimal('0.00')

    # Recent activities
    recent_payments = EnhancedPayment.objects.filter(
        status='completed'
    ).select_related('student', 'payment_method').order_by('-payment_date')[:10]

    overdue_invoices = Invoice.objects.filter(
        status='overdue'
    ).select_related('student').order_by('due_date')[:10]

    pending_scholarships = StudentScholarship.objects.filter(
        status='pending'
    ).select_related('student', 'scholarship').order_by('-application_date')[:5]

    # Statistics
    total_students_with_balance = Invoice.objects.filter(
        balance_due__gt=0
    ).values('student').distinct().count()

    total_active_scholarships = StudentScholarship.objects.filter(
        status='approved'
    ).count()

    context = {
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'outstanding_balance': outstanding_balance,
        'overdue_amount': overdue_amount,
        'recent_payments': recent_payments,
        'overdue_invoices': overdue_invoices,
        'pending_scholarships': pending_scholarships,
        'total_students_with_balance': total_students_with_balance,
        'total_active_scholarships': total_active_scholarships,
        'total_invoices': Invoice.objects.count(),
        'total_payments': EnhancedPayment.objects.filter(status='completed').count(),
        'payment_methods': PaymentMethod.objects.filter(is_active=True).count(),
        'fee_categories': FeeCategory.objects.filter(is_active=True).count(),
    }
    return render(request, 'finance/dashboard.html', context)

@login_required
def fee_structure_list(request):
    """List all fee structures with search and filter functionality."""
    fee_structures = EnhancedFeeStructure.objects.select_related(
        'category', 'grade'
    ).filter(is_active=True).order_by('-academic_year', 'grade', 'category')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        fee_structures = fee_structures.filter(
            Q(name__icontains=search_query) |
            Q(category__name__icontains=search_query) |
            Q(grade__name__icontains=search_query)
        )

    # Filter by grade
    grade_filter = request.GET.get('grade')
    if grade_filter:
        fee_structures = fee_structures.filter(grade_id=grade_filter)

    # Filter by academic year
    year_filter = request.GET.get('year')
    if year_filter:
        fee_structures = fee_structures.filter(academic_year=year_filter)

    paginator = Paginator(fee_structures, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'fee_structures': page_obj,
        'grades': Grade.objects.all(),
        'years': range(date.today().year - 2, date.today().year + 3),
        'search_query': search_query,
        'grade_filter': grade_filter,
        'year_filter': year_filter,
    }
    return render(request, 'finance/fee_structure_list.html', context)

@login_required
def add_fee_structure(request):
    """Add a new fee structure."""
    if request.method == 'POST':
        form = EnhancedFeeStructureForm(request.POST)
        if form.is_valid():
            fee_structure = form.save(commit=False)
            fee_structure.created_by = request.user
            fee_structure.save()
            messages.success(request, f'Fee structure "{fee_structure.name}" has been added successfully.')
            return redirect('finance:fee_structure_list')
    else:
        form = EnhancedFeeStructureForm()

    context = {'form': form}
    return render(request, 'finance/add_fee_structure.html', context)

@login_required
def edit_fee_structure(request, pk):
    """Edit an existing fee structure."""
    fee_structure = get_object_or_404(EnhancedFeeStructure, pk=pk)

    if request.method == 'POST':
        form = EnhancedFeeStructureForm(request.POST, instance=fee_structure)
        if form.is_valid():
            form.save()
            messages.success(request, f'Fee structure "{fee_structure.name}" has been updated successfully.')
            return redirect('finance:fee_structure_list')
    else:
        form = EnhancedFeeStructureForm(instance=fee_structure)

    context = {'form': form, 'fee_structure': fee_structure}
    return render(request, 'finance/edit_fee_structure.html', context)

@login_required
def delete_fee_structure(request, pk):
    """Delete a fee structure."""
    fee_structure = get_object_or_404(EnhancedFeeStructure, pk=pk)

    if request.method == 'POST':
        fee_structure.is_active = False
        fee_structure.save()
        messages.success(request, f'Fee structure "{fee_structure.name}" has been deleted successfully.')
        return redirect('finance:fee_structure_list')

    context = {'fee_structure': fee_structure}
    return render(request, 'finance/delete_fee_structure.html', context)

@login_required
def invoice_list(request):
    """List all invoices with search and filter functionality."""
    invoices = Invoice.objects.select_related('student').order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search_query) |
            Q(student__full_name__icontains=search_query) |
            Q(student__student_id__icontains=search_query)
        )

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        invoices = invoices.filter(status=status_filter)

    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'invoices': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': Invoice.INVOICE_STATUSES,
    }
    return render(request, 'finance/invoice_list.html', context)

@login_required
def generate_invoice(request):
    """Generate a new invoice."""
    if request.method == 'POST':
        student_id = request.POST.get('student')
        fee_structure_ids = request.POST.getlist('fee_structures')
        due_date = request.POST.get('due_date')

        if student_id and fee_structure_ids and due_date:
            student = get_object_or_404(Student, pk=student_id)

            # Create invoice
            invoice = Invoice.objects.create(
                student=student,
                due_date=due_date,
                status='draft',
                created_by=request.user
            )

            # Add invoice items
            total_amount = Decimal('0.00')
            for fee_id in fee_structure_ids:
                fee_structure = get_object_or_404(EnhancedFeeStructure, pk=fee_id)
                InvoiceItem.objects.create(
                    invoice=invoice,
                    fee_structure=fee_structure,
                    amount=fee_structure.amount,
                    description=fee_structure.name
                )
                total_amount += fee_structure.amount

            # Update invoice totals
            invoice.total_amount = total_amount
            invoice.balance_due = total_amount
            invoice.status = 'sent'
            invoice.save()

            messages.success(request, f'Invoice #{invoice.invoice_number} generated successfully for {student.full_name}.')
            return redirect('finance:invoice_detail', pk=invoice.pk)

    context = {
        'students': Student.objects.filter(status='active').order_by('full_name'),
        'fee_structures': EnhancedFeeStructure.objects.filter(is_active=True).order_by('name'),
    }
    return render(request, 'finance/generate_invoice.html', context)

@login_required
def invoice_detail(request, pk):
    """View invoice details."""
    invoice = get_object_or_404(Invoice, pk=pk)
    invoice_items = InvoiceItem.objects.filter(invoice=invoice)
    payments = EnhancedPayment.objects.filter(invoice=invoice).order_by('-payment_date')

    context = {
        'invoice': invoice,
        'invoice_items': invoice_items,
        'payments': payments,
    }
    return render(request, 'finance/invoice_detail.html', context)

@login_required
def payment_list(request):
    """List all payments with search and filter functionality."""
    payments = EnhancedPayment.objects.select_related('student', 'payment_method').order_by('-payment_date')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        payments = payments.filter(
            Q(student__full_name__icontains=search_query) |
            Q(student__student_id__icontains=search_query) |
            Q(reference_number__icontains=search_query)
        )

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        payments = payments.filter(status=status_filter)

    paginator = Paginator(payments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'payments': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': EnhancedPayment.PAYMENT_STATUSES,
    }
    return render(request, 'finance/payment_list.html', context)

@login_required
def record_payment(request):
    """Record a new payment."""
    if request.method == 'POST':
        student_id = request.POST.get('student')
        invoice_id = request.POST.get('invoice')
        amount = request.POST.get('amount')
        payment_method_id = request.POST.get('payment_method')
        reference_number = request.POST.get('reference_number')

        if student_id and amount and payment_method_id:
            student = get_object_or_404(Student, pk=student_id)
            payment_method = get_object_or_404(PaymentMethod, pk=payment_method_id)

            payment = EnhancedPayment.objects.create(
                student=student,
                amount=Decimal(amount),
                payment_method=payment_method,
                reference_number=reference_number,
                payment_date=timezone.now().date(),
                status='completed',
                recorded_by=request.user
            )

            # Update invoice if specified
            if invoice_id:
                invoice = get_object_or_404(Invoice, pk=invoice_id)
                payment.invoice = invoice
                payment.save()

                # Update invoice balance
                invoice.balance_due -= payment.amount
                if invoice.balance_due <= 0:
                    invoice.status = 'paid'
                elif invoice.balance_due < invoice.total_amount:
                    invoice.status = 'partially_paid'
                invoice.save()

            messages.success(request, f'Payment of ${amount} recorded successfully for {student.full_name}.')
            return redirect('finance:payment_detail', pk=payment.pk)

    context = {
        'students': Student.objects.filter(status='active').order_by('full_name'),
        'payment_methods': PaymentMethod.objects.filter(is_active=True),
        'outstanding_invoices': Invoice.objects.filter(status__in=['sent', 'viewed', 'overdue', 'partially_paid']).order_by('-created_at'),
    }
    return render(request, 'finance/record_payment.html', context)

@login_required
def payment_detail(request, pk):
    """View payment details."""
    payment = get_object_or_404(EnhancedPayment, pk=pk)

    context = {
        'payment': payment,
    }
    return render(request, 'finance/payment_detail.html', context)

@login_required
def scholarship_list(request):
    """List all scholarships."""
    scholarships = Scholarship.objects.filter(is_active=True).order_by('name')

    # Calculate statistics
    active_count = scholarships.count()
    recipients_count = StudentScholarship.objects.filter(status='approved').count()
    total_budget = scholarships.aggregate(total=Sum('total_budget'))['total'] or Decimal('0.00')

    context = {
        'scholarships': scholarships,
        'active_count': active_count,
        'recipients_count': recipients_count,
        'total_budget': total_budget,
    }
    return render(request, 'finance/scholarship_list.html', context)

@login_required
def add_scholarship(request):
    """Add a new scholarship."""
    if request.method == 'POST':
        # For now, just redirect back with a success message
        messages.success(request, 'Add scholarship functionality will be implemented soon.')
        return redirect('finance:scholarship_list')

    # For now, redirect to list with info message
    messages.info(request, 'Add scholarship form will be available soon.')
    return redirect('finance:scholarship_list')

@login_required
def scholarship_detail(request, pk):
    """View scholarship details."""
    try:
        scholarship = get_object_or_404(Scholarship, pk=pk)
        recipients = StudentScholarship.objects.filter(scholarship=scholarship).select_related('student')

        context = {
            'scholarship': scholarship,
            'recipients': recipients,
        }
        return render(request, 'finance/scholarship_detail.html', context)
    except Exception:
        messages.error(request, 'Scholarship detail view will be available soon.')
        return redirect('finance:scholarship_list')

@login_required
def edit_scholarship(request, pk):
    """Edit a scholarship."""
    try:
        scholarship = get_object_or_404(Scholarship, pk=pk)
        if request.method == 'POST':
            messages.success(request, 'Edit scholarship functionality will be implemented soon.')
            return redirect('finance:scholarship_detail', pk=pk)

        messages.info(request, 'Edit scholarship form will be available soon.')
        return redirect('finance:scholarship_detail', pk=pk)
    except Exception:
        messages.error(request, 'Edit scholarship functionality will be available soon.')
        return redirect('finance:scholarship_list')

@login_required
def delete_scholarship(request, pk):
    """Delete a scholarship."""
    try:
        scholarship = get_object_or_404(Scholarship, pk=pk)
        if request.method == 'POST':
            scholarship.is_active = False
            scholarship.save()
            messages.success(request, f'Scholarship "{scholarship.name}" has been deactivated.')
            return redirect('finance:scholarship_list')

        messages.warning(request, 'Delete confirmation will be available soon.')
        return redirect('finance:scholarship_list')
    except Exception:
        messages.error(request, 'Delete scholarship functionality will be available soon.')
        return redirect('finance:scholarship_list')

@login_required
def scholarship_recipients(request, pk):
    """View scholarship recipients."""
    try:
        scholarship = get_object_or_404(Scholarship, pk=pk)
        recipients = StudentScholarship.objects.filter(scholarship=scholarship).select_related('student')

        context = {
            'scholarship': scholarship,
            'recipients': recipients,
        }
        return render(request, 'finance/scholarship_recipients.html', context)
    except Exception:
        messages.error(request, 'Scholarship recipients view will be available soon.')
        return redirect('finance:scholarship_list')

@login_required
def scholarship_applications(request):
    """List scholarship applications."""
    applications = StudentScholarship.objects.select_related('student', 'scholarship').order_by('-application_date')

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        applications = applications.filter(status=status_filter)

    paginator = Paginator(applications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'applications': page_obj,
        'status_filter': status_filter,
        'status_choices': StudentScholarship.STATUS_CHOICES,
    }
    return render(request, 'finance/scholarship_applications.html', context)

@login_required
def outstanding_balances(request):
    """View students with outstanding balances."""
    invoices = Invoice.objects.filter(
        balance_due__gt=0
    ).select_related('student').order_by('-balance_due')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        invoices = invoices.filter(
            Q(student__full_name__icontains=search_query) |
            Q(student__student_id__icontains=search_query)
        )

    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'invoices': page_obj,
        'search_query': search_query,
    }
    return render(request, 'finance/outstanding_balances.html', context)

@login_required
def payment_calculator(request):
    """Payment calculator tool."""
    context = {}
    return render(request, 'finance/payment_calculator.html', context)

@login_required
def financial_reports(request):
    """Financial reports dashboard."""
    context = {
        'total_revenue': EnhancedPayment.objects.filter(status='completed').aggregate(total=Sum('amount'))['total'] or Decimal('0.00'),
        'total_outstanding': Invoice.objects.filter(balance_due__gt=0).aggregate(total=Sum('balance_due'))['total'] or Decimal('0.00'),
    }
    return render(request, 'finance/financial_reports.html', context)

@login_required
def fee_categories(request):
    """Manage fee categories."""
    categories = FeeCategory.objects.filter(is_active=True).order_by('name')

    context = {
        'categories': categories,
    }
    return render(request, 'finance/fee_categories.html', context)

@login_required
def payment_methods(request):
    """Manage payment methods."""
    methods = PaymentMethod.objects.all().order_by('name')

    context = {
        'methods': methods,
    }
    return render(request, 'finance/payment_methods.html', context)

@login_required
def send_payment_reminder(request, invoice_id):
    """Send payment reminder for an invoice."""
    invoice = get_object_or_404(Invoice, pk=invoice_id)

    if request.method == 'POST':
        # Create payment reminder record
        PaymentReminder.objects.create(
            invoice=invoice,
            reminder_type='email',
            sent_by=request.user,
            sent_at=timezone.now()
        )

        messages.success(request, f'Payment reminder sent to {invoice.student.full_name}.')
        return redirect('finance:invoice_detail', pk=invoice.pk)

    context = {
        'invoice': invoice,
    }
    return render(request, 'finance/send_reminder.html', context)

@login_required
def salary_management(request):
    """Salary management dashboard."""
    context = {
        'total_teachers': 50,  # Mock data
        'total_staff': 25,     # Mock data
        'monthly_payroll': 125000,  # Mock data
        'pending_payments': 5,      # Mock data
    }
    return render(request, 'finance/salary_management.html', context)
