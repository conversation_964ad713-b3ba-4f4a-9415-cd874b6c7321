from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from datetime import date

from .models import (
    Teacher, TeacherQualification, TeacherDocument, TeacherSubjectSpecialization,
    TeacherAssignment, TeacherPerformanceEvaluation, TeacherEmploymentHistory,
    TeacherDepartmentTransfer, TeacherWorkload
)
from .forms import (
    TeacherRegistrationForm, TeacherProfileForm, TeacherQualificationForm,
    TeacherDocumentForm, TeacherSubjectSpecializationForm, TeacherAssignmentForm,
    TeacherSearchForm, BulkTeacherAssignmentForm
)
from apps.academics.models import Department, Subject, Grade


@login_required
def teacher_dashboard(request):
    """
    Teacher management dashboard
    """
    # Statistics
    total_teachers = Teacher.objects.count()
    active_teachers = Teacher.objects.filter(status='active').count()
    tsc_teachers = Teacher.objects.filter(employment_type='TSC').count()
    bom_teachers = Teacher.objects.filter(employment_type='BOM').count()

    # Recent additions
    recent_teachers = Teacher.objects.order_by('-created_at')[:5]

    # Department distribution
    department_stats = Department.objects.annotate(
        teacher_count=Count('teacher')
    ).order_by('-teacher_count')[:5]

    # Performance overview
    avg_performance = TeacherPerformanceEvaluation.objects.aggregate(
        avg_rating=Avg('overall_rating')
    )['avg_rating'] or 0

    context = {
        'total_teachers': total_teachers,
        'active_teachers': active_teachers,
        'tsc_teachers': tsc_teachers,
        'bom_teachers': bom_teachers,
        'recent_teachers': recent_teachers,
        'department_stats': department_stats,
        'avg_performance': round(avg_performance, 1),
    }

    return render(request, 'teachers/dashboard.html', context)


@login_required
def teacher_list(request):
    """
    List all teachers with search and filtering
    """
    form = TeacherSearchForm(request.GET)
    teachers = Teacher.objects.select_related('department').order_by('last_name', 'first_name')

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        department = form.cleaned_data.get('department')
        employment_type = form.cleaned_data.get('employment_type')
        status = form.cleaned_data.get('status')
        gender = form.cleaned_data.get('gender')

        if search_query:
            teachers = teachers.filter(
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(full_name__icontains=search_query) |
                Q(employee_id__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(national_id__icontains=search_query)
            )

        if department:
            teachers = teachers.filter(department=department)

        if employment_type:
            teachers = teachers.filter(employment_type=employment_type)

        if status:
            teachers = teachers.filter(status=status)

        if gender:
            teachers = teachers.filter(gender=gender)

    # Pagination
    paginator = Paginator(teachers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'teachers': page_obj,
    }

    return render(request, 'teachers/list.html', context)


@login_required
def teacher_register(request):
    """
    Register a new teacher
    """
    if request.method == 'POST':
        form = TeacherRegistrationForm(request.POST, request.FILES)
        if form.is_valid():
            teacher = form.save(commit=False)
            teacher.created_by = request.user
            teacher.save()

            # Create employment history record
            TeacherEmploymentHistory.objects.create(
                teacher=teacher,
                status_change='hired',
                effective_date=teacher.employment_date,
                new_status=teacher.employment_status,
                reason='Initial employment',
                processed_by=request.user
            )

            messages.success(request, f'Teacher {teacher.full_name} has been registered successfully.')
            return redirect('teachers:detail', pk=teacher.pk)
    else:
        form = TeacherRegistrationForm()

    context = {
        'form': form,
        'title': 'Register New Teacher'
    }

    return render(request, 'teachers/teacher_register.html', context)


@login_required
def teacher_detail(request, pk):
    """
    Teacher detail view
    """
    teacher = get_object_or_404(Teacher, pk=pk)

    # Get related data
    qualifications = teacher.qualifications.all()
    documents = teacher.documents.all()
    specializations = teacher.subject_specializations.select_related('subject').all()
    assignments = teacher.assignments.select_related('subject', 'grade').filter(is_active=True)
    recent_evaluations = teacher.performance_evaluations.order_by('-evaluation_date')[:3]
    employment_history = teacher.employment_history.order_by('-effective_date')[:5]
    current_workload = teacher.workloads.filter(
        academic_year=2024,
        term=1
    ).first()

    context = {
        'teacher': teacher,
        'qualifications': qualifications,
        'documents': documents,
        'specializations': specializations,
        'assignments': assignments,
        'recent_evaluations': recent_evaluations,
        'employment_history': employment_history,
        'current_workload': current_workload,
    }

    return render(request, 'teachers/teacher_detail.html', context)


@login_required
def teacher_edit(request, pk):
    """
    Edit teacher profile
    """
    teacher = get_object_or_404(Teacher, pk=pk)

    if request.method == 'POST':
        form = TeacherProfileForm(request.POST, request.FILES, instance=teacher)
        if form.is_valid():
            teacher = form.save(commit=False)
            teacher.updated_by = request.user
            teacher.save()
            messages.success(request, f'Teacher {teacher.full_name} profile updated successfully.')
            return redirect('teachers:detail', pk=teacher.pk)
    else:
        form = TeacherProfileForm(instance=teacher)

    context = {
        'form': form,
        'teacher': teacher,
        'title': f'Edit {teacher.full_name}'
    }

    return render(request, 'teachers/teacher_edit.html', context)


@login_required
def teacher_delete(request, pk):
    """
    Delete a teacher record
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    
    # Check if the user has permission to delete teachers
    if not request.user.has_perm('teachers.delete_teacher'):
        messages.error(request, 'You do not have permission to delete teachers.')
        return redirect('teachers:detail', pk=teacher.pk)
    
    if request.method == 'POST':
        try:
            teacher_name = str(teacher)
            teacher.delete()
            messages.success(request, f'Teacher {teacher_name} has been deleted successfully.')
            return redirect('teachers:list')
        except Exception as e:
            messages.error(request, f'Error deleting teacher: {str(e)}')
            return redirect('teachers:detail', pk=teacher.pk)
    
    # If not a POST request, redirect to the teacher detail page
    return redirect('teachers:detail', pk=teacher.pk)


@login_required
def teacher_qualifications(request, pk):
    """
    Manage teacher qualifications
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    qualifications = teacher.qualifications.all()

    if request.method == 'POST':
        form = TeacherQualificationForm(request.POST)
        if form.is_valid():
            qualification = form.save(commit=False)
            qualification.teacher = teacher
            qualification.save()
            messages.success(request, 'Qualification added successfully.')
            return redirect('teachers:qualifications', pk=teacher.pk)
    else:
        form = TeacherQualificationForm()

    context = {
        'teacher': teacher,
        'qualifications': qualifications,
        'form': form,
    }

    return render(request, 'teachers/qualifications.html', context)


@login_required
def teacher_documents(request, pk):
    """
    Manage teacher documents
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    documents = teacher.documents.all()

    if request.method == 'POST':
        form = TeacherDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.teacher = teacher
            document.uploaded_by = request.user
            document.save()
            messages.success(request, 'Document uploaded successfully.')
            return redirect('teachers:documents', pk=teacher.pk)
    else:
        form = TeacherDocumentForm()

    context = {
        'teacher': teacher,
        'documents': documents,
        'form': form,
    }

    return render(request, 'teachers/documents.html', context)


@login_required
def teacher_specializations(request, pk):
    """
    Manage teacher subject specializations
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    specializations = teacher.subject_specializations.select_related('subject').all()

    if request.method == 'POST':
        form = TeacherSubjectSpecializationForm(request.POST)
        if form.is_valid():
            specialization = form.save(commit=False)
            specialization.teacher = teacher
            specialization.save()
            messages.success(request, 'Subject specialization added successfully.')
            return redirect('teachers:specializations', pk=teacher.pk)
    else:
        form = TeacherSubjectSpecializationForm()

    context = {
        'teacher': teacher,
        'specializations': specializations,
        'form': form,
    }

    return render(request, 'teachers/specializations.html', context)


@login_required
def teacher_assignments(request, pk):
    """
    Manage teacher assignments
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    assignments = teacher.assignments.select_related('subject', 'grade').all()

    if request.method == 'POST':
        form = TeacherAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.teacher = teacher
            assignment.created_by = request.user
            assignment.save()

            # Update teacher workload
            update_teacher_workload(teacher, assignment.academic_year, assignment.term)

            messages.success(request, 'Assignment created successfully.')
            return redirect('teachers:assignments', pk=teacher.pk)
    else:
        form = TeacherAssignmentForm()
        form.fields['teacher'].initial = teacher
        form.fields['teacher'].widget.attrs['readonly'] = True

    context = {
        'teacher': teacher,
        'assignments': assignments,
        'form': form,
    }

    return render(request, 'teachers/assignments.html', context)


@login_required
def bulk_teacher_assignment(request):
    """
    Bulk teacher assignment
    """
    if request.method == 'POST':
        form = BulkTeacherAssignmentForm(request.POST)
        if form.is_valid():
            teachers = form.cleaned_data['teachers']
            subject = form.cleaned_data['subject']
            grade = form.cleaned_data['grade']
            assignment_type = form.cleaned_data['assignment_type']
            academic_year = form.cleaned_data['academic_year']
            term = form.cleaned_data['term']
            lessons_per_week = form.cleaned_data['lessons_per_week']
            start_date = form.cleaned_data['start_date']

            created_count = 0
            for teacher in teachers:
                assignment, created = TeacherAssignment.objects.get_or_create(
                    teacher=teacher,
                    subject=subject,
                    grade=grade,
                    academic_year=academic_year,
                    term=term,
                    defaults={
                        'assignment_type': assignment_type,
                        'lessons_per_week': lessons_per_week,
                        'start_date': start_date,
                        'created_by': request.user,
                    }
                )
                if created:
                    created_count += 1
                    # Update teacher workload
                    update_teacher_workload(teacher, academic_year, term)

            messages.success(request, f'{created_count} teacher assignments created successfully.')
            return redirect('teachers:list')
    else:
        form = BulkTeacherAssignmentForm()

    context = {
        'form': form,
        'title': 'Bulk Teacher Assignment'
    }

    return render(request, 'teachers/bulk_assignment.html', context)


def update_teacher_workload(teacher, academic_year, term):
    """
    Update teacher workload calculation
    """
    workload, created = TeacherWorkload.objects.get_or_create(
        teacher=teacher,
        academic_year=academic_year,
        term=term
    )

    # Calculate totals from assignments
    assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=academic_year,
        term=term,
        is_active=True
    )

    total_lessons = sum(assignment.lessons_per_week for assignment in assignments)
    total_subjects = assignments.values('subject').distinct().count()
    total_classes = assignments.values('grade', 'section').distinct().count()

    workload.total_lessons_per_week = total_lessons
    workload.number_of_subjects = total_subjects
    workload.number_of_classes = total_classes
    workload.is_class_teacher = teacher.is_class_teacher
    workload.is_department_head = teacher.is_department_head
    workload.save()

    # Update teacher's current teaching load
    teacher.current_teaching_load = total_lessons
    teacher.save(update_fields=['current_teaching_load'])


@login_required
@require_http_methods(["POST"])
def teacher_status_change(request, pk):
    """
    Change teacher status
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    new_status = request.POST.get('status')
    reason = request.POST.get('reason', '')

    if new_status in dict(Teacher.STATUS_CHOICES):
        old_status = teacher.status
        teacher.status = new_status
        teacher.updated_by = request.user
        teacher.save()

        # Create employment history record
        TeacherEmploymentHistory.objects.create(
            teacher=teacher,
            status_change=new_status,
            effective_date=date.today(),
            previous_status=old_status,
            new_status=new_status,
            reason=reason,
            processed_by=request.user
        )

        messages.success(request, f'Teacher status changed to {new_status}.')
        return JsonResponse({'success': True})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
def teacher_workload_report(request):
    """
    Teacher workload analysis report
    """
    academic_year = request.GET.get('academic_year', 2024)
    term = request.GET.get('term', 1)

    workloads = TeacherWorkload.objects.filter(
        academic_year=academic_year,
        term=term
    ).select_related('teacher').order_by('-workload_percentage')

    # Statistics
    overloaded_teachers = workloads.filter(is_overloaded=True).count()
    avg_workload = workloads.aggregate(avg=Avg('workload_percentage'))['avg'] or 0

    context = {
        'workloads': workloads,
        'academic_year': academic_year,
        'term': term,
        'overloaded_teachers': overloaded_teachers,
        'avg_workload': round(avg_workload, 1),
    }

    return render(request, 'teachers/workload_report.html', context)
