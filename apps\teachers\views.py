from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from datetime import date

from .models import (
    Teacher, TeacherQualification, TeacherDocument, TeacherSubjectSpecialization,
    TeacherAssignment, TeacherPerformanceEvaluation, TeacherEmploymentHistory,
    TeacherDepartmentTransfer, TeacherWorkload
)
from .forms import (
    TeacherRegistrationForm, TeacherProfileForm, TeacherQualificationForm,
    TeacherDocumentForm, TeacherSubjectSpecializationForm, TeacherAssignmentForm,
    TeacherSearchForm, BulkTeacherAssignmentForm
)
from apps.academics.models import Department, Subject, Grade
from apps.students.models import Student
from apps.exams.models import (
    Exam, ExamTimetable, EnhancedStudentExamResult,
    SubjectPerformanceAnalysis, ExamNotification
)


@login_required
def teacher_dashboard(request):
    """
    Teacher management dashboard
    """
    # Statistics
    total_teachers = Teacher.objects.count()
    active_teachers = Teacher.objects.filter(status='active').count()
    tsc_teachers = Teacher.objects.filter(employment_type='TSC').count()
    bom_teachers = Teacher.objects.filter(employment_type='BOM').count()

    # Recent additions
    recent_teachers = Teacher.objects.order_by('-created_at')[:5]

    # Department distribution
    department_stats = Department.objects.annotate(
        teacher_count=Count('teacher')
    ).order_by('-teacher_count')[:5]

    # Performance overview
    avg_performance = TeacherPerformanceEvaluation.objects.aggregate(
        avg_rating=Avg('overall_rating')
    )['avg_rating'] or 0

    context = {
        'total_teachers': total_teachers,
        'active_teachers': active_teachers,
        'tsc_teachers': tsc_teachers,
        'bom_teachers': bom_teachers,
        'recent_teachers': recent_teachers,
        'department_stats': department_stats,
        'avg_performance': round(avg_performance, 1),
    }

    return render(request, 'teachers/dashboard.html', context)


@login_required
def teacher_list(request):
    """
    List all teachers with search and filtering
    """
    form = TeacherSearchForm(request.GET)
    teachers = Teacher.objects.select_related('department').order_by('last_name', 'first_name')

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        department = form.cleaned_data.get('department')
        employment_type = form.cleaned_data.get('employment_type')
        status = form.cleaned_data.get('status')
        gender = form.cleaned_data.get('gender')

        if search_query:
            teachers = teachers.filter(
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(full_name__icontains=search_query) |
                Q(employee_id__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(national_id__icontains=search_query)
            )

        if department:
            teachers = teachers.filter(department=department)

        if employment_type:
            teachers = teachers.filter(employment_type=employment_type)

        if status:
            teachers = teachers.filter(status=status)

        if gender:
            teachers = teachers.filter(gender=gender)

    # Pagination
    paginator = Paginator(teachers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'teachers': page_obj,
    }

    return render(request, 'teachers/list.html', context)


@login_required
def teacher_register(request):
    """
    Register a new teacher
    """
    if request.method == 'POST':
        form = TeacherRegistrationForm(request.POST, request.FILES)
        if form.is_valid():
            teacher = form.save(commit=False)
            teacher.created_by = request.user
            teacher.save()

            # Create employment history record
            TeacherEmploymentHistory.objects.create(
                teacher=teacher,
                status_change='hired',
                effective_date=teacher.employment_date,
                new_status=teacher.employment_status,
                reason='Initial employment',
                processed_by=request.user
            )

            messages.success(request, f'Teacher {teacher.full_name} has been registered successfully.')
            return redirect('teachers:detail', pk=teacher.pk)
    else:
        form = TeacherRegistrationForm()

    context = {
        'form': form,
        'title': 'Register New Teacher'
    }

    return render(request, 'teachers/teacher_register.html', context)


@login_required
def teacher_detail(request, pk):
    """
    Teacher detail view
    """
    teacher = get_object_or_404(Teacher, pk=pk)

    # Get related data
    qualifications = teacher.qualifications.all()
    documents = teacher.documents.all()
    specializations = teacher.subject_specializations.select_related('subject').all()
    assignments = teacher.assignments.select_related('subject', 'grade').filter(is_active=True)
    recent_evaluations = teacher.performance_evaluations.order_by('-evaluation_date')[:3]
    employment_history = teacher.employment_history.order_by('-effective_date')[:5]
    current_workload = teacher.workloads.filter(
        academic_year=2024,
        term=1
    ).first()

    context = {
        'teacher': teacher,
        'qualifications': qualifications,
        'documents': documents,
        'specializations': specializations,
        'assignments': assignments,
        'recent_evaluations': recent_evaluations,
        'employment_history': employment_history,
        'current_workload': current_workload,
    }

    return render(request, 'teachers/teacher_detail.html', context)


@login_required
def teacher_edit(request, pk):
    """
    Edit teacher profile
    """
    teacher = get_object_or_404(Teacher, pk=pk)

    if request.method == 'POST':
        form = TeacherProfileForm(request.POST, request.FILES, instance=teacher)
        if form.is_valid():
            teacher = form.save(commit=False)
            teacher.updated_by = request.user
            teacher.save()
            messages.success(request, f'Teacher {teacher.full_name} profile updated successfully.')
            return redirect('teachers:detail', pk=teacher.pk)
    else:
        form = TeacherProfileForm(instance=teacher)

    context = {
        'form': form,
        'teacher': teacher,
        'title': f'Edit {teacher.full_name}'
    }

    return render(request, 'teachers/teacher_edit.html', context)


@login_required
def teacher_delete(request, pk):
    """
    Delete a teacher record
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    
    # Check if the user has permission to delete teachers
    if not request.user.has_perm('teachers.delete_teacher'):
        messages.error(request, 'You do not have permission to delete teachers.')
        return redirect('teachers:detail', pk=teacher.pk)
    
    if request.method == 'POST':
        try:
            teacher_name = str(teacher)
            teacher.delete()
            messages.success(request, f'Teacher {teacher_name} has been deleted successfully.')
            return redirect('teachers:list')
        except Exception as e:
            messages.error(request, f'Error deleting teacher: {str(e)}')
            return redirect('teachers:detail', pk=teacher.pk)
    
    # If not a POST request, redirect to the teacher detail page
    return redirect('teachers:detail', pk=teacher.pk)


@login_required
def teacher_qualifications(request, pk):
    """
    Manage teacher qualifications
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    qualifications = teacher.qualifications.all()

    if request.method == 'POST':
        form = TeacherQualificationForm(request.POST)
        if form.is_valid():
            qualification = form.save(commit=False)
            qualification.teacher = teacher
            qualification.save()
            messages.success(request, 'Qualification added successfully.')
            return redirect('teachers:qualifications', pk=teacher.pk)
    else:
        form = TeacherQualificationForm()

    context = {
        'teacher': teacher,
        'qualifications': qualifications,
        'form': form,
    }

    return render(request, 'teachers/qualifications.html', context)


@login_required
def teacher_documents(request, pk):
    """
    Manage teacher documents
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    documents = teacher.documents.all()

    if request.method == 'POST':
        form = TeacherDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.teacher = teacher
            document.uploaded_by = request.user
            document.save()
            messages.success(request, 'Document uploaded successfully.')
            return redirect('teachers:documents', pk=teacher.pk)
    else:
        form = TeacherDocumentForm()

    context = {
        'teacher': teacher,
        'documents': documents,
        'form': form,
    }

    return render(request, 'teachers/documents.html', context)


@login_required
def teacher_specializations(request, pk):
    """
    Manage teacher subject specializations
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    specializations = teacher.subject_specializations.select_related('subject').all()

    if request.method == 'POST':
        form = TeacherSubjectSpecializationForm(request.POST)
        if form.is_valid():
            specialization = form.save(commit=False)
            specialization.teacher = teacher
            specialization.save()
            messages.success(request, 'Subject specialization added successfully.')
            return redirect('teachers:specializations', pk=teacher.pk)
    else:
        form = TeacherSubjectSpecializationForm()

    context = {
        'teacher': teacher,
        'specializations': specializations,
        'form': form,
    }

    return render(request, 'teachers/specializations.html', context)


@login_required
def teacher_assignments(request, pk):
    """
    Manage teacher assignments
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    assignments = teacher.assignments.select_related('subject', 'grade').all()

    if request.method == 'POST':
        form = TeacherAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.teacher = teacher
            assignment.created_by = request.user
            assignment.save()

            # Update teacher workload
            update_teacher_workload(teacher, assignment.academic_year, assignment.term)

            messages.success(request, 'Assignment created successfully.')
            return redirect('teachers:assignments', pk=teacher.pk)
    else:
        form = TeacherAssignmentForm()
        form.fields['teacher'].initial = teacher
        form.fields['teacher'].widget.attrs['readonly'] = True

    context = {
        'teacher': teacher,
        'assignments': assignments,
        'form': form,
    }

    return render(request, 'teachers/assignments.html', context)


@login_required
def bulk_teacher_assignment(request):
    """
    Bulk teacher assignment
    """
    if request.method == 'POST':
        form = BulkTeacherAssignmentForm(request.POST)
        if form.is_valid():
            teachers = form.cleaned_data['teachers']
            subject = form.cleaned_data['subject']
            grade = form.cleaned_data['grade']
            assignment_type = form.cleaned_data['assignment_type']
            academic_year = form.cleaned_data['academic_year']
            term = form.cleaned_data['term']
            lessons_per_week = form.cleaned_data['lessons_per_week']
            start_date = form.cleaned_data['start_date']

            created_count = 0
            for teacher in teachers:
                assignment, created = TeacherAssignment.objects.get_or_create(
                    teacher=teacher,
                    subject=subject,
                    grade=grade,
                    academic_year=academic_year,
                    term=term,
                    defaults={
                        'assignment_type': assignment_type,
                        'lessons_per_week': lessons_per_week,
                        'start_date': start_date,
                        'created_by': request.user,
                    }
                )
                if created:
                    created_count += 1
                    # Update teacher workload
                    update_teacher_workload(teacher, academic_year, term)

            messages.success(request, f'{created_count} teacher assignments created successfully.')
            return redirect('teachers:list')
    else:
        form = BulkTeacherAssignmentForm()

    context = {
        'form': form,
        'title': 'Bulk Teacher Assignment'
    }

    return render(request, 'teachers/bulk_assignment.html', context)


def update_teacher_workload(teacher, academic_year, term):
    """
    Update teacher workload calculation
    """
    workload, created = TeacherWorkload.objects.get_or_create(
        teacher=teacher,
        academic_year=academic_year,
        term=term
    )

    # Calculate totals from assignments
    assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=academic_year,
        term=term,
        is_active=True
    )

    total_lessons = sum(assignment.lessons_per_week for assignment in assignments)
    total_subjects = assignments.values('subject').distinct().count()
    total_classes = assignments.values('grade', 'section').distinct().count()

    workload.total_lessons_per_week = total_lessons
    workload.number_of_subjects = total_subjects
    workload.number_of_classes = total_classes
    workload.is_class_teacher = teacher.is_class_teacher
    workload.is_department_head = teacher.is_department_head
    workload.save()

    # Update teacher's current teaching load
    teacher.current_teaching_load = total_lessons
    teacher.save(update_fields=['current_teaching_load'])


@login_required
@require_http_methods(["POST"])
def teacher_status_change(request, pk):
    """
    Change teacher status
    """
    teacher = get_object_or_404(Teacher, pk=pk)
    new_status = request.POST.get('status')
    reason = request.POST.get('reason', '')

    if new_status in dict(Teacher.STATUS_CHOICES):
        old_status = teacher.status
        teacher.status = new_status
        teacher.updated_by = request.user
        teacher.save()

        # Create employment history record
        TeacherEmploymentHistory.objects.create(
            teacher=teacher,
            status_change=new_status,
            effective_date=date.today(),
            previous_status=old_status,
            new_status=new_status,
            reason=reason,
            processed_by=request.user
        )

        messages.success(request, f'Teacher status changed to {new_status}.')
        return JsonResponse({'success': True})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
def teacher_workload_report(request):
    """
    Teacher workload analysis report
    """
    academic_year = request.GET.get('academic_year', 2024)
    term = request.GET.get('term', 1)

    workloads = TeacherWorkload.objects.filter(
        academic_year=academic_year,
        term=term
    ).select_related('teacher').order_by('-workload_percentage')

    # Statistics
    overloaded_teachers = workloads.filter(is_overloaded=True).count()
    avg_workload = workloads.aggregate(avg=Avg('workload_percentage'))['avg'] or 0

    context = {
        'workloads': workloads,
        'academic_year': academic_year,
        'term': term,
        'overloaded_teachers': overloaded_teachers,
        'avg_workload': round(avg_workload, 1),
    }

    return render(request, 'teachers/workload_report.html', context)


# Exam-related views for teachers

@login_required
def teacher_exam_dashboard(request):
    """
    Exam dashboard for teachers showing their exam assignments and responsibilities
    """
    teacher = get_object_or_404(Teacher, user=request.user)

    # Get exams where teacher is chief invigilator
    chief_invigilated_exams = ExamTimetable.objects.filter(
        chief_invigilator=teacher
    ).select_related('exam', 'subject', 'grade').order_by('-exam_date')

    # Get exams where teacher is assistant invigilator
    assistant_invigilated_exams = ExamTimetable.objects.filter(
        assistant_invigilators=teacher
    ).select_related('exam', 'subject', 'grade').order_by('-exam_date')

    # Get subjects taught by this teacher for result entry
    teacher_assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=date.today().year
    ).select_related('subject', 'grade')

    # Get pending result entries for teacher's subjects
    pending_results = []
    for assignment in teacher_assignments:
        pending_exams = Exam.objects.filter(
            status__in=['active', 'completed'],
            results_published=False
        )
        for exam in pending_exams:
            timetable_entries = ExamTimetable.objects.filter(
                exam=exam,
                subject=assignment.subject,
                grade=assignment.grade
            )
            for entry in timetable_entries:
                results_count = EnhancedStudentExamResult.objects.filter(
                    timetable_entry=entry
                ).count()
                pending_results.append({
                    'exam': exam,
                    'subject': assignment.subject,
                    'grade': assignment.grade,
                    'timetable_entry': entry,
                    'results_count': results_count
                })

    # Get recent notifications
    notifications = ExamNotification.objects.filter(
        recipients=teacher.user
    ).order_by('-created_at')[:5]

    # Get performance analysis for teacher's subjects
    subject_performance = SubjectPerformanceAnalysis.objects.filter(
        teacher=teacher
    ).order_by('-exam__start_date')[:5]

    context = {
        'teacher': teacher,
        'chief_invigilated_exams': chief_invigilated_exams[:10],
        'assistant_invigilated_exams': assistant_invigilated_exams[:10],
        'teacher_assignments': teacher_assignments,
        'pending_results': pending_results[:10],
        'notifications': notifications,
        'subject_performance': subject_performance,
    }

    return render(request, 'teachers/exam_dashboard.html', context)


@login_required
def teacher_exam_assignments(request):
    """
    View all exam assignments for a teacher (invigilation duties)
    """
    teacher = get_object_or_404(Teacher, user=request.user)

    # Get all exam assignments
    chief_assignments = ExamTimetable.objects.filter(
        chief_invigilator=teacher
    ).select_related('exam', 'subject', 'grade', 'classroom').order_by('-exam_date')

    assistant_assignments = ExamTimetable.objects.filter(
        assistant_invigilators=teacher
    ).select_related('exam', 'subject', 'grade', 'classroom').order_by('-exam_date')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        chief_assignments = chief_assignments.filter(status=status_filter)
        assistant_assignments = assistant_assignments.filter(status=status_filter)

    # Pagination
    chief_paginator = Paginator(chief_assignments, 10)
    chief_page = request.GET.get('chief_page')
    chief_page_obj = chief_paginator.get_page(chief_page)

    assistant_paginator = Paginator(assistant_assignments, 10)
    assistant_page = request.GET.get('assistant_page')
    assistant_page_obj = assistant_paginator.get_page(assistant_page)

    context = {
        'teacher': teacher,
        'chief_page_obj': chief_page_obj,
        'assistant_page_obj': assistant_page_obj,
        'status_filter': status_filter,
        'status_choices': ExamTimetable.TIMETABLE_STATUS,
    }

    return render(request, 'teachers/exam_assignments.html', context)


@login_required
def teacher_result_entry(request):
    """
    Result entry interface for teachers
    """
    teacher = get_object_or_404(Teacher, user=request.user)

    # Get teacher's subject assignments
    assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=date.today().year
    ).select_related('subject', 'grade')

    # Get available exams for result entry
    available_exams = []
    for assignment in assignments:
        exams = Exam.objects.filter(
            status__in=['active', 'completed']
        ).order_by('-start_date')

        for exam in exams:
            timetable_entries = ExamTimetable.objects.filter(
                exam=exam,
                subject=assignment.subject,
                grade=assignment.grade
            )

            for entry in timetable_entries:
                results_count = EnhancedStudentExamResult.objects.filter(
                    timetable_entry=entry
                ).count()

                submitted_count = EnhancedStudentExamResult.objects.filter(
                    timetable_entry=entry,
                    status__in=['submitted', 'verified']
                ).count()

                available_exams.append({
                    'exam': exam,
                    'subject': assignment.subject,
                    'grade': assignment.grade,
                    'timetable_entry': entry,
                    'total_results': results_count,
                    'submitted_results': submitted_count,
                    'completion_percentage': (submitted_count / results_count * 100) if results_count > 0 else 0
                })

    # Sort by completion percentage (incomplete first)
    available_exams.sort(key=lambda x: x['completion_percentage'])

    context = {
        'teacher': teacher,
        'available_exams': available_exams,
    }

    return render(request, 'teachers/result_entry.html', context)


@login_required
def bulk_result_upload(request):
    """
    Bulk result upload interface for teachers
    """
    teacher = get_object_or_404(Teacher, user=request.user)

    if request.method == 'POST':
        if 'upload_file' in request.FILES:
            return handle_bulk_upload(request, teacher)

    # Get teacher's subject assignments for dropdown
    assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=date.today().year
    ).select_related('subject', 'grade')

    # Get available exams
    available_exams = []
    for assignment in assignments:
        exams = Exam.objects.filter(
            status__in=['active', 'completed']
        ).order_by('-start_date')

        for exam in exams:
            timetable_entries = ExamTimetable.objects.filter(
                exam=exam,
                subject=assignment.subject,
                grade=assignment.grade
            )

            for entry in timetable_entries:
                available_exams.append({
                    'exam': exam,
                    'subject': assignment.subject,
                    'grade': assignment.grade,
                    'timetable_entry': entry,
                })

    context = {
        'teacher': teacher,
        'available_exams': available_exams,
    }

    return render(request, 'teachers/bulk_result_upload.html', context)


def handle_bulk_upload(request, teacher):
    """
    Handle the bulk upload file processing
    """
    import pandas as pd
    import io
    from django.contrib import messages

    upload_file = request.FILES['upload_file']
    timetable_entry_id = request.POST.get('timetable_entry')

    if not timetable_entry_id:
        messages.error(request, 'Please select an exam and subject.')
        return redirect('teachers:bulk_result_upload')

    try:
        timetable_entry = ExamTimetable.objects.get(id=timetable_entry_id)

        # Verify teacher has permission for this exam/subject
        assignment_exists = TeacherAssignment.objects.filter(
            teacher=teacher,
            subject=timetable_entry.subject,
            grade=timetable_entry.grade,
            academic_year=date.today().year
        ).exists()

        if not assignment_exists:
            messages.error(request, 'You do not have permission to upload results for this exam/subject.')
            return redirect('teachers:bulk_result_upload')

        # Read the uploaded file
        if upload_file.name.endswith('.csv'):
            df = pd.read_csv(upload_file)
        elif upload_file.name.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(upload_file)
        else:
            messages.error(request, 'Please upload a CSV or Excel file.')
            return redirect('teachers:bulk_result_upload')

        # Validate required columns
        required_columns = ['student_id', 'marks']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            messages.error(request, f'Missing required columns: {", ".join(missing_columns)}')
            return redirect('teachers:bulk_result_upload')

        # Process the data
        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                student_id = str(row['student_id']).strip()
                marks = float(row['marks'])

                # Find the student
                try:
                    student = Student.objects.get(student_id=student_id)
                except Student.DoesNotExist:
                    errors.append(f'Row {index + 2}: Student ID {student_id} not found')
                    error_count += 1
                    continue

                # Validate marks
                if marks < 0 or marks > 100:
                    errors.append(f'Row {index + 2}: Invalid marks {marks} for student {student_id}')
                    error_count += 1
                    continue

                # Create or update result
                result, created = EnhancedStudentExamResult.objects.get_or_create(
                    student=student,
                    timetable_entry=timetable_entry,
                    defaults={
                        'marks_obtained': marks,
                        'status': 'submitted',
                        'entered_by': request.user,
                        'entry_date': date.today()
                    }
                )

                if not created:
                    # Update existing result
                    result.marks_obtained = marks
                    result.status = 'submitted'
                    result.entered_by = request.user
                    result.entry_date = date.today()
                    result.save()

                success_count += 1

            except Exception as e:
                errors.append(f'Row {index + 2}: {str(e)}')
                error_count += 1

        # Show results
        if success_count > 0:
            messages.success(request, f'Successfully uploaded {success_count} results.')

        if error_count > 0:
            error_message = f'{error_count} errors occurred:\n' + '\n'.join(errors[:10])
            if len(errors) > 10:
                error_message += f'\n... and {len(errors) - 10} more errors.'
            messages.error(request, error_message)

        return redirect('teachers:result_entry')

    except Exception as e:
        messages.error(request, f'Error processing file: {str(e)}')
        return redirect('teachers:bulk_result_upload')


@login_required
def download_result_template(request):
    """
    Download Excel template for bulk result upload
    """
    import pandas as pd
    from django.http import HttpResponse
    import io

    teacher = get_object_or_404(Teacher, user=request.user)
    timetable_entry_id = request.GET.get('timetable_entry')

    if not timetable_entry_id:
        messages.error(request, 'Please select an exam and subject first.')
        return redirect('teachers:bulk_result_upload')

    try:
        timetable_entry = ExamTimetable.objects.get(id=timetable_entry_id)

        # Get students for this grade
        students = Student.objects.filter(
            current_grade=timetable_entry.grade,
            status='active'
        ).order_by('student_id')

        # Create template data
        template_data = []
        for student in students:
            template_data.append({
                'student_id': student.student_id,
                'student_name': f"{student.first_name} {student.last_name}",
                'marks': '',  # Empty for teachers to fill
                'comments': ''  # Optional comments
            })

        # Create DataFrame
        df = pd.DataFrame(template_data)

        # Create Excel file
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Results', index=False)

            # Add instructions sheet
            instructions = pd.DataFrame({
                'Instructions': [
                    '1. Fill in the marks column with numerical values (0-100)',
                    '2. Do not modify the student_id or student_name columns',
                    '3. Comments column is optional',
                    '4. Save the file and upload it back to the system',
                    '5. Ensure all marks are between 0 and 100',
                    '',
                    f'Exam: {timetable_entry.exam.name}',
                    f'Subject: {timetable_entry.subject.name}',
                    f'Grade: {timetable_entry.grade.name}',
                    f'Date: {timetable_entry.exam_date}',
                ]
            })
            instructions.to_excel(writer, sheet_name='Instructions', index=False)

        output.seek(0)

        # Create response
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        filename = f"results_template_{timetable_entry.exam.name}_{timetable_entry.subject.name}_{timetable_entry.grade.name}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    except Exception as e:
        messages.error(request, f'Error generating template: {str(e)}')
        return redirect('teachers:bulk_result_upload')


@login_required
def teacher_student_management(request):
    """
    Student management interface for teachers showing their assigned classes and students
    """
    teacher = get_object_or_404(Teacher, user=request.user)

    # Get teacher's current assignments
    current_year = date.today().year
    assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=current_year,
        is_active=True
    ).select_related('subject', 'grade').distinct()

    # Get unique grades the teacher is assigned to
    assigned_grades = Grade.objects.filter(
        id__in=assignments.values_list('grade_id', flat=True)
    ).distinct()

    # Get students for each assigned grade
    grade_students = {}
    total_students = 0

    for grade in assigned_grades:
        students = Student.objects.filter(
            current_grade=grade,
            status='active'
        ).select_related('current_grade').order_by('student_id')

        grade_students[grade] = students
        total_students += students.count()

    # Get class teacher assignments (if teacher is a class teacher)
    class_teacher_grades = []
    if teacher.is_class_teacher:
        class_teacher_grades = Grade.objects.filter(
            classroom__current_class_teacher=teacher
        ).distinct()

    # Get recent exam results for teacher's subjects
    recent_results = EnhancedStudentExamResult.objects.filter(
        timetable_entry__subject__in=assignments.values_list('subject', flat=True),
        timetable_entry__grade__in=assigned_grades,
        status__in=['submitted', 'verified']
    ).select_related(
        'student', 'timetable_entry__subject', 'timetable_entry__exam'
    ).order_by('-entry_date')[:10]

    context = {
        'teacher': teacher,
        'assignments': assignments,
        'assigned_grades': assigned_grades,
        'grade_students': grade_students,
        'total_students': total_students,
        'class_teacher_grades': class_teacher_grades,
        'recent_results': recent_results,
    }

    return render(request, 'teachers/student_management.html', context)


@login_required
def teacher_student_detail(request, student_id):
    """
    Detailed view of a student for teachers (only for students in their assigned classes)
    """
    teacher = get_object_or_404(Teacher, user=request.user)
    student = get_object_or_404(Student, id=student_id)

    # Verify teacher has access to this student
    current_year = date.today().year
    teacher_grades = TeacherAssignment.objects.filter(
        teacher=teacher,
        academic_year=current_year,
        is_active=True
    ).values_list('grade_id', flat=True)

    if student.current_grade_id not in teacher_grades:
        messages.error(request, 'You do not have access to this student.')
        return redirect('teachers:student_management')

    # Get teacher's subjects for this student's grade
    teacher_subjects = TeacherAssignment.objects.filter(
        teacher=teacher,
        grade=student.current_grade,
        academic_year=current_year,
        is_active=True
    ).select_related('subject')

    # Get student's exam results for teacher's subjects
    exam_results = EnhancedStudentExamResult.objects.filter(
        student=student,
        timetable_entry__subject__in=teacher_subjects.values_list('subject', flat=True)
    ).select_related(
        'timetable_entry__exam', 'timetable_entry__subject'
    ).order_by('-timetable_entry__exam__start_date')

    # Calculate performance statistics for teacher's subjects
    subject_performance = {}
    for assignment in teacher_subjects:
        subject_results = exam_results.filter(
            timetable_entry__subject=assignment.subject
        )

        if subject_results.exists():
            avg_marks = subject_results.aggregate(
                avg_marks=Avg('marks_obtained')
            )['avg_marks']

            subject_performance[assignment.subject] = {
                'average_marks': round(avg_marks, 1) if avg_marks else 0,
                'total_exams': subject_results.count(),
                'latest_result': subject_results.first()
            }

    # Get student's attendance for teacher's subjects (if attendance tracking is available)
    # This would require integration with attendance app

    context = {
        'teacher': teacher,
        'student': student,
        'teacher_subjects': teacher_subjects,
        'exam_results': exam_results[:20],  # Limit to recent results
        'subject_performance': subject_performance,
    }

    return render(request, 'teachers/student_detail.html', context)


@login_required
def teacher_class_progress(request, grade_id):
    """
    Class progress overview for teachers showing performance across their subjects
    """
    teacher = get_object_or_404(Teacher, user=request.user)
    grade = get_object_or_404(Grade, id=grade_id)

    # Verify teacher has access to this grade
    current_year = date.today().year
    teacher_assignment = TeacherAssignment.objects.filter(
        teacher=teacher,
        grade=grade,
        academic_year=current_year,
        is_active=True
    ).first()

    if not teacher_assignment:
        messages.error(request, 'You do not have access to this class.')
        return redirect('teachers:student_management')

    # Get all students in this grade
    students = Student.objects.filter(
        current_grade=grade,
        status='active'
    ).order_by('student_id')

    # Get teacher's subjects for this grade
    teacher_subjects = TeacherAssignment.objects.filter(
        teacher=teacher,
        grade=grade,
        academic_year=current_year,
        is_active=True
    ).select_related('subject')

    # Get recent exam results for analysis
    recent_exams = Exam.objects.filter(
        status__in=['completed', 'active']
    ).order_by('-start_date')[:5]

    # Calculate class performance for each subject
    subject_performance = {}
    for assignment in teacher_subjects:
        subject_results = EnhancedStudentExamResult.objects.filter(
            student__current_grade=grade,
            timetable_entry__subject=assignment.subject,
            timetable_entry__exam__in=recent_exams
        ).select_related('student', 'timetable_entry__exam')

        if subject_results.exists():
            avg_marks = subject_results.aggregate(
                avg_marks=Avg('marks_obtained')
            )['avg_marks']

            # Get grade distribution
            grade_distribution = {
                'A': subject_results.filter(marks_obtained__gte=80).count(),
                'B': subject_results.filter(marks_obtained__gte=70, marks_obtained__lt=80).count(),
                'C': subject_results.filter(marks_obtained__gte=60, marks_obtained__lt=70).count(),
                'D': subject_results.filter(marks_obtained__gte=50, marks_obtained__lt=60).count(),
                'E': subject_results.filter(marks_obtained__lt=50).count(),
            }

            subject_performance[assignment.subject] = {
                'average_marks': round(avg_marks, 1) if avg_marks else 0,
                'total_results': subject_results.count(),
                'grade_distribution': grade_distribution,
                'recent_results': subject_results.order_by('-entry_date')[:10]
            }

    context = {
        'teacher': teacher,
        'grade': grade,
        'students': students,
        'teacher_subjects': teacher_subjects,
        'subject_performance': subject_performance,
        'recent_exams': recent_exams,
        'total_students': students.count(),
    }

    return render(request, 'teachers/class_progress.html', context)
