# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("academics", "0001_initial"),
        ("teachers", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Timetable",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("day", models.CharField(max_length=255)),
                ("start_time", models.DecimalField(decimal_places=2, max_digits=11)),
                ("end_time", models.DecimalField(decimal_places=2, max_digits=11)),
                ("period_number", models.IntegerField(blank=True, null=True)),
                ("is_break", models.BooleanField(default=False)),
                (
                    "classroom",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.classroom",
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
            ],
        ),
    ]
