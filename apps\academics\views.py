from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Grade, Department, Subject, SubjectRouting, ClassRoom
from .forms import (
    SchoolInfoForm, GradeForm, DepartmentForm, SubjectForm,
    ClassRoomForm, SystemSettingsForm
)
from apps.core.models import SchoolInfo, SystemSettings


@login_required
def academics_dashboard(request):
    """Academic dashboard with overview statistics"""
    context = {
        'title': 'Academic Dashboard',
        'total_grades': Grade.objects.count(),
        'total_subjects': Subject.objects.count(),
        'total_departments': Department.objects.count(),
        'total_classrooms': ClassRoom.objects.count(),
        'recent_grades': Grade.objects.all()[:5],
        'recent_subjects': Subject.objects.all()[:5],
    }
    return render(request, 'academics/dashboard.html', context)


@login_required
def academics_settings(request):
    """Academic settings overview page"""
    try:
        school_info = SchoolInfo.objects.first()
    except SchoolInfo.DoesNotExist:
        school_info = None

    context = {
        'title': 'Academic Settings',
        'school_info': school_info,
        'total_grades': Grade.objects.count(),
        'total_subjects': Subject.objects.count(),
        'total_departments': Department.objects.count(),
        'total_classrooms': ClassRoom.objects.count(),
    }
    return render(request, 'academics/settings.html', context)


@login_required
def school_info_view(request):
    """View school information"""
    try:
        school_info = SchoolInfo.objects.first()
    except SchoolInfo.DoesNotExist:
        school_info = None

    context = {
        'title': 'School Information',
        'school_info': school_info,
    }
    return render(request, 'academics/school_info.html', context)


@login_required
def school_info_edit(request):
    """Edit school information"""
    try:
        school_info = SchoolInfo.objects.first()
    except SchoolInfo.DoesNotExist:
        school_info = None

    if request.method == 'POST':
        form = SchoolInfoForm(request.POST, request.FILES, instance=school_info)
        if form.is_valid():
            form.save()
            messages.success(request, 'School information updated successfully!')
            return redirect('academics:school_info')
    else:
        form = SchoolInfoForm(instance=school_info)

    context = {
        'title': 'Edit School Information',
        'form': form,
        'school_info': school_info,
    }
    return render(request, 'academics/school_info_edit.html', context)


@login_required
def grades_list(request):
    """List all grades with search and pagination"""
    search_query = request.GET.get('search', '')
    grades = Grade.objects.all().order_by('name')

    if search_query:
        grades = grades.filter(
            Q(name__icontains=search_query) |
            Q(section__icontains=search_query) |
            Q(level__icontains=search_query)
        )

    paginator = Paginator(grades, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'title': 'Grades Management',
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'academics/grades_list.html', context)


@login_required
def grade_create(request):
    """Create a new grade"""
    if request.method == 'POST':
        form = GradeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Grade created successfully!')
            return redirect('academics:grades_list')
    else:
        form = GradeForm()

    context = {
        'title': 'Create Grade',
        'form': form,
    }
    return render(request, 'academics/grade_form.html', context)


@login_required
def grade_edit(request, pk):
    """Edit an existing grade"""
    grade = get_object_or_404(Grade, pk=pk)

    if request.method == 'POST':
        form = GradeForm(request.POST, instance=grade)
        if form.is_valid():
            form.save()
            messages.success(request, 'Grade updated successfully!')
            return redirect('academics:grades_list')
    else:
        form = GradeForm(instance=grade)

    context = {
        'title': f'Edit {grade.name}',
        'form': form,
        'grade': grade,
    }
    return render(request, 'academics/grade_form.html', context)


@login_required
def grade_delete(request, pk):
    """Delete a grade"""
    grade = get_object_or_404(Grade, pk=pk)

    if request.method == 'POST':
        grade_name = grade.name
        grade.delete()
        messages.success(request, f'Grade "{grade_name}" deleted successfully!')
        return redirect('academics:grades_list')

    context = {
        'title': f'Delete {grade.name}',
        'grade': grade,
    }
    return render(request, 'academics/grade_confirm_delete.html', context)


# Department Views
@login_required
def departments_list(request):
    """List all departments"""
    search_query = request.GET.get('search', '')
    departments = Department.objects.select_related('head_teacher').annotate(
        subject_count=Count('subject')
    ).order_by('name')

    if search_query:
        departments = departments.filter(
            Q(name__icontains=search_query) |
            Q(head_teacher__full_name__icontains=search_query)
        )

    paginator = Paginator(departments, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'title': 'Departments Management',
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'academics/departments_list.html', context)


@login_required
def department_create(request):
    """Create a new department"""
    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Department created successfully!')
            return redirect('academics:departments_list')
    else:
        form = DepartmentForm()

    context = {
        'title': 'Create Department',
        'form': form,
    }
    return render(request, 'academics/department_form.html', context)


@login_required
def department_edit(request, pk):
    """Edit an existing department"""
    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            form.save()
            messages.success(request, 'Department updated successfully!')
            return redirect('academics:departments_list')
    else:
        form = DepartmentForm(instance=department)

    context = {
        'title': f'Edit {department.name}',
        'form': form,
        'department': department,
    }
    return render(request, 'academics/department_form.html', context)


@login_required
def department_delete(request, pk):
    """Delete a department"""
    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        department_name = department.name
        department.delete()
        messages.success(request, f'Department "{department_name}" deleted successfully!')
        return redirect('academics:departments_list')

    context = {
        'title': f'Delete {department.name}',
        'department': department,
    }
    return render(request, 'academics/department_confirm_delete.html', context)


# Subject Views
@login_required
def subjects_list(request):
    """List all subjects"""
    search_query = request.GET.get('search', '')
    subjects = Subject.objects.select_related('department').order_by('name')

    if search_query:
        subjects = subjects.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(department__name__icontains=search_query)
        )

    paginator = Paginator(subjects, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'title': 'Subjects Management',
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'academics/subjects_list.html', context)


@login_required
def subject_create(request):
    """Create a new subject"""
    if request.method == 'POST':
        form = SubjectForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Subject created successfully!')
            return redirect('academics:subjects_list')
    else:
        form = SubjectForm()

    context = {
        'title': 'Create Subject',
        'form': form,
    }
    return render(request, 'academics/subject_form.html', context)


@login_required
def subject_edit(request, pk):
    """Edit an existing subject"""
    subject = get_object_or_404(Subject, pk=pk)

    if request.method == 'POST':
        form = SubjectForm(request.POST, instance=subject)
        if form.is_valid():
            form.save()
            messages.success(request, 'Subject updated successfully!')
            return redirect('academics:subjects_list')
    else:
        form = SubjectForm(instance=subject)

    context = {
        'title': f'Edit {subject.name}',
        'form': form,
        'subject': subject,
    }
    return render(request, 'academics/subject_form.html', context)


@login_required
def subject_delete(request, pk):
    """Delete a subject"""
    subject = get_object_or_404(Subject, pk=pk)

    if request.method == 'POST':
        subject_name = subject.name
        subject.delete()
        messages.success(request, f'Subject "{subject_name}" deleted successfully!')
        return redirect('academics:subjects_list')

    context = {
        'title': f'Delete {subject.name}',
        'subject': subject,
    }
    return render(request, 'academics/subject_confirm_delete.html', context)


# Classroom Views
@login_required
def classrooms_list(request):
    """List all classrooms"""
    search_query = request.GET.get('search', '')
    classrooms = ClassRoom.objects.select_related('class_teacher').order_by('name')

    if search_query:
        classrooms = classrooms.filter(
            Q(name__icontains=search_query) |
            Q(building__icontains=search_query) |
            Q(room_number__icontains=search_query)
        )

    paginator = Paginator(classrooms, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'title': 'Classrooms Management',
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'academics/classrooms_list.html', context)


@login_required
def classroom_create(request):
    """Create a new classroom"""
    if request.method == 'POST':
        form = ClassRoomForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Classroom created successfully!')
            return redirect('academics:classrooms_list')
    else:
        form = ClassRoomForm()

    context = {
        'title': 'Create Classroom',
        'form': form,
    }
    return render(request, 'academics/classroom_form.html', context)


@login_required
def classroom_edit(request, pk):
    """Edit an existing classroom"""
    classroom = get_object_or_404(ClassRoom, pk=pk)

    if request.method == 'POST':
        form = ClassRoomForm(request.POST, instance=classroom)
        if form.is_valid():
            form.save()
            messages.success(request, 'Classroom updated successfully!')
            return redirect('academics:classrooms_list')
    else:
        form = ClassRoomForm(instance=classroom)

    context = {
        'title': f'Edit {classroom.name}',
        'form': form,
        'classroom': classroom,
    }
    return render(request, 'academics/classroom_form.html', context)


@login_required
def classroom_delete(request, pk):
    """Delete a classroom"""
    classroom = get_object_or_404(ClassRoom, pk=pk)

    if request.method == 'POST':
        classroom_name = classroom.name
        classroom.delete()
        messages.success(request, f'Classroom "{classroom_name}" deleted successfully!')
        return redirect('academics:classrooms_list')

    context = {
        'title': f'Delete {classroom.name}',
        'classroom': classroom,
    }
    return render(request, 'academics/classroom_confirm_delete.html', context)


# System Settings View
@login_required
def system_settings(request):
    """Manage system settings"""
    if request.method == 'POST':
        form = SystemSettingsForm(request.POST)
        if form.is_valid():
            # Save settings
            for field_name, value in form.cleaned_data.items():
                setting, created = SystemSettings.objects.get_or_create(
                    setting_key=field_name,
                    defaults={'setting_value': str(value), 'description': f'System setting for {field_name}'}
                )
                if not created:
                    setting.setting_value = str(value)
                    setting.save()

            messages.success(request, 'System settings updated successfully!')
            return redirect('academics:system_settings')
    else:
        form = SystemSettingsForm()

    context = {
        'title': 'System Settings',
        'form': form,
    }
    return render(request, 'academics/system_settings.html', context)
