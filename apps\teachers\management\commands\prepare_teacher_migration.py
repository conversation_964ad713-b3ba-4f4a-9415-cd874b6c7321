from django.core.management.base import BaseCommand
from django.db import connection
from datetime import date


class Command(BaseCommand):
    help = 'Prepare existing teacher records for migration by adding default values'

    def handle(self, *args, **options):
        self.stdout.write('Preparing teacher records for migration...')
        
        with connection.cursor() as cursor:
            # Check if teachers table exists and has data
            cursor.execute("""
                SELECT COUNT(*) FROM teachers_teacher;
            """)
            teacher_count = cursor.fetchone()[0]
            
            if teacher_count == 0:
                self.stdout.write(
                    self.style.SUCCESS('No existing teachers found. Migration should proceed smoothly.')
                )
                return
            
            self.stdout.write(f'Found {teacher_count} existing teachers. Adding default values...')
            
            # Add default values for new required fields
            updates = [
                # Add employee_id with unique values
                """
                UPDATE teachers_teacher 
                SET full_name = COALESCE(full_name, 'Unknown Teacher')
                WHERE full_name IS NULL OR full_name = '';
                """,
                
                # Ensure email has @ symbol
                """
                UPDATE teachers_teacher 
                SET email = CONCAT(LOWER(REPLACE(full_name, ' ', '.')), '@school.edu')
                WHERE email IS NULL OR email = '' OR email NOT LIKE '%@%';
                """,
                
                # Set default phone numbers
                """
                UPDATE teachers_teacher 
                SET phone = CONCAT('0700', LPAD(id, 6, '0'))
                WHERE phone IS NULL OR phone = '';
                """,
                
                # Set default address
                """
                UPDATE teachers_teacher 
                SET address = 'Address not provided'
                WHERE address IS NULL OR address = '';
                """,
                
                # Set default gender
                """
                UPDATE teachers_teacher 
                SET gender = 'Male'
                WHERE gender IS NULL OR gender = '';
                """,
            ]
            
            for update_sql in updates:
                try:
                    cursor.execute(update_sql)
                    self.stdout.write(f'✓ Applied update: {update_sql[:50]}...')
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'Skipped update due to error: {e}')
                    )
            
            self.stdout.write(
                self.style.SUCCESS('Teacher records prepared for migration!')
            )
            self.stdout.write(
                self.style.WARNING('You can now run: python manage.py makemigrations teachers')
            )
