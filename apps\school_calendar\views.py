from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from datetime import date, timedelta, datetime
import json
import calendar

from .models import (
    AcademicYearConfiguration, TermConfiguration, HolidaySchedule,
    ExamPeriodSchedule, SchoolCalendar, CalendarNotification,
    CalendarSyncConfiguration, UserCalendarPreferences, CalendarExportImport
)
from .forms import (
    AcademicYearConfigurationForm, TermConfigurationForm, HolidayScheduleForm,
    ExamPeriodScheduleForm, SchoolCalendarForm, UserCalendarPreferencesForm,
    CalendarExportForm, CalendarSearchForm, CalendarSyncConfigurationForm
)
from apps.academics.models import Subject, Grade

@login_required
def calendar_dashboard(request):
    """School calendar dashboard with overview and statistics."""
    today = date.today()
    current_year = AcademicYearConfiguration.objects.filter(is_current=True).first()
    current_term = TermConfiguration.objects.filter(is_current=True).first()

    # Get upcoming events (next 30 days)
    upcoming_events = SchoolCalendar.objects.filter(
        start_date__gte=today,
        start_date__lte=today + timedelta(days=30)
    ).order_by('start_date')[:10]

    # Get recent holidays
    recent_holidays = HolidaySchedule.objects.filter(
        start_date__gte=today - timedelta(days=30),
        is_active=True
    ).order_by('start_date')[:5]

    # Get upcoming exams
    upcoming_exams = ExamPeriodSchedule.objects.filter(
        start_date__gte=today,
        status__in=['scheduled', 'ongoing']
    ).order_by('start_date')[:5]

    context = {
        'current_year': current_year,
        'current_term': current_term,
        'total_events': SchoolCalendar.objects.count(),
        'total_holidays': HolidaySchedule.objects.filter(is_active=True).count(),
        'total_exams': ExamPeriodSchedule.objects.count(),
        'upcoming_events': upcoming_events,
        'recent_holidays': recent_holidays,
        'upcoming_exams': upcoming_exams,
        'today': today,
        'events_this_month': SchoolCalendar.objects.filter(
            start_date__year=today.year,
            start_date__month=today.month
        ).count(),
        'holidays_this_month': HolidaySchedule.objects.filter(
            start_date__year=today.year,
            start_date__month=today.month,
            is_active=True
        ).count(),
    }
    return render(request, 'school_calendar/dashboard.html', context)

@login_required
def calendar_view(request):
    """Main calendar view with different view types."""
    view_type = request.GET.get('view', 'month')
    year = int(request.GET.get('year', date.today().year))
    month = int(request.GET.get('month', date.today().month))

    # Get user preferences
    try:
        preferences = request.user.calendar_preferences
    except UserCalendarPreferences.DoesNotExist:
        preferences = UserCalendarPreferences.objects.create(user=request.user)

    # Get events for the specified period
    if view_type == 'month':
        start_date = date(year, month, 1)
        # Get last day of month
        last_day = calendar.monthrange(year, month)[1]
        end_date = date(year, month, last_day)
    elif view_type == 'week':
        # Calculate week start and end
        today = date.today()
        start_date = today - timedelta(days=today.weekday())
        end_date = start_date + timedelta(days=6)
    else:  # day view
        start_date = end_date = date.today()

    # Filter events based on user preferences and role
    events = SchoolCalendar.objects.filter(
        start_date__lte=end_date,
        end_date__gte=start_date
    )

    # Apply user preference filters
    if not preferences.show_holidays:
        events = events.exclude(event_type='holiday')
    if not preferences.show_exams:
        events = events.exclude(event_type='exam')
    if not preferences.show_events:
        events = events.exclude(event_type='event')

    # Get holidays and exams separately for display
    holidays = HolidaySchedule.objects.filter(
        start_date__lte=end_date,
        end_date__gte=start_date,
        is_active=True
    ) if preferences.show_holidays else HolidaySchedule.objects.none()

    exams = ExamPeriodSchedule.objects.filter(
        start_date__lte=end_date,
        end_date__gte=start_date
    ) if preferences.show_exams else ExamPeriodSchedule.objects.none()

    context = {
        'view_type': view_type,
        'year': year,
        'month': month,
        'start_date': start_date,
        'end_date': end_date,
        'events': events,
        'holidays': holidays,
        'exams': exams,
        'preferences': preferences,
        'calendar_data': _get_calendar_data(year, month, events, holidays, exams),
    }
    return render(request, 'school_calendar/calendar_view.html', context)

def _get_calendar_data(year, month, events, holidays, exams):
    """Helper function to prepare calendar data for frontend."""
    cal = calendar.monthcalendar(year, month)
    calendar_data = []

    for week in cal:
        week_data = []
        for day in week:
            if day == 0:
                week_data.append({'day': 0, 'events': [], 'holidays': [], 'exams': []})
            else:
                day_date = date(year, month, day)
                day_events = events.filter(start_date__lte=day_date, end_date__gte=day_date)
                day_holidays = holidays.filter(start_date__lte=day_date, end_date__gte=day_date)
                day_exams = exams.filter(start_date__lte=day_date, end_date__gte=day_date)

                week_data.append({
                    'day': day,
                    'date': day_date,
                    'events': list(day_events),
                    'holidays': list(day_holidays),
                    'exams': list(day_exams),
                    'is_today': day_date == date.today(),
                })
        calendar_data.append(week_data)

    return calendar_data
