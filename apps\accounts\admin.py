from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import User, Admin, LoginAttempt, ParentChildRelationship

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'email', 'type', 'status', 'failed_login_attempts',
        'account_status', 'last_login', 'created_at'
    )
    search_fields = ('email', 'type', 'index_number')
    list_filter = ('type', 'status', 'failed_login_attempts', 'account_locked_until')
    ordering = ('-created_at',)
    readonly_fields = ('last_login', 'created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('email', 'type', 'index_number', 'status')
        }),
        ('Security', {
            'fields': (
                'failed_login_attempts', 'last_failed_login',
                'account_locked_until', 'lockout_reason'
            )
        }),
        ('Password Reset', {
            'fields': ('reset_token', 'reset_token_expiry')
        }),
        ('Permissions', {
            'fields': ('is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('Timestamps', {
            'fields': ('last_login', 'created_at', 'updated_at')
        })
    )

    actions = ['unlock_selected_accounts', 'reset_failed_attempts']

    def account_status(self, obj):
        if obj.is_account_locked():
            return format_html(
                '<span style="color: red;">🔒 Locked until {}</span>',
                obj.account_locked_until.strftime('%Y-%m-%d %H:%M')
            )
        elif obj.status == 'active':
            return format_html('<span style="color: green;">✅ Active</span>')
        else:
            return format_html('<span style="color: orange;">⚠️ {}</span>', obj.status.title())
    account_status.short_description = 'Account Status'

    def unlock_selected_accounts(self, request, queryset):
        count = 0
        for user in queryset:
            if user.is_account_locked():
                user.unlock_account()
                count += 1
        self.message_user(request, f'Successfully unlocked {count} accounts.')
    unlock_selected_accounts.short_description = 'Unlock selected accounts'

    def reset_failed_attempts(self, request, queryset):
        count = 0
        for user in queryset:
            if user.failed_login_attempts > 0:
                user.reset_failed_login()
                count += 1
        self.message_user(request, f'Reset failed login attempts for {count} accounts.')
    reset_failed_attempts.short_description = 'Reset failed login attempts'

@admin.register(Admin)
class AdminProfileAdmin(admin.ModelAdmin):
    list_display = ('id', 'full_name', 'index_number', 'gender', 'email', 'phone', 'role', 'reg_date')
    search_fields = ('full_name', 'index_number', 'email', 'phone')
    list_filter = ('gender', 'role')
    ordering = ('-reg_date',)


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    list_display = (
        'email', 'attempt_type', 'ip_address', 'timestamp',
        'user_link', 'failure_reason'
    )
    search_fields = ('email', 'ip_address', 'user_agent', 'failure_reason')
    list_filter = ('attempt_type', 'timestamp')
    ordering = ('-timestamp',)
    readonly_fields = ('timestamp',)

    def user_link(self, obj):
        if obj.user:
            url = reverse('admin:accounts_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.email)
        return 'N/A'
    user_link.short_description = 'User'

    def has_add_permission(self, request):
        return False  # Don't allow manual creation

    def has_change_permission(self, request, obj=None):
        return False  # Don't allow editing


@admin.register(ParentChildRelationship)
class ParentChildRelationshipAdmin(admin.ModelAdmin):
    list_display = (
        'parent_email', 'student_name', 'relationship_type',
        'is_primary_contact', 'is_emergency_contact', 'can_pick_up'
    )
    search_fields = (
        'parent__email', 'student__full_name', 'student__student_id',
        'relationship_type'
    )
    list_filter = (
        'relationship_type', 'is_primary_contact',
        'is_emergency_contact', 'can_pick_up'
    )
    ordering = ('student__full_name', '-is_primary_contact')

    fieldsets = (
        ('Relationship', {
            'fields': ('parent', 'student', 'relationship_type')
        }),
        ('Contact Preferences', {
            'fields': (
                'is_primary_contact', 'is_emergency_contact', 'can_pick_up'
            )
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    readonly_fields = ('created_at', 'updated_at')

    def parent_email(self, obj):
        return obj.parent.email
    parent_email.short_description = 'Parent Email'

    def student_name(self, obj):
        return obj.student.full_name
    student_name.short_description = 'Student Name'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('parent', 'student')
