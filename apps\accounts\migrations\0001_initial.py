# Generated by Django X.Y on YYYY-MM-DD HH:MM
from django.db import migrations, models
import django.db.models.deletion

class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('email', models.EmailField(unique=True, max_length=254)),
                ('password', models.CharField(max_length=255)),
                ('type', models.CharField(max_length=255, choices=[('admin', 'Admin'), ('teacher', 'Teacher'), ('student', 'Student'), ('parent', 'Parent'), ('accountant', 'Accountant')])),
                ('index_number', models.BigIntegerField(null=True, blank=True)),
                ('reset_token', models.Char<PERSON>ield(max_length=255, null=True, blank=True)),
                ('reset_token_expiry', models.DateTimeField(null=True, blank=True)),
                ('last_login', models.DateTimeField(null=True, blank=True)),
                ('status', models.CharField(max_length=50, default='active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Admin',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('index_number', models.BigIntegerField(unique=True)),
                ('full_name', models.CharField(max_length=255)),
                ('i_name', models.CharField(max_length=255)),
                ('gender', models.CharField(max_length=255, choices=[('Male', 'Male'), ('Female', 'Female')])),
                ('address', models.CharField(max_length=255)),
                ('phone', models.CharField(max_length=255)),
                ('email', models.EmailField(max_length=254)),
                ('image_name', models.CharField(max_length=255)),
                ('national_id', models.CharField(max_length=20, null=True, blank=True)),
                ('role', models.CharField(max_length=100, default='admin')),
                ('reg_date', models.DateField()),
            ],
        ),
    ] 