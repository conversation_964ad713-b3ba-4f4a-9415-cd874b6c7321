from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from .models import (
    Teacher, TeacherQualification, TeacherDocument, TeacherSubjectSpecialization,
    TeacherAssignment, TeacherPerformanceEvaluation, TeacherEmploymentHistory,
    TeacherDepartmentTransfer, TeacherWorkload
)
from apps.academics.models import Grade, Subject, Department

User = get_user_model()

class TeacherRegistrationForm(forms.ModelForm):
    """
    Comprehensive teacher registration form
    """
    confirm_email = forms.EmailField(label="Confirm Email")
    
    class Meta:
        model = Teacher
        fields = [
            # Personal Details
            'first_name', 'middle_name', 'last_name', 'preferred_name', 'gender', 
            'date_of_birth', 'place_of_birth', 'marital_status',
            # Identification
            'national_id', 'passport_number', 'kra_pin', 'nhif_number', 'nssf_number',
            # Contact Information
            'phone', 'alternative_phone', 'email', 'alternative_email',
            # Address Information
            'address', 'city', 'county', 'postal_address', 'postal_code',
            # Emergency Contact
            'emergency_contact_name', 'emergency_contact_relationship', 
            'emergency_contact_phone', 'emergency_contact_address',
            # Employment Information
            'employment_type', 'employment_status', 'employment_date', 
            'tsc_number', 'teacher_registration_number', 'professional_qualification',
            'teaching_experience_years', 'previous_schools',
            # Current Assignment
            'department', 'role', 'is_department_head', 'is_class_teacher',
            # Salary Information
            'basic_salary', 'house_allowance', 'transport_allowance', 'other_allowances',
            # Photo
            'photo'
        ]
        
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'First Name'}),
            'middle_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Middle Name (Optional)'}),
            'last_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Last Name'}),
            'preferred_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Preferred Name (Optional)'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'place_of_birth': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Place of Birth'}),
            'marital_status': forms.Select(attrs={'class': 'form-select'}),
            'national_id': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'National ID Number'}),
            'passport_number': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Passport Number (Optional)'}),
            'kra_pin': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'KRA PIN'}),
            'nhif_number': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'NHIF Number'}),
            'nssf_number': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'NSSF Number'}),
            'phone': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Primary Phone Number'}),
            'alternative_phone': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Alternative Phone (Optional)'}),
            'email': forms.EmailInput(attrs={'class': 'form-input', 'placeholder': 'Primary Email Address'}),
            'alternative_email': forms.EmailInput(attrs={'class': 'form-input', 'placeholder': 'Alternative Email (Optional)'}),
            'address': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Physical Address'}),
            'city': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'City'}),
            'county': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'County'}),
            'postal_address': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Postal Address'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Postal Code'}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Emergency Contact Name'}),
            'emergency_contact_relationship': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Relationship'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Emergency Contact Phone'}),
            'emergency_contact_address': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2, 'placeholder': 'Emergency Contact Address'}),
            'employment_type': forms.Select(attrs={'class': 'form-select'}),
            'employment_status': forms.Select(attrs={'class': 'form-select'}),
            'employment_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'tsc_number': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'TSC Registration Number'}),
            'teacher_registration_number': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Teacher Registration Number'}),
            'professional_qualification': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Professional Qualification'}),
            'teaching_experience_years': forms.NumberInput(attrs={'class': 'form-input', 'min': '0'}),
            'previous_schools': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3, 'placeholder': 'Previous Teaching Experience'}),
            'department': forms.Select(attrs={'class': 'form-select'}),
            'role': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Job Title/Role'}),
            'is_department_head': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'is_class_teacher': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'basic_salary': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'house_allowance': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'transport_allowance': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'other_allowances': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.01'}),
            'photo': forms.FileInput(attrs={'class': 'form-file', 'accept': 'image/*'}),
        }
    
    def clean_confirm_email(self):
        email = self.cleaned_data.get('email')
        confirm_email = self.cleaned_data.get('confirm_email')
        if email and confirm_email and email != confirm_email:
            raise ValidationError("Email addresses do not match.")
        return confirm_email
    
    def clean_national_id(self):
        national_id = self.cleaned_data.get('national_id')
        if national_id:
            # Check if national ID already exists
            if Teacher.objects.filter(national_id=national_id).exists():
                raise ValidationError("A teacher with this National ID already exists.")
        return national_id
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # Check if email already exists
            if Teacher.objects.filter(email=email).exists():
                raise ValidationError("A teacher with this email already exists.")
        return email
    
    def clean_date_of_birth(self):
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth:
            # Check if teacher is at least 18 years old
            today = date.today()
            age = today.year - date_of_birth.year - ((today.month, today.day) < (date_of_birth.month, date_of_birth.day))
            if age < 18:
                raise ValidationError("Teacher must be at least 18 years old.")
            if age > 70:
                raise ValidationError("Please verify the date of birth.")
        return date_of_birth


class TeacherProfileForm(forms.ModelForm):
    """
    Teacher profile editing form
    """
    class Meta:
        model = Teacher
        fields = [
            'first_name', 'middle_name', 'last_name', 'preferred_name', 'gender',
            'phone', 'alternative_phone', 'email', 'alternative_email',
            'address', 'city', 'county', 'postal_address', 'postal_code',
            'emergency_contact_name', 'emergency_contact_relationship', 
            'emergency_contact_phone', 'emergency_contact_address',
            'photo', 'marital_status'
        ]
        
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-input'}),
            'middle_name': forms.TextInput(attrs={'class': 'form-input'}),
            'last_name': forms.TextInput(attrs={'class': 'form-input'}),
            'preferred_name': forms.TextInput(attrs={'class': 'form-input'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'phone': forms.TextInput(attrs={'class': 'form-input'}),
            'alternative_phone': forms.TextInput(attrs={'class': 'form-input'}),
            'email': forms.EmailInput(attrs={'class': 'form-input'}),
            'alternative_email': forms.EmailInput(attrs={'class': 'form-input'}),
            'address': forms.TextInput(attrs={'class': 'form-input'}),
            'city': forms.TextInput(attrs={'class': 'form-input'}),
            'county': forms.TextInput(attrs={'class': 'form-input'}),
            'postal_address': forms.TextInput(attrs={'class': 'form-input'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_relationship': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_address': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2}),
            'photo': forms.FileInput(attrs={'class': 'form-file', 'accept': 'image/*'}),
            'marital_status': forms.Select(attrs={'class': 'form-select'}),
        }


class TeacherQualificationForm(forms.ModelForm):
    """
    Teacher qualification form
    """
    class Meta:
        model = TeacherQualification
        fields = [
            'qualification_type', 'qualification_name', 'institution', 'field_of_study',
            'grade_obtained', 'year_obtained', 'certificate_number', 'expiry_date',
            'is_teaching_qualification'
        ]
        
        widgets = {
            'qualification_type': forms.Select(attrs={'class': 'form-select'}),
            'qualification_name': forms.TextInput(attrs={'class': 'form-input'}),
            'institution': forms.TextInput(attrs={'class': 'form-input'}),
            'field_of_study': forms.TextInput(attrs={'class': 'form-input'}),
            'grade_obtained': forms.TextInput(attrs={'class': 'form-input'}),
            'year_obtained': forms.NumberInput(attrs={'class': 'form-input'}),
            'certificate_number': forms.TextInput(attrs={'class': 'form-input'}),
            'expiry_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'is_teaching_qualification': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }


class TeacherDocumentForm(forms.ModelForm):
    """
    Teacher document upload form
    """
    class Meta:
        model = TeacherDocument
        fields = ['document_type', 'document_name', 'document_file', 'description']
        
        widgets = {
            'document_type': forms.Select(attrs={'class': 'form-select'}),
            'document_name': forms.TextInput(attrs={'class': 'form-input'}),
            'document_file': forms.FileInput(attrs={'class': 'form-file'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
        }


class TeacherSubjectSpecializationForm(forms.ModelForm):
    """
    Teacher subject specialization form
    """
    class Meta:
        model = TeacherSubjectSpecialization
        fields = [
            'subject', 'expertise_level', 'years_of_experience', 'certification_status',
            'certification_date', 'certification_body', 'certification_number',
            'can_teach_grade_levels', 'notes'
        ]
        
        widgets = {
            'subject': forms.Select(attrs={'class': 'form-select'}),
            'expertise_level': forms.Select(attrs={'class': 'form-select'}),
            'years_of_experience': forms.NumberInput(attrs={'class': 'form-input', 'min': '0'}),
            'certification_status': forms.Select(attrs={'class': 'form-select'}),
            'certification_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'certification_body': forms.TextInput(attrs={'class': 'form-input'}),
            'certification_number': forms.TextInput(attrs={'class': 'form-input'}),
            'can_teach_grade_levels': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'e.g., Form 1, Form 2, Form 3'}),
            'notes': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
        }


class TeacherAssignmentForm(forms.ModelForm):
    """
    Teacher assignment form
    """
    class Meta:
        model = TeacherAssignment
        fields = [
            'teacher', 'subject', 'grade', 'section', 'assignment_type',
            'academic_year', 'term', 'lessons_per_week', 'start_date', 'end_date', 'notes'
        ]
        
        widgets = {
            'teacher': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200'}),
            'subject': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200'}),
            'grade': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200'}),
            'section': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'placeholder': 'Enter section'}),
            'assignment_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200'}),
            'academic_year': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'placeholder': 'Enter academic year'}),
            'term': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'min': '1', 'max': '3', 'placeholder': 'Enter term (1-3)'}),
            'lessons_per_week': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'min': '1', 'placeholder': 'Enter number of lessons per week'}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200', 'rows': 3, 'placeholder': 'Enter any additional notes'}),
        }


class TeacherSearchForm(forms.Form):
    """
    Teacher search and filter form
    """
    search_query = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search by name, employee ID, email, or phone...'
        })
    )
    
    department = forms.ModelChoiceField(
        queryset=Department.objects.all(),
        required=False,
        empty_label="All Departments",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    employment_type = forms.ChoiceField(
        choices=[('', 'All Employment Types')] + Teacher.EMPLOYMENT_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + Teacher.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    gender = forms.ChoiceField(
        choices=[('', 'All Genders')] + Teacher.GENDER_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class BulkTeacherAssignmentForm(forms.Form):
    """
    Bulk teacher assignment form
    """
    teachers = forms.ModelMultipleChoiceField(
        queryset=Teacher.objects.filter(status='active'),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-checkbox'})
    )
    
    subject = forms.ModelChoiceField(
        queryset=Subject.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    assignment_type = forms.ChoiceField(
        choices=TeacherAssignment.ASSIGNMENT_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    academic_year = forms.IntegerField(
        initial=2024,
        widget=forms.NumberInput(attrs={'class': 'form-input'})
    )
    
    term = forms.IntegerField(
        initial=1,
        widget=forms.NumberInput(attrs={'class': 'form-input', 'min': '1', 'max': '3'})
    )
    
    lessons_per_week = forms.IntegerField(
        initial=1,
        widget=forms.NumberInput(attrs={'class': 'form-input', 'min': '1'})
    )
    
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-input', 'type': 'date'})
    )
