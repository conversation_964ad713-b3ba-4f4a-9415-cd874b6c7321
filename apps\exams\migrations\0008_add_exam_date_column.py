# Generated manually to recreate ExamTimetable with correct structure

from django.db import migrations
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0007_add_missing_tables'),
        ('academics', '0002_initial'),
        ('teachers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # Backup existing data
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_examtimetable_backup AS
            SELECT * FROM exams_examtimetable;
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_examtimetable_backup;"
        ),

        # Drop the old table
        migrations.RunSQL(
            """
            DROP TABLE IF EXISTS exams_examtimetable;
            """,
            reverse_sql="-- Cannot reverse dropping table"
        ),

        # Create the new table with correct structure
        migrations.RunSQL(
            """
            CREATE TABLE exams_examtimetable (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exam_date DATE NOT NULL,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                duration_minutes INTEGER NOT NULL,
                session_type VARCHAR(20) DEFAULT 'morning',
                total_marks INTEGER DEFAULT 100,
                instructions TEXT,
                special_requirements TEXT,
                materials_allowed TEXT,
                materials_provided TEXT,
                status VARCHAR(20) DEFAULT 'draft',
                is_published BOOLEAN DEFAULT 0,
                published_at DATETIME,
                expected_candidates INTEGER DEFAULT 0,
                actual_candidates INTEGER DEFAULT 0,
                absentees INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                exam_id INTEGER NOT NULL,
                grade_id INTEGER NOT NULL,
                subject_id INTEGER NOT NULL,
                classroom_id INTEGER NOT NULL,
                chief_invigilator_id INTEGER,
                created_by_id INTEGER,
                FOREIGN KEY (exam_id) REFERENCES exams_exam(id),
                FOREIGN KEY (grade_id) REFERENCES academics_grade(id),
                FOREIGN KEY (subject_id) REFERENCES academics_subject(id),
                FOREIGN KEY (classroom_id) REFERENCES academics_classroom(id),
                FOREIGN KEY (chief_invigilator_id) REFERENCES teachers_teacher(id),
                FOREIGN KEY (created_by_id) REFERENCES auth_user(id),
                UNIQUE(exam_id, grade_id, subject_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_examtimetable;"
        ),

        # Create many-to-many tables
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_examtimetable_additional_venues (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                examtimetable_id INTEGER NOT NULL,
                classroom_id INTEGER NOT NULL,
                FOREIGN KEY (examtimetable_id) REFERENCES exams_examtimetable(id),
                FOREIGN KEY (classroom_id) REFERENCES academics_classroom(id),
                UNIQUE(examtimetable_id, classroom_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_examtimetable_additional_venues;"
        ),

        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_examtimetable_assistant_invigilators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                examtimetable_id INTEGER NOT NULL,
                teacher_id INTEGER NOT NULL,
                FOREIGN KEY (examtimetable_id) REFERENCES exams_examtimetable(id),
                FOREIGN KEY (teacher_id) REFERENCES teachers_teacher(id),
                UNIQUE(examtimetable_id, teacher_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_examtimetable_assistant_invigilators;"
        ),
    ]
