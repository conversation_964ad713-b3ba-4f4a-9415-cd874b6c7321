from django.urls import path
from . import views

app_name = 'salary'

urlpatterns = [
    # Dashboard
    path('', views.salary_dashboard, name='dashboard'),
    
    # Employee Salary Management
    path('employees/', views.employee_salary_list, name='employee_list'),
    path('employees/<int:pk>/', views.employee_salary_detail, name='employee_detail'),
    path('employees/<int:pk>/edit/', views.edit_employee_salary, name='edit_employee_salary'),
    path('employees/add/', views.add_employee_salary, name='add_employee_salary'),
    
    # Salary Grades
    path('grades/', views.salary_grade_list, name='grade_list'),
    path('grades/add/', views.add_salary_grade, name='add_grade'),
    path('grades/<int:pk>/edit/', views.edit_salary_grade, name='edit_grade'),
    path('grades/<int:pk>/delete/', views.delete_salary_grade, name='delete_grade'),
    
    # Salary Components
    path('components/', views.salary_component_list, name='component_list'),
    path('components/add/', views.add_salary_component, name='add_component'),
    path('components/<int:pk>/edit/', views.edit_salary_component, name='edit_component'),
    path('components/<int:pk>/delete/', views.delete_salary_component, name='delete_component'),
    
    # Payroll Management
    path('payroll/', views.payroll_list, name='payroll_list'),
    path('payroll/periods/', views.payroll_period_list, name='payroll_period_list'),
    path('payroll/periods/add/', views.add_payroll_period, name='add_payroll_period'),
    path('payroll/periods/<int:pk>/', views.payroll_period_detail, name='payroll_period_detail'),
    path('payroll/periods/<int:pk>/process/', views.process_payroll, name='process_payroll'),
    path('payroll/periods/<int:pk>/approve/', views.approve_payroll, name='approve_payroll'),
    
    # Payslips
    path('payslips/', views.payslip_list, name='payslip_list'),
    path('payslips/<int:pk>/', views.payslip_detail, name='payslip_detail'),
    path('payslips/<int:pk>/download/', views.download_payslip, name='download_payslip'),
    path('payslips/generate-bulk/', views.generate_bulk_payslips, name='generate_bulk_payslips'),
    path('payslips/generate/<int:payroll_period_id>/', views.generate_payslips, name='generate_payslips'),
    
    # Reports
    path('reports/', views.salary_reports, name='reports'),
    path('reports/payroll-summary/', views.payroll_summary_report, name='payroll_summary'),
    path('reports/employee-salary/', views.employee_salary_report, name='employee_salary_report'),
    path('reports/monthly-payroll/', views.monthly_payroll_report, name='monthly_payroll_report'),
    path('reports/annual-payroll/', views.annual_payroll_report, name='annual_payroll_report'),
    path('reports/department-salary/', views.department_salary_report, name='department_salary_report'),
    path('reports/grade-salary/', views.grade_salary_report, name='grade_salary_report'),
    path('reports/custom/', views.custom_report, name='custom_report'),
    
    # API endpoints
    path('api/calculate-salary/', views.calculate_salary_api, name='calculate_salary_api'),
    path('api/employee-components/<int:employee_id>/', views.employee_components_api, name='employee_components_api'),
]
