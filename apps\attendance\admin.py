from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    StudentAttendance, TeacherAttendance, AttendancePeriod,
    AttendanceStatistics, AttendanceAlert, BulkAttendanceSession, AttendanceReport
)

@admin.register(AttendancePeriod)
class AttendancePeriodAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)
    ordering = ('-start_date',)

@admin.register(StudentAttendance)
class StudentAttendanceAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'date', 'status', 'reason', 'notified', 'recorded_by', 'period')
    search_fields = ('student__first_name', 'student__last_name', 'student__id', 'date', 'status', 'reason')
    list_filter = ('status', 'date', 'notified', 'period')
    ordering = ('-date',)

@admin.register(TeacherAttendance)
class TeacherAttendanceAdmin(admin.ModelAdmin):
    list_display = ('id', 'teacher', 'date', 'status', 'reason', 'notified', 'recorded_by', 'period')
    search_fields = ('teacher__first_name', 'teacher__last_name', 'teacher__id', 'date', 'status', 'reason')
    list_filter = ('status', 'date', 'notified', 'period')
    ordering = ('-date',)


@admin.register(AttendanceStatistics)
class AttendanceStatisticsAdmin(admin.ModelAdmin):
    list_display = (
        'entity_type', 'entity_id', 'period_type', 'period_start', 'period_end',
        'attendance_rate', 'total_days', 'present_days', 'absent_days'
    )
    list_filter = ('entity_type', 'period_type', 'academic_year', 'term')
    search_fields = ('entity_id',)
    readonly_fields = ('calculated_at',)
    ordering = ('-period_start', 'entity_type')


@admin.register(AttendanceAlert)
class AttendanceAlertAdmin(admin.ModelAdmin):
    list_display = (
        'get_entity_name', 'alert_type', 'severity', 'status',
        'current_value', 'threshold_value', 'triggered_date'
    )
    list_filter = ('alert_type', 'severity', 'status', 'triggered_date')
    search_fields = ('student__full_name', 'teacher__full_name', 'title')
    readonly_fields = ('triggered_date',)
    ordering = ('-triggered_date',)

    def get_entity_name(self, obj):
        if obj.student:
            return f"Student: {obj.student.full_name}"
        elif obj.teacher:
            return f"Teacher: {obj.teacher.full_name}"
        return "Unknown"
    get_entity_name.short_description = "Entity"


@admin.register(BulkAttendanceSession)
class BulkAttendanceSessionAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'session_type', 'status', 'date', 'grade',
        'total_records', 'successful_records', 'failed_records', 'created_by'
    )
    list_filter = ('session_type', 'status', 'date', 'grade')
    search_fields = ('created_by__username', 'grade__name')
    readonly_fields = ('created_at', 'completed_at')
    ordering = ('-created_at',)


@admin.register(AttendanceReport)
class AttendanceReportAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'report_type', 'format', 'status',
        'start_date', 'end_date', 'generated_by', 'generated_at'
    )
    list_filter = ('report_type', 'format', 'status', 'generated_at')
    search_fields = ('title', 'generated_by__username')
    readonly_fields = ('generated_at', 'accessed_count', 'last_accessed')
    ordering = ('-generated_at',)
