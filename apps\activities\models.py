from django.db import models
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from django.utils import timezone

# Create your models here.

class CoCurricularActivity(models.Model):
    ACTIVITY_CATEGORIES = [
        ('sports', 'Sports'),
        ('music', 'Music'),
        ('drama', 'Drama'),
        ('clubs', 'Clubs'),
    ]
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    category = models.CharField(max_length=50, choices=ACTIVITY_CATEGORIES)
    description = models.TextField(null=True, blank=True)
    teacher_in_charge = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, blank=True)
    meeting_day = models.CharField(max_length=20, null=True, blank=True)
    meeting_time = models.TimeField(null=True, blank=True)
    venue = models.CharField(max_length=100, null=True, blank=True)
    fee = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    status = models.CharField(max_length=50, default='active')

    def __str__(self):
        return self.name

class StudentActivity(models.Model):
    ACTIVITY_STATUSES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    activity = models.ForeignKey(CoCurricularActivity, on_delete=models.CASCADE)
    join_date = models.DateField()
    position = models.CharField(max_length=50, null=True, blank=True)
    achievements = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, default='active', choices=ACTIVITY_STATUSES)

    def __str__(self):
        return f"Student Activity - {self.id}"

# Enhanced Models for Complete Activities Management

class ActivityCategory(models.Model):
    """Enhanced activity categories with detailed management"""
    CATEGORY_TYPES = [
        ('sports', 'Sports'),
        ('music', 'Music'),
        ('drama', 'Drama & Theatre'),
        ('clubs', 'Clubs & Societies'),
        ('academic', 'Academic Competitions'),
        ('community', 'Community Service'),
        ('arts', 'Arts & Crafts'),
        ('technology', 'Technology & Innovation'),
        ('debate', 'Debate & Public Speaking'),
        ('cultural', 'Cultural Activities'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="CSS icon class")
    color = models.CharField(max_length=7, default='#3B82F6', help_text="Hex color code")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Activity Categories"

    def __str__(self):
        return self.name

class ActivitySchedule(models.Model):
    """Detailed schedule management for activities"""
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('biweekly', 'Bi-weekly'),
        ('monthly', 'Monthly'),
        ('irregular', 'Irregular'),
    ]

    DAYS_OF_WEEK = [
        ('monday', 'Monday'),
        ('tuesday', 'Tuesday'),
        ('wednesday', 'Wednesday'),
        ('thursday', 'Thursday'),
        ('friday', 'Friday'),
        ('saturday', 'Saturday'),
        ('sunday', 'Sunday'),
    ]

    id = models.AutoField(primary_key=True)
    activity = models.ForeignKey(CoCurricularActivity, on_delete=models.CASCADE, related_name='schedules')
    day_of_week = models.CharField(max_length=10, choices=DAYS_OF_WEEK)
    start_time = models.TimeField()
    end_time = models.TimeField()
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, default='weekly')
    venue = models.CharField(max_length=200, blank=True)
    max_participants = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['activity', 'day_of_week', 'start_time']

    def clean(self):
        if self.start_time and self.end_time and self.end_time <= self.start_time:
            raise ValidationError("End time must be after start time.")

    def __str__(self):
        return f"{self.activity.name} - {self.get_day_of_week_display()} {self.start_time}"

class Achievement(models.Model):
    """Track student achievements in activities"""
    ACHIEVEMENT_TYPES = [
        ('participation', 'Participation'),
        ('competition', 'Competition'),
        ('leadership', 'Leadership'),
        ('skill', 'Skill Development'),
        ('service', 'Community Service'),
        ('award', 'Award/Recognition'),
    ]

    ACHIEVEMENT_LEVELS = [
        ('school', 'School Level'),
        ('district', 'District Level'),
        ('regional', 'Regional Level'),
        ('national', 'National Level'),
        ('international', 'International Level'),
    ]

    id = models.AutoField(primary_key=True)
    student_activity = models.ForeignKey(StudentActivity, on_delete=models.CASCADE, related_name='achievement_records')
    title = models.CharField(max_length=200)
    achievement_type = models.CharField(max_length=20, choices=ACHIEVEMENT_TYPES)
    level = models.CharField(max_length=20, choices=ACHIEVEMENT_LEVELS)
    description = models.TextField()
    date_achieved = models.DateField()
    position = models.CharField(max_length=50, blank=True, help_text="e.g., 1st Place, Gold Medal")
    certificate_number = models.CharField(max_length=100, blank=True)
    awarded_by = models.CharField(max_length=200, blank=True)
    points_earned = models.IntegerField(default=0, help_text="Points for school grading system")
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.student_activity.student.full_name} - {self.title}"

class ActivityCoach(models.Model):
    """Enhanced coach/teacher assignment for activities"""
    COACH_TYPES = [
        ('head_coach', 'Head Coach'),
        ('assistant_coach', 'Assistant Coach'),
        ('teacher_in_charge', 'Teacher in Charge'),
        ('external_coach', 'External Coach'),
        ('volunteer', 'Volunteer'),
    ]

    id = models.AutoField(primary_key=True)
    activity = models.ForeignKey(CoCurricularActivity, on_delete=models.CASCADE, related_name='coaches')
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE, null=True, blank=True)
    external_name = models.CharField(max_length=200, blank=True, help_text="For external coaches")
    external_contact = models.CharField(max_length=20, blank=True)
    external_email = models.EmailField(blank=True)
    coach_type = models.CharField(max_length=20, choices=COACH_TYPES)
    specialization = models.CharField(max_length=200, blank=True)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    qualifications = models.TextField(blank=True)
    experience_years = models.IntegerField(default=0)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def get_coach_name(self):
        """Get coach name whether internal or external"""
        if self.teacher:
            return self.teacher.full_name
        return self.external_name

    def __str__(self):
        return f"{self.activity.name} - {self.get_coach_name()} ({self.get_coach_type_display()})"

class ActivityFeeStructure(models.Model):
    """Fee structure for different activities"""
    FEE_TYPES = [
        ('registration', 'Registration Fee'),
        ('monthly', 'Monthly Fee'),
        ('term', 'Term Fee'),
        ('annual', 'Annual Fee'),
        ('equipment', 'Equipment Fee'),
        ('competition', 'Competition Fee'),
        ('uniform', 'Uniform Fee'),
        ('transport', 'Transport Fee'),
    ]

    id = models.AutoField(primary_key=True)
    activity = models.ForeignKey(CoCurricularActivity, on_delete=models.CASCADE, related_name='fee_structures')
    fee_type = models.CharField(max_length=20, choices=FEE_TYPES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField(blank=True)
    is_mandatory = models.BooleanField(default=True)
    due_date = models.DateField(null=True, blank=True)
    academic_year = models.CharField(max_length=20)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['activity', 'fee_type', 'academic_year']

    def __str__(self):
        return f"{self.activity.name} - {self.get_fee_type_display()} - KES {self.amount}"

class ActivityFeePayment(models.Model):
    """Track activity fee payments"""
    PAYMENT_METHODS = [
        ('cash', 'Cash'),
        ('mpesa', 'M-Pesa'),
        ('bank_transfer', 'Bank Transfer'),
        ('cheque', 'Cheque'),
        ('card', 'Credit/Debit Card'),
    ]

    PAYMENT_STATUSES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]

    id = models.AutoField(primary_key=True)
    student_activity = models.ForeignKey(StudentActivity, on_delete=models.CASCADE, related_name='fee_payments')
    fee_structure = models.ForeignKey(ActivityFeeStructure, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    payment_date = models.DateTimeField(auto_now_add=True)
    reference_number = models.CharField(max_length=100, blank=True)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUSES, default='completed')
    notes = models.TextField(blank=True)
    processed_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return f"Payment - {self.student_activity.student.full_name} - {self.fee_structure.get_fee_type_display()}"

class ActivityFeeDiscount(models.Model):
    """Manage discounts for activity fees"""
    DISCOUNT_TYPES = [
        ('percentage', 'Percentage'),
        ('fixed_amount', 'Fixed Amount'),
    ]

    DISCOUNT_REASONS = [
        ('scholarship', 'Scholarship'),
        ('financial_hardship', 'Financial Hardship'),
        ('sibling_discount', 'Sibling Discount'),
        ('staff_child', 'Staff Child'),
        ('academic_excellence', 'Academic Excellence'),
        ('loyalty', 'Loyalty Discount'),
        ('early_bird', 'Early Bird Discount'),
        ('other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    student_activity = models.ForeignKey(StudentActivity, on_delete=models.CASCADE, related_name='fee_discounts')
    fee_structure = models.ForeignKey(ActivityFeeStructure, on_delete=models.CASCADE)
    discount_type = models.CharField(max_length=20, choices=DISCOUNT_TYPES)
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.CharField(max_length=30, choices=DISCOUNT_REASONS)
    description = models.TextField(blank=True)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def calculate_discount_amount(self, base_fee):
        """Calculate the actual discount amount"""
        if self.discount_type == 'percentage':
            return base_fee * (self.discount_value / 100)
        else:
            return min(self.discount_value, base_fee)

    def __str__(self):
        return f"Discount - {self.student_activity.student.full_name} - {self.get_reason_display()}"
