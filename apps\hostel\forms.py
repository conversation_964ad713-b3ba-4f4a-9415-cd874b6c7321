from django import forms
from django.core.exceptions import ValidationError
from django.db import models
from .models import (
    Hostel, HostelRoom, HostelAllocation, HostelBed, MealPlan, 
    HostelWarden, BoardingFeeStructure, BoardingFeePayment, 
    BoardingFeeDiscount, HostelFacility, BoardingStudentStatus
)
from apps.students.models import Student
from datetime import date, datetime

class HostelForm(forms.ModelForm):
    class Meta:
        model = Hostel
        fields = [
            'name', 'capacity', 'gender', 'description', 'warden_name', 
            'warden_contact', 'building', 'floor', 'status'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Hostel name (e.g., Victoria House, King\'s Hall)'
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Total student capacity'
            }),
            'gender': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Hostel description and features'
            }),
            'warden_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Head warden name'
            }),
            'warden_contact': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Warden phone number'
            }),
            'building': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Building name/number'
            }),
            'floor': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Floor information'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
        }

class HostelRoomForm(forms.ModelForm):
    class Meta:
        model = HostelRoom
        fields = ['hostel', 'room_number', 'capacity', 'room_type', 'floor', 'status']
        widgets = {
            'hostel': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'room_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Room number (e.g., 101, A-12)'
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Number of beds in room'
            }),
            'room_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'floor': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Floor number/name'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        hostel = cleaned_data.get('hostel')
        room_number = cleaned_data.get('room_number')
        
        if hostel and room_number:
            # Check for duplicate room numbers in the same hostel
            existing = HostelRoom.objects.filter(hostel=hostel, room_number=room_number)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError("A room with this number already exists in this hostel.")
        
        return cleaned_data

class HostelBedForm(forms.ModelForm):
    class Meta:
        model = HostelBed
        fields = ['room', 'bed_number', 'status', 'notes']
        widgets = {
            'room': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'bed_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Bed number (e.g., 1, A, Upper-1)'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Bed condition notes'
            }),
        }

class MealPlanForm(forms.ModelForm):
    class Meta:
        model = MealPlan
        fields = [
            'name', 'meal_type', 'description', 'monthly_fee',
            'includes_breakfast', 'includes_lunch', 'includes_dinner', 'includes_snacks'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Meal plan name'
            }),
            'meal_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Meal plan details and inclusions'
            }),
            'monthly_fee': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Monthly fee (KES)',
                'step': '0.01'
            }),
            'includes_breakfast': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'includes_lunch': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'includes_dinner': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'includes_snacks': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
        }

class HostelAllocationForm(forms.ModelForm):
    student_index = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter student index number'
        })
    )
    
    class Meta:
        model = HostelAllocation
        fields = ['hostel', 'room', 'bed_number', 'allocation_date', 'fee', 'notes']
        widgets = {
            'hostel': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'room': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'bed_number': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Bed number'
            }),
            'allocation_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'fee': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Monthly boarding fee (KES)',
                'step': '0.01'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Allocation notes'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['allocation_date'].initial = date.today()
        # Initially empty rooms (will be populated via JavaScript based on hostel selection)
        self.fields['room'].queryset = HostelRoom.objects.none()

    def clean_student_index(self):
        student_index = self.cleaned_data['student_index']
        try:
            student = Student.objects.get(index_number=student_index)
            return student
        except Student.DoesNotExist:
            raise ValidationError("Student with this index number does not exist.")

    def clean(self):
        cleaned_data = super().clean()
        hostel = cleaned_data.get('hostel')
        room = cleaned_data.get('room')
        bed_number = cleaned_data.get('bed_number')
        student = cleaned_data.get('student_index')
        
        # Validate room belongs to hostel
        if hostel and room and room.hostel != hostel:
            raise ValidationError("Selected room does not belong to the selected hostel.")
        
        # Check if student already has active allocation
        if student:
            existing = HostelAllocation.objects.filter(student=student, status='active')
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError("Student already has an active hostel allocation.")
        
        # Check room capacity
        if room and bed_number:
            if bed_number > room.capacity:
                raise ValidationError(f"Bed number cannot exceed room capacity ({room.capacity}).")
            
            # Check if bed is already occupied
            existing_allocation = HostelAllocation.objects.filter(
                room=room, 
                bed_number=bed_number, 
                status='active'
            )
            if self.instance.pk:
                existing_allocation = existing_allocation.exclude(pk=self.instance.pk)
            if existing_allocation.exists():
                raise ValidationError(f"Bed {bed_number} in this room is already occupied.")
        
        return cleaned_data

class BoardingFeePaymentForm(forms.ModelForm):
    class Meta:
        model = BoardingFeePayment
        fields = ['amount', 'payment_method', 'payment_for_month', 'reference_number', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Payment amount (KES)',
                'step': '0.01'
            }),
            'payment_method': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'payment_for_month': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'month'
            }),
            'reference_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Payment reference/transaction ID'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Payment notes'
            }),
        }

class BoardingFeeDiscountForm(forms.ModelForm):
    class Meta:
        model = BoardingFeeDiscount
        fields = [
            'discount_type', 'discount_value', 'reason', 'description',
            'start_date', 'end_date'
        ]
        widgets = {
            'discount_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'discount_value': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Discount value',
                'step': '0.01'
            }),
            'reason': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Detailed reason for discount'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date <= start_date:
            raise ValidationError("End date must be after start date.")

        return cleaned_data

class HostelSearchForm(forms.Form):
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Search by student name, index number, hostel, or room'
        })
    )
    hostel = forms.ModelChoiceField(
        queryset=Hostel.objects.all(),
        required=False,
        empty_label="All Hostels",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    status = forms.ChoiceField(
        choices=[('', 'All Status')] + HostelAllocation.ALLOCATION_STATUSES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
