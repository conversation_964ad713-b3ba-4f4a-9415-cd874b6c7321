{% extends 'teachers/base.html' %}
{% load static %}

{% block teachers_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="flex items-center mb-6">
        <a href="{% url 'teachers:result_entry' %}" 
           class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Bulk Result Upload</h1>
            <p class="text-gray-600 mt-1">Upload exam results using Excel or CSV files</p>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-blue-900">Upload Instructions</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ol class="list-decimal list-inside space-y-1">
                        <li>Select the exam and subject from the dropdown below</li>
                        <li>Download the Excel template with student information</li>
                        <li>Fill in the marks column (values between 0-100)</li>
                        <li>Save the file and upload it back to the system</li>
                        <li>Review the upload results and fix any errors</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 1: Select Exam and Subject -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
            <span class="bg-blue-600 text-white rounded-full w-8 h-8 inline-flex items-center justify-center text-sm mr-3">1</span>
            Select Exam and Subject
        </h2>
        
        <form id="exam-selection-form" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="timetable_entry" class="block text-sm font-medium text-gray-700 mb-2">
                    Exam, Subject & Grade
                </label>
                <select id="timetable_entry" name="timetable_entry" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select exam, subject and grade...</option>
                    {% for exam_info in available_exams %}
                    <option value="{{ exam_info.timetable_entry.id }}">
                        {{ exam_info.exam.name }} - {{ exam_info.subject.name }} ({{ exam_info.grade.name }})
                        {% if exam_info.timetable_entry.exam_date %}
                        - {{ exam_info.timetable_entry.exam_date|date:"M d, Y" }}
                        {% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="button" id="download-template-btn" 
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                        disabled>
                    <i class="fas fa-download mr-2"></i>Download Template
                </button>
            </div>
        </form>
    </div>

    <!-- Step 2: Upload File -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
            <span class="bg-blue-600 text-white rounded-full w-8 h-8 inline-flex items-center justify-center text-sm mr-3">2</span>
            Upload Results File
        </h2>
        
        <form method="post" enctype="multipart/form-data" id="upload-form">
            {% csrf_token %}
            <input type="hidden" id="selected_timetable_entry" name="timetable_entry" value="">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="upload_file" class="block text-sm font-medium text-gray-700 mb-2">
                        Select File (Excel or CSV)
                    </label>
                    <input type="file" id="upload_file" name="upload_file" 
                           accept=".xlsx,.xls,.csv"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p class="mt-1 text-sm text-gray-500">Supported formats: .xlsx, .xls, .csv</p>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" id="upload-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                            disabled>
                        <i class="fas fa-upload mr-2"></i>Upload Results
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- File Format Requirements -->
    <div class="bg-gray-50 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">File Format Requirements</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Required Columns:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><code class="bg-gray-200 px-2 py-1 rounded">student_id</code> - Student ID number</li>
                    <li><code class="bg-gray-200 px-2 py-1 rounded">marks</code> - Marks obtained (0-100)</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Optional Columns:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><code class="bg-gray-200 px-2 py-1 rounded">student_name</code> - Student name (for reference)</li>
                    <li><code class="bg-gray-200 px-2 py-1 rounded">comments</code> - Additional comments</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sample Data Preview -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Sample Data Format</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">student_id</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">student_name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">marks</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">comments</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">STU001</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">John Doe</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">85</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Good performance</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">STU002</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jane Smith</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">92</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Excellent work</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">STU003</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Bob Johnson</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">78</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Needs improvement</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const timetableSelect = document.getElementById('timetable_entry');
    const downloadBtn = document.getElementById('download-template-btn');
    const uploadBtn = document.getElementById('upload-btn');
    const uploadFile = document.getElementById('upload_file');
    const hiddenTimetableEntry = document.getElementById('selected_timetable_entry');
    
    // Enable/disable download button based on selection
    timetableSelect.addEventListener('change', function() {
        const isSelected = this.value !== '';
        downloadBtn.disabled = !isSelected;
        
        if (isSelected) {
            hiddenTimetableEntry.value = this.value;
            downloadBtn.classList.remove('disabled:bg-gray-400');
            downloadBtn.classList.add('bg-green-600', 'hover:bg-green-700');
        } else {
            hiddenTimetableEntry.value = '';
            downloadBtn.classList.add('disabled:bg-gray-400');
            downloadBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
        }
        
        checkUploadReady();
    });
    
    // Enable/disable upload button
    uploadFile.addEventListener('change', function() {
        checkUploadReady();
    });
    
    function checkUploadReady() {
        const hasFile = uploadFile.files.length > 0;
        const hasSelection = timetableSelect.value !== '';
        const isReady = hasFile && hasSelection;
        
        uploadBtn.disabled = !isReady;
        
        if (isReady) {
            uploadBtn.classList.remove('disabled:bg-gray-400');
            uploadBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
        } else {
            uploadBtn.classList.add('disabled:bg-gray-400');
            uploadBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        }
    }
    
    // Download template
    downloadBtn.addEventListener('click', function() {
        if (timetableSelect.value) {
            const url = "{% url 'teachers:download_result_template' %}?timetable_entry=" + timetableSelect.value;
            window.location.href = url;
        }
    });
    
    // Form validation
    document.getElementById('upload-form').addEventListener('submit', function(e) {
        if (!timetableSelect.value) {
            e.preventDefault();
            alert('Please select an exam and subject first.');
            return false;
        }
        
        if (!uploadFile.files.length) {
            e.preventDefault();
            alert('Please select a file to upload.');
            return false;
        }
        
        // Show loading state
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
        uploadBtn.disabled = true;
    });
});
</script>
{% endblock %}
