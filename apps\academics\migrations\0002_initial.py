# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("academics", "0001_initial"),
        ("teachers", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="classroom",
            name="class_teacher",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="classrooms",
                to="teachers.teacher",
            ),
        ),
        migrations.AddField(
            model_name="department",
            name="head_teacher",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="headed_departments",
                to="teachers.teacher",
            ),
        ),
        migrations.AddField(
            model_name="grade",
            name="class_teacher",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="class_teacher_grades",
                to="teachers.teacher",
            ),
        ),
        migrations.AddField(
            model_name="subject",
            name="department",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="academics.department",
            ),
        ),
        migrations.AddField(
            model_name="subjectrouting",
            name="grade",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="academics.grade"
            ),
        ),
        migrations.AddField(
            model_name="subjectrouting",
            name="subject",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="academics.subject"
            ),
        ),
        migrations.AddField(
            model_name="subjectrouting",
            name="teacher",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="teachers.teacher"
            ),
        ),
    ]
