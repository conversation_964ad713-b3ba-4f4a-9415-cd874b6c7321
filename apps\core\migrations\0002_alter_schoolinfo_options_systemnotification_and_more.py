# Generated by Django 5.2.1 on 2025-06-10 14:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="schoolinfo",
            options={},
        ),
        migrations.CreateModel(
            name="SystemNotification",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                (
                    "notification_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("system_update", "System Update"),
                            ("maintenance", "Maintenance"),
                            ("security_alert", "Security Alert"),
                            ("announcement", "Announcement"),
                            ("reminder", "Reminder"),
                            ("warning", "Warning"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.Char<PERSON>ield(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                (
                    "target_audience",
                    models.CharField(
                        choices=[
                            ("all", "All Users"),
                            ("admin", "Administrators"),
                            ("teacher", "Teachers"),
                            ("student", "Students"),
                            ("parent", "Parents"),
                            ("accountant", "Accountants"),
                        ],
                        default="all",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("show_on_dashboard", models.BooleanField(default=True)),
                ("show_as_popup", models.BooleanField(default=False)),
                ("auto_dismiss", models.BooleanField(default=False)),
                ("dismiss_after_days", models.PositiveIntegerField(default=7)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("view_count", models.PositiveIntegerField(default=0)),
                ("dismiss_count", models.PositiveIntegerField(default=0)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "School Information",
            },
        ),
        migrations.CreateModel(
            name="SystemAnalytics",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("user_activity", "User Activity"),
                            ("attendance_trends", "Attendance Trends"),
                            ("academic_performance", "Academic Performance"),
                            ("financial_summary", "Financial Summary"),
                            ("system_usage", "System Usage"),
                            ("security_metrics", "Security Metrics"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("annual", "Annual"),
                        ],
                        max_length=20,
                    ),
                ),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                ("data", models.JSONField(default=dict)),
                ("total_users", models.PositiveIntegerField(default=0)),
                ("active_users", models.PositiveIntegerField(default=0)),
                ("total_students", models.PositiveIntegerField(default=0)),
                ("total_teachers", models.PositiveIntegerField(default=0)),
                (
                    "average_attendance_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                ("total_logins", models.PositiveIntegerField(default=0)),
                ("failed_logins", models.PositiveIntegerField(default=0)),
                ("security_incidents", models.PositiveIntegerField(default=0)),
                ("calculated_at", models.DateTimeField(auto_now=True)),
                (
                    "calculated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "System Analytics",
                "verbose_name_plural": "System Analytics",
                "ordering": ["-period_start", "metric_type"],
                "unique_together": {
                    ("metric_type", "period_type", "period_start", "period_end")
                },
            },
        ),
    ]
