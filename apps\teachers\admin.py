from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Teacher, TeacherQualification, TeacherDocument, TeacherSubjectSpecialization,
    TeacherAssignment, TeacherPerformanceEvaluation, TeacherEmploymentHistory,
    TeacherDepartmentTransfer, TeacherWorkload
)


class TeacherQualificationInline(admin.TabularInline):
    model = TeacherQualification
    extra = 0
    fields = ('qualification_type', 'qualification_name', 'institution', 'year_obtained', 'verification_status')
    readonly_fields = ('created_at', 'updated_at')


class TeacherDocumentInline(admin.TabularInline):
    model = TeacherDocument
    extra = 0
    fields = ('document_type', 'document_name', 'is_verified', 'upload_date')
    readonly_fields = ('upload_date', 'uploaded_by')


class TeacherSubjectSpecializationInline(admin.TabularInline):
    model = TeacherSubjectSpecialization
    extra = 0
    fields = ('subject', 'expertise_level', 'years_of_experience', 'certification_status')


class TeacherAssignmentInline(admin.TabularInline):
    model = TeacherAssignment
    extra = 0
    fields = ('subject', 'grade', 'assignment_type', 'lessons_per_week', 'is_active')
    readonly_fields = ('created_at',)


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = (
        'employee_id', 'full_name', 'gender', 'phone', 'email',
        'employment_type', 'department', 'status', 'current_teaching_load',
        'get_years_of_service', 'photo_preview'
    )

    list_filter = (
        'employment_type', 'status', 'department', 'gender',
        'employment_status', 'is_department_head', 'is_class_teacher'
    )

    search_fields = (
        'first_name', 'last_name', 'full_name', 'employee_id',
        'email', 'phone', 'national_id', 'tsc_number'
    )

    readonly_fields = (
        'full_name', 'employee_id', 'total_salary', 'created_at',
        'updated_at', 'get_age', 'get_years_of_service', 'get_workload_percentage'
    )

    fieldsets = (
        ('Basic Information', {
            'fields': (
                ('employee_id', 'status'),
                ('first_name', 'middle_name', 'last_name'),
                ('full_name', 'preferred_name'),
                ('gender', 'date_of_birth', 'place_of_birth'),
                ('marital_status', 'photo')
            )
        }),
        ('Identification', {
            'fields': (
                ('national_id', 'passport_number'),
                ('kra_pin', 'nhif_number', 'nssf_number'),
                ('tsc_number', 'teacher_registration_number')
            )
        }),
        ('Contact Information', {
            'fields': (
                ('phone', 'alternative_phone'),
                ('email', 'alternative_email'),
                ('address', 'city', 'county'),
                ('postal_address', 'postal_code')
            )
        }),
        ('Emergency Contact', {
            'fields': (
                ('emergency_contact_name', 'emergency_contact_relationship'),
                ('emergency_contact_phone', 'emergency_contact_address')
            )
        }),
        ('Employment Information', {
            'fields': (
                ('employment_type', 'employment_status'),
                ('employment_date', 'confirmation_date'),
                ('probation_end_date', 'contract_start_date', 'contract_end_date'),
                ('professional_qualification', 'teaching_experience_years'),
                ('previous_schools',)
            )
        }),
        ('Current Assignment', {
            'fields': (
                ('department', 'role'),
                ('is_department_head', 'is_class_teacher'),
                ('current_class',)
            )
        }),
        ('Salary Information', {
            'fields': (
                ('basic_salary', 'house_allowance'),
                ('transport_allowance', 'other_allowances'),
                ('total_salary',)
            )
        }),
        ('Performance & Workload', {
            'fields': (
                ('current_teaching_load', 'maximum_teaching_load'),
                ('performance_rating', 'last_evaluation_date'),
                ('get_workload_percentage',)
            )
        }),
        ('Legacy Fields', {
            'fields': (
                ('index_number', 'reg_date'),
                ('i_name', 'image_name')
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': (
                ('created_at', 'updated_at'),
                ('created_by', 'updated_by'),
                ('get_age', 'get_years_of_service')
            ),
            'classes': ('collapse',)
        })
    )

    inlines = [
        TeacherQualificationInline,
        TeacherSubjectSpecializationInline,
        TeacherAssignmentInline,
        TeacherDocumentInline,
    ]

    ordering = ('last_name', 'first_name')

    def photo_preview(self, obj):
        if obj.photo:
            return format_html(
                '<img src="{}" width="50" height="50" style="border-radius: 50%;" />',
                obj.photo.url
            )
        return "No Photo"
    photo_preview.short_description = "Photo"

    def get_years_of_service(self, obj):
        return f"{obj.get_years_of_service()} years"
    get_years_of_service.short_description = "Years of Service"

    def get_age(self, obj):
        age = obj.get_age()
        return f"{age} years" if age else "N/A"
    get_age.short_description = "Age"

    def get_workload_percentage(self, obj):
        percentage = obj.get_workload_percentage()
        color = "red" if percentage > 100 else "orange" if percentage > 80 else "green"
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, percentage
        )
    get_workload_percentage.short_description = "Workload %"


@admin.register(TeacherQualification)
class TeacherQualificationAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'qualification_type', 'qualification_name',
        'institution', 'year_obtained', 'verification_status'
    )
    list_filter = ('qualification_type', 'verification_status', 'is_teaching_qualification')
    search_fields = ('teacher__full_name', 'qualification_name', 'institution')
    ordering = ('-year_obtained',)


@admin.register(TeacherDocument)
class TeacherDocumentAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'document_type', 'document_name',
        'is_verified', 'upload_date', 'uploaded_by'
    )
    list_filter = ('document_type', 'is_verified', 'upload_date')
    search_fields = ('teacher__full_name', 'document_name')
    readonly_fields = ('upload_date', 'uploaded_by')
    ordering = ('-upload_date',)


@admin.register(TeacherSubjectSpecialization)
class TeacherSubjectSpecializationAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'subject', 'expertise_level',
        'years_of_experience', 'certification_status'
    )
    list_filter = ('expertise_level', 'certification_status', 'subject')
    search_fields = ('teacher__full_name', 'subject__name')
    ordering = ('teacher__last_name', 'subject__name')


@admin.register(TeacherAssignment)
class TeacherAssignmentAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'subject', 'grade', 'assignment_type',
        'academic_year', 'term', 'lessons_per_week', 'is_active'
    )
    list_filter = (
        'assignment_type', 'academic_year', 'term',
        'is_active', 'subject', 'grade'
    )
    search_fields = ('teacher__full_name', 'subject__name', 'grade__name')
    ordering = ('-academic_year', '-term', 'teacher__last_name')


@admin.register(TeacherPerformanceEvaluation)
class TeacherPerformanceEvaluationAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'evaluation_type', 'evaluation_date',
        'overall_rating', 'is_approved', 'evaluator'
    )
    list_filter = ('evaluation_type', 'is_approved', 'evaluation_date')
    search_fields = ('teacher__full_name', 'evaluator__username')
    readonly_fields = ('overall_rating',)
    ordering = ('-evaluation_date',)

    fieldsets = (
        ('Basic Information', {
            'fields': (
                ('teacher', 'evaluation_type'),
                ('evaluation_period_start', 'evaluation_period_end'),
                ('evaluation_date', 'evaluator')
            )
        }),
        ('Performance Ratings', {
            'fields': (
                ('teaching_effectiveness', 'classroom_management'),
                ('student_engagement', 'professional_development'),
                ('collaboration', 'punctuality'),
                ('overall_rating',)
            )
        }),
        ('Comments and Goals', {
            'fields': (
                'strengths', 'areas_for_improvement',
                'goals_for_next_period', 'evaluator_comments',
                'teacher_comments'
            )
        }),
        ('Approval', {
            'fields': (
                ('is_approved', 'approved_by', 'approval_date')
            )
        })
    )


@admin.register(TeacherEmploymentHistory)
class TeacherEmploymentHistoryAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'status_change', 'effective_date',
        'previous_status', 'new_status', 'processed_by'
    )
    list_filter = ('status_change', 'effective_date')
    search_fields = ('teacher__full_name', 'reason')
    readonly_fields = ('created_at',)
    ordering = ('-effective_date',)


@admin.register(TeacherDepartmentTransfer)
class TeacherDepartmentTransferAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'from_department', 'to_department',
        'transfer_type', 'transfer_status', 'request_date'
    )
    list_filter = ('transfer_type', 'transfer_status', 'request_date')
    search_fields = ('teacher__full_name', 'reason')
    ordering = ('-request_date',)


@admin.register(TeacherWorkload)
class TeacherWorkloadAdmin(admin.ModelAdmin):
    list_display = (
        'teacher', 'academic_year', 'term',
        'total_lessons_per_week', 'number_of_subjects',
        'workload_percentage', 'is_overloaded'
    )
    list_filter = ('academic_year', 'term', 'is_overloaded')
    search_fields = ('teacher__full_name',)
    readonly_fields = ('workload_percentage', 'is_overloaded', 'calculation_date')
    ordering = ('-academic_year', '-term', '-workload_percentage')
