{% extends 'students/base.html' %}
{% load static %}

{% block title %}Exam Dashboard - {{ student.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
    .exam-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .exam-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .upcoming-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .results-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .performance-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    .average-card {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }
    .grade-badge {
        font-size: 1.2em;
        padding: 8px 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Exam Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Exam Dashboard</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card upcoming-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Upcoming Exams</p>
                            <h4 class="mb-2">{{ upcoming_exams|length }}</h4>
                            <p class="text-muted mb-0"><span class="text-light fw-bold font-size-12 me-2">This Month</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-primary rounded-3">
                                <i class="ri-calendar-event-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card results-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Recent Results</p>
                            <h4 class="mb-2">{{ recent_results|length }}</h4>
                            <p class="text-muted mb-0"><span class="text-light fw-bold font-size-12 me-2">Available</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-warning rounded-3">
                                <i class="ri-file-list-3-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card performance-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Performance Trend</p>
                            <h4 class="mb-2">
                                {% if performance_trend > 0 %}
                                <i class="ri-arrow-up-line text-success"></i>
                                {% elif performance_trend < 0 %}
                                <i class="ri-arrow-down-line text-danger"></i>
                                {% else %}
                                <i class="ri-subtract-line text-warning"></i>
                                {% endif %}
                                {{ performance_trend|floatformat:1 }}%
                            </h4>
                            <p class="text-muted mb-0"><span class="text-light fw-bold font-size-12 me-2">vs Last Exam</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-info rounded-3">
                                <i class="ri-line-chart-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card average-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Overall Average</p>
                            <h4 class="mb-2">{{ overall_average|floatformat:1 }}%</h4>
                            <p class="text-muted mb-0"><span class="text-light fw-bold font-size-12 me-2">All Subjects</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-success rounded-3">
                                <i class="ri-trophy-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Exams -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Upcoming Exams</h4>
                </div>
                <div class="card-body">
                    {% if upcoming_exams %}
                        <div class="table-responsive">
                            <table class="table table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th>Exam</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for exam in upcoming_exams %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">{{ exam.exam.name }}</h6>
                                                <p class="text-muted mb-0 font-size-12">{{ exam.exam.exam_type.name }}</p>
                                            </div>
                                        </td>
                                        <td>{{ exam.subject.name }}</td>
                                        <td>{{ exam.exam_date|date:"M d, Y" }}</td>
                                        <td>{{ exam.start_time|time:"H:i" }}</td>
                                        <td>{{ exam.duration }} mins</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-calendar-event-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No upcoming exams</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Results -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Recent Results</h4>
                </div>
                <div class="card-body">
                    {% if recent_results %}
                        <div class="table-responsive">
                            <table class="table table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th>Exam</th>
                                        <th>Subject</th>
                                        <th>Score</th>
                                        <th>Grade</th>
                                        <th>Position</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for result in recent_results %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">{{ result.exam.name }}</h6>
                                                <p class="text-muted mb-0 font-size-12">{{ result.exam.exam_type.name }}</p>
                                            </div>
                                        </td>
                                        <td>{{ result.subject.name }}</td>
                                        <td>{{ result.score }}/{{ result.subject.max_score|default:"100" }}</td>
                                        <td>
                                            <span class="badge grade-badge bg-{% if result.grade == 'A' %}success{% elif result.grade == 'B' %}info{% elif result.grade == 'C' %}warning{% else %}danger{% endif %}">
                                                {{ result.grade }}
                                            </span>
                                        </td>
                                        <td>{{ result.position|default:"-" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'students:exam_results' %}" class="btn btn-primary btn-sm">View All Results</a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-file-list-3-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No recent results available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Subject Performance -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Subject Performance Overview</h4>
                </div>
                <div class="card-body">
                    {% if subject_performance %}
                        <div class="table-responsive">
                            <table class="table table-nowrap align-middle">
                                <thead>
                                    <tr>
                                        <th>Subject</th>
                                        <th>Latest Score</th>
                                        <th>Average</th>
                                        <th>Best Score</th>
                                        <th>Progress</th>
                                        <th>Grade</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for performance in subject_performance %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-xs me-3">
                                                    <span class="avatar-title rounded-circle bg-primary">
                                                        {{ performance.subject.name|first }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ performance.subject.name }}</h6>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ performance.latest_score|default:"-" }}</td>
                                        <td>{{ performance.average_score|floatformat:1 }}%</td>
                                        <td>{{ performance.best_score|default:"-" }}</td>
                                        <td>
                                            <div class="progress progress-sm">
                                                <div class="progress-bar bg-{% if performance.average_score >= 75 %}success{% elif performance.average_score >= 50 %}warning{% else %}danger{% endif %}" 
                                                     style="width: {{ performance.average_score }}%"></div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge grade-badge bg-{% if performance.latest_grade == 'A' %}success{% elif performance.latest_grade == 'B' %}info{% elif performance.latest_grade == 'C' %}warning{% else %}danger{% endif %}">
                                                {{ performance.latest_grade|default:"-" }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-bar-chart-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No performance data available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Quick Actions</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'students:exam_results' %}" class="btn btn-primary">
                            <i class="ri-file-list-3-line me-2"></i>View All Results
                        </a>
                        <a href="{% url 'students:exam_schedule' %}" class="btn btn-info">
                            <i class="ri-calendar-event-line me-2"></i>Exam Schedule
                        </a>
                        <a href="{% url 'students:performance_report' %}" class="btn btn-success">
                            <i class="ri-bar-chart-line me-2"></i>Performance Report
                        </a>
                        <a href="{% url 'students:download_results' %}" class="btn btn-warning">
                            <i class="ri-download-line me-2"></i>Download Results
                        </a>
                    </div>
                </div>
            </div>

            <!-- Exam Calendar -->
            <div class="card mt-3">
                <div class="card-header">
                    <h4 class="card-title mb-0">Exam Calendar</h4>
                </div>
                <div class="card-body">
                    <div id="examCalendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize exam calendar
    document.addEventListener('DOMContentLoaded', function() {
        // Simple calendar implementation
        const calendarEl = document.getElementById('examCalendar');
        if (calendarEl) {
            // You can integrate with a calendar library like FullCalendar here
            calendarEl.innerHTML = '<p class="text-center text-muted">Calendar integration coming soon</p>';
        }
    });
</script>
{% endblock %}
