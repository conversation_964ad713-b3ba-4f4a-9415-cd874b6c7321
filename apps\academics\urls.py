from django.urls import path
from . import views

app_name = 'academics'

urlpatterns = [
    # Main pages
    path('', views.academics_dashboard, name='dashboard'),
    path('settings/', views.academics_settings, name='settings'),

    # School Info Management
    path('school-info/', views.school_info_view, name='school_info'),
    path('school-info/edit/', views.school_info_edit, name='school_info_edit'),

    # Grade Management
    path('grades/', views.grades_list, name='grades_list'),
    path('grades/create/', views.grade_create, name='grade_create'),
    path('grades/<int:pk>/edit/', views.grade_edit, name='grade_edit'),
    path('grades/<int:pk>/delete/', views.grade_delete, name='grade_delete'),

    # Department Management
    path('departments/', views.departments_list, name='departments_list'),
    path('departments/create/', views.department_create, name='department_create'),
    path('departments/<int:pk>/edit/', views.department_edit, name='department_edit'),
    path('departments/<int:pk>/delete/', views.department_delete, name='department_delete'),

    # Subject Management
    path('subjects/', views.subjects_list, name='subjects_list'),
    path('subjects/create/', views.subject_create, name='subject_create'),
    path('subjects/<int:pk>/edit/', views.subject_edit, name='subject_edit'),
    path('subjects/<int:pk>/delete/', views.subject_delete, name='subject_delete'),

    # Classroom Management
    path('classrooms/', views.classrooms_list, name='classrooms_list'),
    path('classrooms/create/', views.classroom_create, name='classroom_create'),
    path('classrooms/<int:pk>/edit/', views.classroom_edit, name='classroom_edit'),
    path('classrooms/<int:pk>/delete/', views.classroom_delete, name='classroom_delete'),

    # System Settings
    path('system-settings/', views.system_settings, name='system_settings'),
]
