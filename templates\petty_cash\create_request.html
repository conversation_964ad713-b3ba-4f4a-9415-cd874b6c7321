{% extends 'base.html' %}
{% load static %}

{% block title %}Create Petty Cash Request{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex items-center mb-6">
        <a href="{% url 'petty_cash:dashboard' %}" 
           class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Create Petty Cash Request</h1>
            <p class="text-gray-600 mt-1">Submit a new request for petty cash funding</p>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Request Details</h2>
        </div>
        
        <form method="post" enctype="multipart/form-data" class="p-6">
            {% csrf_token %}
            
            <!-- Display form errors -->
            {% if form.non_field_errors %}
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                            <div class="mt-2 text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Fund Selection -->
                <div>
                    <label for="{{ form.fund.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Fund <span class="text-red-500">*</span>
                    </label>
                    {{ form.fund }}
                    {% if form.fund.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.fund.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Category Selection -->
                <div>
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Category <span class="text-red-500">*</span>
                    </label>
                    {{ form.category }}
                    {% if form.category.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.category.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Request Type -->
                <div>
                    <label for="{{ form.request_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Request Type <span class="text-red-500">*</span>
                    </label>
                    {{ form.request_type }}
                    {% if form.request_type.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.request_type.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Priority -->
                <div>
                    <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Priority
                    </label>
                    {{ form.priority }}
                    {% if form.priority.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.priority.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Amount -->
                <div>
                    <label for="{{ form.amount_requested.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Amount Requested (KSh) <span class="text-red-500">*</span>
                    </label>
                    {{ form.amount_requested }}
                    {% if form.amount_requested.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.amount_requested.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Required Date -->
                <div>
                    <label for="{{ form.required_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Required Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.required_date }}
                    {% if form.required_date.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.required_date.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Title -->
            <div class="mt-6">
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Title <span class="text-red-500">*</span>
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Description <span class="text-red-500">*</span>
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">Provide detailed information about your request</p>
            </div>

            <!-- Supporting Documents -->
            <div class="mt-6">
                <label for="{{ form.supporting_documents.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Supporting Documents
                </label>
                {{ form.supporting_documents }}
                {% if form.supporting_documents.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.supporting_documents.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">Upload any supporting documents (PDF, DOC, DOCX, JPG, PNG)</p>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex justify-end space-x-4">
                <a href="{% url 'petty_cash:dashboard' %}" 
                   class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-paper-plane mr-2"></i>Submit Request
                </button>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-blue-900">Request Guidelines</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Ensure all required fields are completed accurately</li>
                        <li>Provide detailed justification for your request</li>
                        <li>Attach relevant supporting documents when available</li>
                        <li>Allow sufficient time for approval process</li>
                        <li>Contact the finance office for urgent requests</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add form validation and dynamic behavior
    document.addEventListener('DOMContentLoaded', function() {
        const categorySelect = document.getElementById('{{ form.category.id_for_label }}');
        const amountInput = document.getElementById('{{ form.amount_requested.id_for_label }}');
        
        // Add currency formatting
        if (amountInput) {
            amountInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^\d.]/g, '');
                if (value) {
                    e.target.value = parseFloat(value).toFixed(2);
                }
            });
        }
        
        // Category change handler
        if (categorySelect) {
            categorySelect.addEventListener('change', function() {
                // You can add category-specific logic here
                console.log('Category changed to:', this.value);
            });
        }
    });
</script>
{% endblock %}
