from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.http import JsonResponse
from django.utils import timezone
from datetime import date, timedelta, datetime
import json

from .models import (
    EnhancedPettyCashRequest, PettyCashCategory, PettyCashFund,
    PettyCashExpense, PettyCashReplenishment, PettyCashReconciliation
)
from .forms import PettyCashRequestForm, PettyCashExpenseForm
from apps.teachers.models import Teacher


@login_required
def petty_cash_dashboard(request):
    """
    Petty cash dashboard for teachers and administrators
    """
    user = request.user

    # Check if user is a teacher
    try:
        teacher = Teacher.objects.get(user=user)
        is_teacher = True
    except Teacher.DoesNotExist:
        teacher = None
        is_teacher = False

    # Get user's requests
    if is_teacher:
        user_requests = EnhancedPettyCashRequest.objects.filter(
            requested_by=user
        ).order_by('-created_at')[:10]
    else:
        user_requests = EnhancedPettyCashRequest.objects.all().order_by('-created_at')[:10]

    # Statistics
    total_requests = EnhancedPettyCashRequest.objects.filter(requested_by=user).count() if is_teacher else EnhancedPettyCashRequest.objects.count()
    pending_requests = EnhancedPettyCashRequest.objects.filter(
        requested_by=user if is_teacher else Q(),
        status__in=['submitted', 'under_review']
    ).count()
    approved_requests = EnhancedPettyCashRequest.objects.filter(
        requested_by=user if is_teacher else Q(),
        status='approved'
    ).count()
    total_amount = EnhancedPettyCashRequest.objects.filter(
        requested_by=user if is_teacher else Q(),
        status='approved'
    ).aggregate(total=Sum('amount_approved'))['total'] or 0

    # Available categories
    categories = PettyCashCategory.objects.filter(is_active=True)

    # Available funds
    funds = PettyCashFund.objects.filter(status='active')

    context = {
        'is_teacher': is_teacher,
        'teacher': teacher,
        'user_requests': user_requests,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'total_amount': total_amount,
        'categories': categories,
        'funds': funds,
    }

    return render(request, 'petty_cash/dashboard.html', context)


@login_required
def create_request(request):
    """
    Create a new petty cash request
    """
    if request.method == 'POST':
        form = PettyCashRequestForm(request.POST, request.FILES)
        if form.is_valid():
            petty_request = form.save(commit=False)
            petty_request.requested_by = request.user
            petty_request.status = 'submitted'
            petty_request.save()

            messages.success(request, f'Petty cash request {petty_request.request_number} submitted successfully!')
            return redirect('petty_cash:dashboard')
    else:
        form = PettyCashRequestForm()

    # Get available categories and funds
    categories = PettyCashCategory.objects.filter(is_active=True)
    funds = PettyCashFund.objects.filter(status='active')

    context = {
        'form': form,
        'categories': categories,
        'funds': funds,
    }

    return render(request, 'petty_cash/create_request.html', context)


@login_required
def request_list(request):
    """
    List all petty cash requests for the user
    """
    user = request.user

    # Check if user is a teacher
    try:
        teacher = Teacher.objects.get(user=user)
        is_teacher = True
        requests = EnhancedPettyCashRequest.objects.filter(requested_by=user)
    except Teacher.DoesNotExist:
        is_teacher = False
        requests = EnhancedPettyCashRequest.objects.all()

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        requests = requests.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        requests = requests.filter(
            Q(request_number__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(requests.order_by('-created_at'), 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'is_teacher': is_teacher,
        'status_choices': EnhancedPettyCashRequest.REQUEST_STATUSES,
    }

    return render(request, 'petty_cash/request_list.html', context)


@login_required
def request_detail(request, pk):
    """
    View details of a specific petty cash request
    """
    petty_request = get_object_or_404(EnhancedPettyCashRequest, pk=pk)

    # Check permissions - teachers can only view their own requests
    try:
        teacher = Teacher.objects.get(user=request.user)
        if petty_request.requested_by != request.user:
            messages.error(request, 'You can only view your own requests.')
            return redirect('petty_cash:dashboard')
    except Teacher.DoesNotExist:
        # Non-teachers (admins) can view all requests
        pass

    context = {
        'request': petty_request,
    }

    return render(request, 'petty_cash/request_detail.html', context)


@login_required
def edit_request(request, pk):
    """
    Edit a petty cash request (only if in draft or submitted status)
    """
    petty_request = get_object_or_404(EnhancedPettyCashRequest, pk=pk)

    # Check permissions
    if petty_request.requested_by != request.user:
        messages.error(request, 'You can only edit your own requests.')
        return redirect('petty_cash:dashboard')

    # Check if request can be edited
    if petty_request.status not in ['draft', 'submitted']:
        messages.error(request, 'This request cannot be edited in its current status.')
        return redirect('petty_cash:request_detail', pk=pk)

    if request.method == 'POST':
        form = PettyCashRequestForm(request.POST, request.FILES, instance=petty_request)
        if form.is_valid():
            form.save()
            messages.success(request, 'Request updated successfully!')
            return redirect('petty_cash:request_detail', pk=pk)
    else:
        form = PettyCashRequestForm(instance=petty_request)

    context = {
        'form': form,
        'request': petty_request,
    }

    return render(request, 'petty_cash/edit_request.html', context)


@login_required
def cancel_request(request, pk):
    """
    Cancel a petty cash request
    """
    petty_request = get_object_or_404(EnhancedPettyCashRequest, pk=pk)

    # Check permissions
    if petty_request.requested_by != request.user:
        messages.error(request, 'You can only cancel your own requests.')
        return redirect('petty_cash:dashboard')

    # Check if request can be cancelled
    if petty_request.status in ['approved', 'disbursed', 'completed', 'cancelled']:
        messages.error(request, 'This request cannot be cancelled in its current status.')
        return redirect('petty_cash:request_detail', pk=pk)

    if request.method == 'POST':
        petty_request.status = 'cancelled'
        petty_request.save()
        messages.success(request, 'Request cancelled successfully!')
        return redirect('petty_cash:dashboard')

    context = {
        'request': petty_request,
    }

    return render(request, 'petty_cash/cancel_request.html', context)


@login_required
def admin_request_list(request):
    """
    Admin view for managing all petty cash requests
    """
    # Check if user has admin permissions (you may want to add proper permission checks)
    try:
        Teacher.objects.get(user=request.user)
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('petty_cash:dashboard')
    except Teacher.DoesNotExist:
        pass  # Non-teachers can access admin views

    requests = EnhancedPettyCashRequest.objects.all()

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        requests = requests.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        requests = requests.filter(
            Q(request_number__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(requested_by__first_name__icontains=search_query) |
            Q(requested_by__last_name__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(requests.order_by('-created_at'), 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': EnhancedPettyCashRequest.REQUEST_STATUSES,
    }

    return render(request, 'petty_cash/admin_request_list.html', context)


@login_required
def approve_request(request, pk):
    """
    Approve a petty cash request
    """
    petty_request = get_object_or_404(EnhancedPettyCashRequest, pk=pk)

    # Check admin permissions
    try:
        Teacher.objects.get(user=request.user)
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('petty_cash:dashboard')
    except Teacher.DoesNotExist:
        pass

    if request.method == 'POST':
        approved_amount = request.POST.get('approved_amount')
        approval_notes = request.POST.get('approval_notes', '')

        try:
            approved_amount = float(approved_amount)
            if approved_amount <= 0:
                raise ValueError("Amount must be positive")

            petty_request.amount_approved = approved_amount
            petty_request.approval_notes = approval_notes
            petty_request.approved_by = request.user
            petty_request.approved_date = date.today()
            petty_request.status = 'approved'
            petty_request.save()

            messages.success(request, f'Request {petty_request.request_number} approved successfully!')
            return redirect('petty_cash:admin_request_list')

        except (ValueError, TypeError):
            messages.error(request, 'Invalid approved amount.')

    context = {
        'request': petty_request,
    }

    return render(request, 'petty_cash/approve_request.html', context)


@login_required
def reject_request(request, pk):
    """
    Reject a petty cash request
    """
    petty_request = get_object_or_404(EnhancedPettyCashRequest, pk=pk)

    # Check admin permissions
    try:
        Teacher.objects.get(user=request.user)
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('petty_cash:dashboard')
    except Teacher.DoesNotExist:
        pass

    if request.method == 'POST':
        rejection_reason = request.POST.get('rejection_reason', '')

        if not rejection_reason.strip():
            messages.error(request, 'Rejection reason is required.')
        else:
            petty_request.rejection_reason = rejection_reason
            petty_request.approved_by = request.user
            petty_request.approved_date = date.today()
            petty_request.status = 'rejected'
            petty_request.save()

            messages.success(request, f'Request {petty_request.request_number} rejected.')
            return redirect('petty_cash:admin_request_list')

    context = {
        'request': petty_request,
    }

    return render(request, 'petty_cash/reject_request.html', context)


@login_required
def expense_list(request):
    """
    List expenses for the current user
    """
    user = request.user

    # Check if user is a teacher
    try:
        teacher = Teacher.objects.get(user=user)
        is_teacher = True
        expenses = PettyCashExpense.objects.filter(incurred_by=user)
    except Teacher.DoesNotExist:
        is_teacher = False
        expenses = PettyCashExpense.objects.all()

    # Pagination
    paginator = Paginator(expenses.order_by('-expense_date'), 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'is_teacher': is_teacher,
    }

    return render(request, 'petty_cash/expense_list.html', context)


@login_required
def create_expense(request):
    """
    Create a new expense record
    """
    if request.method == 'POST':
        form = PettyCashExpenseForm(request.POST, request.FILES)
        if form.is_valid():
            expense = form.save(commit=False)
            expense.incurred_by = request.user
            # You might want to set the fund based on user's default fund
            expense.save()

            messages.success(request, f'Expense {expense.expense_number} recorded successfully!')
            return redirect('petty_cash:expense_list')
    else:
        form = PettyCashExpenseForm()

    context = {
        'form': form,
    }

    return render(request, 'petty_cash/create_expense.html', context)


@login_required
def expense_detail(request, pk):
    """
    View expense details
    """
    expense = get_object_or_404(PettyCashExpense, pk=pk)

    # Check permissions for teachers
    try:
        teacher = Teacher.objects.get(user=request.user)
        if expense.incurred_by != request.user:
            messages.error(request, 'You can only view your own expenses.')
            return redirect('petty_cash:expense_list')
    except Teacher.DoesNotExist:
        pass

    context = {
        'expense': expense,
    }

    return render(request, 'petty_cash/expense_detail.html', context)


@login_required
def reports_dashboard(request):
    """
    Reports dashboard for petty cash
    """
    # Basic statistics
    total_requests = EnhancedPettyCashRequest.objects.count()
    pending_requests = EnhancedPettyCashRequest.objects.filter(
        status__in=['submitted', 'under_review']
    ).count()
    approved_amount = EnhancedPettyCashRequest.objects.filter(
        status='approved'
    ).aggregate(total=Sum('amount_approved'))['total'] or 0

    # Monthly statistics
    from django.db.models import Count
    from django.utils import timezone
    from datetime import datetime, timedelta

    # Get current month data
    current_month = timezone.now().replace(day=1)
    monthly_requests = EnhancedPettyCashRequest.objects.filter(
        created_at__gte=current_month
    ).count()

    # Category breakdown
    category_stats = EnhancedPettyCashRequest.objects.filter(
        status='approved'
    ).values('category__name').annotate(
        count=Count('id'),
        total_amount=Sum('amount_approved')
    ).order_by('-total_amount')[:10]

    context = {
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_amount': approved_amount,
        'monthly_requests': monthly_requests,
        'category_stats': category_stats,
    }

    return render(request, 'petty_cash/reports_dashboard.html', context)


@login_required
def summary_report(request):
    """
    Generate summary report
    """
    # Date range filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    requests = EnhancedPettyCashRequest.objects.all()

    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            requests = requests.filter(created_at__date__gte=date_from)
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            requests = requests.filter(created_at__date__lte=date_to)
        except ValueError:
            pass

    # Summary statistics
    summary = {
        'total_requests': requests.count(),
        'approved_requests': requests.filter(status='approved').count(),
        'rejected_requests': requests.filter(status='rejected').count(),
        'pending_requests': requests.filter(status__in=['submitted', 'under_review']).count(),
        'total_approved_amount': requests.filter(status='approved').aggregate(
            total=Sum('amount_approved')
        )['total'] or 0,
        'total_requested_amount': requests.aggregate(
            total=Sum('amount_requested')
        )['total'] or 0,
    }

    # Status breakdown
    status_breakdown = requests.values('status').annotate(
        count=Count('id')
    ).order_by('status')

    context = {
        'summary': summary,
        'status_breakdown': status_breakdown,
        'date_from': date_from,
        'date_to': date_to,
    }

    return render(request, 'petty_cash/summary_report.html', context)


# API Views
@login_required
def api_categories(request):
    """
    API endpoint to get categories
    """
    categories = PettyCashCategory.objects.filter(is_active=True).values(
        'id', 'name', 'category_type', 'approval_threshold'
    )
    return JsonResponse(list(categories), safe=False)


@login_required
def api_funds(request):
    """
    API endpoint to get available funds
    """
    funds = PettyCashFund.objects.filter(status='active').values(
        'id', 'fund_name', 'current_balance', 'maximum_limit'
    )
    return JsonResponse(list(funds), safe=False)
