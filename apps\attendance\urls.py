from django.urls import path
from . import views

app_name = 'attendance'

urlpatterns = [
    # Dashboard and main views
    path('', views.attendance_dashboard, name='dashboard'),

    # Period management
    path('periods/', views.manage_attendance_periods, name='manage_periods'),
    path('student/entry/', views.student_attendance_entry, name='student_attendance_entry'),
    path('student/bulk/', views.bulk_student_attendance_entry, name='bulk_student_attendance_entry'),
    path('teacher/entry/', views.teacher_attendance_entry, name='teacher_attendance_entry'),

    # Enhanced bulk operations
    path('class/marking/', views.class_attendance_marking, name='class_marking'),
    path('bulk/import/', views.bulk_attendance_import, name='bulk_import'),

    # Analytics and reporting
    path('analytics/', views.attendance_analytics, name='analytics'),
    path('reports/generate/', views.generate_attendance_report, name='generate_report'),
    path('reports/', views.attendance_reports_list, name='reports'),
    path('student/history/', views.student_attendance_history, name='student_attendance_history'),
    path('student/history/<int:student_id>/', views.student_attendance_history, name='student_attendance_history_by_id'),
    path('class/history/', views.class_attendance_history, name='class_attendance_history'),
    path('class/history/<int:grade_id>/', views.class_attendance_history, name='class_attendance_history_by_id'),
    path('teacher/history/', views.teacher_attendance_history, name='teacher_attendance_history'),
    path('teacher/history/<int:teacher_id>/', views.teacher_attendance_history, name='teacher_attendance_history_by_id'),
    path('student/history/export/', views.export_student_attendance_csv, name='export_student_attendance_csv'),
    path('student/history/<int:student_id>/export/', views.export_student_attendance_csv, name='export_student_attendance_csv_by_id'),
    path('class/history/export/', views.export_class_attendance_csv, name='export_class_attendance_csv'),
    path('class/history/<int:grade_id>/export/', views.export_class_attendance_csv, name='export_class_attendance_csv_by_id'),
    path('teacher/history/export/', views.export_teacher_attendance_csv, name='export_teacher_attendance_csv'),
    path('teacher/history/<int:teacher_id>/export/', views.export_teacher_attendance_csv, name='export_teacher_attendance_csv_by_id'),
    path('student/history/export/pdf/', views.export_student_attendance_pdf, name='export_student_attendance_pdf'),
    path('student/history/<int:student_id>/export/pdf/', views.export_student_attendance_pdf, name='export_student_attendance_pdf_by_id'),
    path('class/history/export/pdf/', views.export_class_attendance_pdf, name='export_class_attendance_pdf'),
    path('class/history/<int:grade_id>/export/pdf/', views.export_class_attendance_pdf, name='export_class_attendance_pdf_by_id'),
    path('teacher/history/export/pdf/', views.export_teacher_attendance_pdf, name='export_teacher_attendance_pdf'),
    path('teacher/history/<int:teacher_id>/export/pdf/', views.export_teacher_attendance_pdf, name='export_teacher_attendance_pdf_by_id'),
]