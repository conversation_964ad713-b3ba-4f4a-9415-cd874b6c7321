{% extends 'exams/base.html' %}

{% block exam_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Result Entry</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        {{ timetable.exam.name }} - {{ timetable.subject.name }} ({{ timetable.grade.name }})
                    </p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'exams:exam_detail' timetable.exam.id %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Exam
                    </a>
                    <a href="{% url 'exams:bulk_result_upload' timetable.exam.id %}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-upload mr-2"></i>Bulk Upload
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Exam Details -->
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Exam Date</h3>
                    <p class="mt-1 text-sm text-gray-900">{{ timetable.exam_date|date:"M d, Y" }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Duration</h3>
                    <p class="mt-1 text-sm text-gray-900">{{ timetable.duration_minutes }} minutes</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Total Marks</h3>
                    <p class="mt-1 text-sm text-gray-900">{{ timetable.total_marks }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Expected Candidates</h3>
                    <p class="mt-1 text-sm text-gray-900">{{ timetable.expected_candidates|default:students.count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Result Entry Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Enter Student Results</h2>
            <p class="text-sm text-gray-600 mt-1">Enter marks for each student. Leave blank if student was absent.</p>
        </div>
        
        <form method="post" class="px-6 py-4">
            {% csrf_token %}
            
            <!-- Bulk Actions -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Bulk Actions</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Mark All Present</label>
                        <button type="button" onclick="markAllPresent()" 
                                class="mt-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                            Mark All Present
                        </button>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Set Default Marks</label>
                        <div class="mt-1 flex">
                            <input type="number" id="defaultMarks" min="0" max="{{ timetable.total_marks }}" 
                                   class="flex-1 px-3 py-1 border border-gray-300 rounded-l text-sm">
                            <button type="button" onclick="setDefaultMarks()" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-r text-sm">
                                Apply
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Clear All</label>
                        <button type="button" onclick="clearAll()" 
                                class="mt-1 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                            Clear All
                        </button>
                    </div>
                </div>
            </div>

            <!-- Students Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Student
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Student ID
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Marks (out of {{ timetable.total_marks }})
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Attendance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Remarks
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for student in students %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">
                                                {{ student.full_name|slice:":2"|upper }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ student.full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ student.current_grade.name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ student.student_id|default:student.index_number }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="number" 
                                       name="marks_{{ student.id }}" 
                                       id="marks_{{ student.id }}"
                                       min="0" 
                                       max="{{ timetable.total_marks }}" 
                                       step="0.01"
                                       value="{% if student.id in existing_results %}{{ existing_results|get_item:student.id.marks_obtained }}{% endif %}"
                                       class="w-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                       onchange="calculatePercentage(this, {{ student.id }})">
                                <span id="percentage_{{ student.id }}" class="ml-2 text-sm text-gray-500"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <select name="attendance_{{ student.id }}" 
                                        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                    <option value="present" 
                                        {% if student.id in existing_results and existing_results|get_item:student.id.attendance_status == 'present' %}selected{% endif %}>
                                        Present
                                    </option>
                                    <option value="absent" 
                                        {% if student.id in existing_results and existing_results|get_item:student.id.attendance_status == 'absent' %}selected{% endif %}>
                                        Absent
                                    </option>
                                    <option value="late" 
                                        {% if student.id in existing_results and existing_results|get_item:student.id.attendance_status == 'late' %}selected{% endif %}>
                                        Late
                                    </option>
                                </select>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="text" 
                                       name="remarks_{{ student.id }}" 
                                       value="{% if student.id in existing_results %}{{ existing_results|get_item:student.id.teacher_remarks }}{% endif %}"
                                       class="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                       placeholder="Optional">
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                                No students found for this grade.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end space-x-3">
                <a href="{% url 'exams:exam_detail' timetable.exam.id %}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-save mr-2"></i>Save Results
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function markAllPresent() {
    const attendanceSelects = document.querySelectorAll('select[name^="attendance_"]');
    attendanceSelects.forEach(select => {
        select.value = 'present';
    });
}

function setDefaultMarks() {
    const defaultMarks = document.getElementById('defaultMarks').value;
    if (defaultMarks) {
        const marksInputs = document.querySelectorAll('input[name^="marks_"]');
        marksInputs.forEach(input => {
            input.value = defaultMarks;
            const studentId = input.name.split('_')[1];
            calculatePercentage(input, studentId);
        });
    }
}

function clearAll() {
    const marksInputs = document.querySelectorAll('input[name^="marks_"]');
    const remarksInputs = document.querySelectorAll('input[name^="remarks_"]');
    const percentageSpans = document.querySelectorAll('span[id^="percentage_"]');
    
    marksInputs.forEach(input => input.value = '');
    remarksInputs.forEach(input => input.value = '');
    percentageSpans.forEach(span => span.textContent = '');
}

function calculatePercentage(input, studentId) {
    const marks = parseFloat(input.value) || 0;
    const totalMarks = {{ timetable.total_marks }};
    const percentage = (marks / totalMarks * 100).toFixed(1);
    
    const percentageSpan = document.getElementById(`percentage_${studentId}`);
    if (marks > 0) {
        percentageSpan.textContent = `(${percentage}%)`;
        
        // Color code based on performance
        if (percentage >= 80) {
            percentageSpan.className = 'ml-2 text-sm text-green-600 font-medium';
        } else if (percentage >= 60) {
            percentageSpan.className = 'ml-2 text-sm text-blue-600 font-medium';
        } else if (percentage >= 40) {
            percentageSpan.className = 'ml-2 text-sm text-yellow-600 font-medium';
        } else {
            percentageSpan.className = 'ml-2 text-sm text-red-600 font-medium';
        }
    } else {
        percentageSpan.textContent = '';
    }
}

// Calculate percentages on page load for existing results
document.addEventListener('DOMContentLoaded', function() {
    const marksInputs = document.querySelectorAll('input[name^="marks_"]');
    marksInputs.forEach(input => {
        if (input.value) {
            const studentId = input.name.split('_')[1];
            calculatePercentage(input, studentId);
        }
    });
});
</script>
{% endblock %}
