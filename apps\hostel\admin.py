from django.contrib import admin
from .models import Hostel, HostelRoom, HostelAllocation

@admin.register(Hostel)
class HostelAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'capacity', 'gender', 'description', 'warden_name', 'warden_contact', 'building', 'floor', 'status')
    search_fields = ('name', 'warden_name', 'warden_contact', 'building')
    list_filter = ('gender', 'status', 'building', 'floor')
    ordering = ('name',)

@admin.register(HostelRoom)
class HostelRoomAdmin(admin.ModelAdmin):
    list_display = ('id', 'hostel', 'room_number', 'capacity', 'current_occupancy', 'room_type', 'floor', 'status')
    search_fields = ('hostel__name', 'room_number', 'floor')
    list_filter = ('hostel', 'room_type', 'status', 'floor')
    ordering = ('hostel', 'room_number')

@admin.register(HostelAllocation)
class HostelAllocationAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'hostel', 'room', 'bed_number', 'allocation_date', 'release_date', 'fee', 'status', 'allocated_by')
    search_fields = ('student__full_name', 'hostel__name', 'room__room_number', 'bed_number')
    list_filter = ('hostel', 'room', 'status', 'allocation_date', 'release_date')
    ordering = ('-allocation_date', 'student')
