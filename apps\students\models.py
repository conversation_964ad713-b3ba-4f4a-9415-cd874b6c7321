from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import date
import uuid
import os

User = get_user_model()

def student_photo_upload_path(instance, filename):
    """Generate upload path for student photos"""
    ext = filename.split('.')[-1]
    filename = f"{instance.index_number}_{uuid.uuid4().hex[:8]}.{ext}"
    return os.path.join('students', 'photos', filename)

def student_document_upload_path(instance, filename):
    """Generate upload path for student documents"""
    ext = filename.split('.')[-1]
    filename = f"{instance.student.index_number}_{instance.document_type}_{uuid.uuid4().hex[:8]}.{ext}"
    return os.path.join('students', 'documents', filename)

class Guardian(models.Model):
    """
    Guardian/Parent information for students
    """
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
    ]

    RELATIONSHIP_CHOICES = [
        ('Father', 'Father'),
        ('Mother', 'Mother'),
        ('Guardian', 'Guardian'),
        ('Grandfather', 'Grandfather'),
        ('Grandmother', 'Grandmother'),
        ('Uncle', 'Uncle'),
        ('Aunt', 'Aunt'),
        ('Brother', 'Brother'),
        ('Sister', 'Sister'),
        ('Other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    full_name = models.CharField(max_length=255)
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES)
    relationship = models.CharField(max_length=50, choices=RELATIONSHIP_CHOICES)
    phone = models.CharField(max_length=20)
    alt_phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    address = models.TextField()
    occupation = models.CharField(max_length=100, null=True, blank=True)
    employer = models.CharField(max_length=100, null=True, blank=True)
    national_id = models.CharField(max_length=20, null=True, blank=True)
    is_primary_contact = models.BooleanField(default=False)
    is_emergency_contact = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Guardian"
        verbose_name_plural = "Guardians"

    def __str__(self):
        return f"{self.full_name} ({self.relationship})"

class Student(models.Model):
    """
    Enhanced student model with comprehensive personal, academic, and contact information.
    """
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
    ]

    NATIONALITY_CHOICES = [
        ('Kenyan', 'Kenyan'),
        ('Other', 'Other'),
    ]

    BOARDING_STATUS_CHOICES = [
        ('Day', 'Day'),
        ('Boarding', 'Boarding'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('transferred', 'Transferred'),
        ('graduated', 'Graduated'),
        ('suspended', 'Suspended'),
        ('expelled', 'Expelled'),
        ('withdrawn', 'Withdrawn'),
    ]

    TRANSFER_STATUS_CHOICES = [
        ('new', 'New Student'),
        ('transfer_in', 'Transfer In'),
        ('transfer_out', 'Transfer Out'),
        ('readmission', 'Readmission'),
    ]

    # Basic Information
    id = models.AutoField(primary_key=True)
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, related_name='student')
    student_id = models.CharField(max_length=20, unique=True, editable=False)  # Auto-generated
    index_number = models.BigIntegerField(unique=True)
    admission_number = models.CharField(max_length=20, unique=True, null=True, blank=True)

    # Personal Details
    first_name = models.CharField(max_length=100, default='')
    middle_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, default='')
    full_name = models.CharField(max_length=255, editable=False)  # Auto-generated
    preferred_name = models.CharField(max_length=100, null=True, blank=True)
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES)
    date_of_birth = models.DateField(default=date.today)
    place_of_birth = models.CharField(max_length=100, null=True, blank=True)
    nationality = models.CharField(max_length=50, choices=NATIONALITY_CHOICES, default='Kenyan')
    religion = models.CharField(max_length=50, null=True, blank=True)

    # Contact Information
    phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    address = models.TextField()
    postal_address = models.CharField(max_length=255, null=True, blank=True)
    postal_code = models.CharField(max_length=10, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    county = models.CharField(max_length=100, null=True, blank=True)

    # Academic Background
    kcpe_index = models.CharField(max_length=50, null=True, blank=True, verbose_name="KCPE Index Number")
    kcpe_score = models.IntegerField(null=True, blank=True, verbose_name="KCPE Score")
    kcpe_year = models.PositiveIntegerField(null=True, blank=True, verbose_name="KCPE Year")
    primary_school = models.CharField(max_length=255, null=True, blank=True)
    previous_school = models.CharField(max_length=255, null=True, blank=True)
    previous_class = models.CharField(max_length=50, null=True, blank=True)

    # Transfer Information
    transfer_status = models.CharField(max_length=50, choices=TRANSFER_STATUS_CHOICES, default='new')
    transfer_date = models.DateField(null=True, blank=True)
    transfer_reason = models.TextField(null=True, blank=True)
    transfer_documents = models.TextField(null=True, blank=True, help_text="List of transfer documents received")

    # Health Information
    health_conditions = models.TextField(null=True, blank=True, help_text="Known medical conditions")
    allergies = models.TextField(null=True, blank=True)
    current_medications = models.TextField(null=True, blank=True, help_text="Current medications")
    blood_group = models.CharField(max_length=5, null=True, blank=True)
    special_needs = models.TextField(null=True, blank=True)

    # School Information
    boarding_status = models.CharField(max_length=20, choices=BOARDING_STATUS_CHOICES, default='Day')
    current_grade = models.ForeignKey('academics.Grade', on_delete=models.SET_NULL, null=True, blank=True)
    current_section = models.CharField(max_length=10, null=True, blank=True)

    # Status and Registration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    admission_date = models.DateField(default=date.today)
    graduation_date = models.DateField(null=True, blank=True)

    # Guardian Information
    guardians = models.ManyToManyField(Guardian, through='StudentGuardian', related_name='students')

    # Photo and Documents
    photo = models.ImageField(upload_to=student_photo_upload_path, null=True, blank=True)

    # Legacy fields (for backward compatibility)
    TSC_number = models.CharField(max_length=255, null=True, blank=True)
    i_name = models.CharField(max_length=255, null=True, blank=True)
    image_name = models.CharField(max_length=255, null=True, blank=True)
    b_date = models.DateField(null=True, blank=True)
    emergency_contact = models.CharField(max_length=255, null=True, blank=True)
    _status = models.CharField(max_length=255, null=True, blank=True)
    reg_year = models.PositiveIntegerField(null=True, blank=True)
    reg_month = models.CharField(max_length=255, null=True, blank=True)
    reg_date = models.DateField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='students_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='students_updated')

    class Meta:
        verbose_name = "Student"
        verbose_name_plural = "Students"
        ordering = ['last_name', 'first_name']

    def save(self, *args, **kwargs):
        # Auto-generate full name
        if self.middle_name:
            self.full_name = f"{self.first_name} {self.middle_name} {self.last_name}"
        else:
            self.full_name = f"{self.first_name} {self.last_name}"

        # Auto-generate student ID if not exists
        if not self.student_id:
            self.student_id = self.generate_student_id()

        # Sync legacy fields for backward compatibility
        if not self.b_date and self.date_of_birth:
            self.b_date = self.date_of_birth
        if not self.reg_date and self.admission_date:
            self.reg_date = self.admission_date
        if not self._status:
            self._status = self.status
        if not self.reg_year and self.admission_date:
            self.reg_year = self.admission_date.year
        if not self.reg_month and self.admission_date:
            self.reg_month = self.admission_date.strftime('%B')

        super().save(*args, **kwargs)

    def generate_student_id(self):
        """Generate unique student ID"""
        from datetime import datetime
        year = datetime.now().year
        # Get the last student ID for this year
        last_student = Student.objects.filter(
            student_id__startswith=f"STU{year}"
        ).order_by('student_id').last()

        if last_student:
            last_number = int(last_student.student_id[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"STU{year}{new_number:04d}"

    def get_full_name(self):
        """Return full name"""
        return self.full_name

    def get_age(self):
        """Calculate student's age"""
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))

    def get_primary_guardian(self):
        """Get primary guardian"""
        primary = self.studentguardian_set.filter(guardian__is_primary_contact=True).first()
        return primary.guardian if primary else None

    def get_emergency_contact(self):
        """Get emergency contact guardian"""
        emergency = self.studentguardian_set.filter(guardian__is_emergency_contact=True).first()
        return emergency.guardian if emergency else None

    def __str__(self):
        return f"{self.student_id} - {self.full_name}"

class StudentGuardian(models.Model):
    """
    Through model for Student-Guardian relationship
    """
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    guardian = models.ForeignKey(Guardian, on_delete=models.CASCADE)
    relationship_notes = models.TextField(null=True, blank=True)
    is_authorized_pickup = models.BooleanField(default=True)
    is_fee_payer = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['student', 'guardian']
        verbose_name = "Student Guardian"
        verbose_name_plural = "Student Guardians"

    def __str__(self):
        return f"{self.student.full_name} - {self.guardian.full_name}"

class StudentDocument(models.Model):
    """
    Student documents and certificates
    """
    DOCUMENT_TYPE_CHOICES = [
        ('birth_certificate', 'Birth Certificate'),
        ('kcpe_certificate', 'KCPE Certificate'),
        ('transfer_letter', 'Transfer Letter'),
        ('medical_report', 'Medical Report'),
        ('passport_photo', 'Passport Photo'),
        ('immunization_card', 'Immunization Card'),
        ('other', 'Other'),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPE_CHOICES)
    document_name = models.CharField(max_length=255)
    document_file = models.FileField(upload_to=student_document_upload_path)
    description = models.TextField(null=True, blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = "Student Document"
        verbose_name_plural = "Student Documents"

    def __str__(self):
        return f"{self.student.full_name} - {self.document_name}"

class StudentGrade(models.Model):
    """
    Enhanced grade/class assignment tracking for students with comprehensive status management.
    """
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('promoted', 'Promoted'),
        ('repeated', 'Repeated'),
        ('transferred', 'Transferred'),
        ('graduated', 'Graduated'),
        ('withdrawn', 'Withdrawn'),
    ]

    ASSIGNMENT_TYPE_CHOICES = [
        ('new_admission', 'New Admission'),
        ('promotion', 'Promotion'),
        ('transfer', 'Transfer'),
        ('repeat', 'Repeat'),
        ('readmission', 'Readmission'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='grade_assignments')
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    section = models.CharField(max_length=50, null=True, blank=True)
    academic_year = models.PositiveIntegerField()
    term = models.IntegerField(default=1)
    assignment_type = models.CharField(max_length=20, choices=ASSIGNMENT_TYPE_CHOICES, default='new_admission')
    assignment_date = models.DateField()
    status = models.CharField(max_length=20, default='active', choices=STATUS_CHOICES)
    is_current = models.BooleanField(default=True)

    # Performance tracking
    term_average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    class_position = models.PositiveIntegerField(null=True, blank=True)
    total_students = models.PositiveIntegerField(null=True, blank=True)

    # Promotion/Graduation details
    promotion_date = models.DateField(null=True, blank=True)
    promotion_reason = models.TextField(null=True, blank=True)
    next_grade = models.ForeignKey('academics.Grade', on_delete=models.SET_NULL, null=True, blank=True, related_name='promoted_from')

    # Legacy field for backward compatibility
    index_number = models.BigIntegerField(null=True, blank=True)
    year = models.PositiveIntegerField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = "Student Grade Assignment"
        verbose_name_plural = "Student Grade Assignments"
        unique_together = ['student', 'grade', 'academic_year', 'term']
        ordering = ['-academic_year', '-term', 'grade__name']

    def save(self, *args, **kwargs):
        # Sync legacy fields
        if not self.index_number and self.student:
            self.index_number = self.student.index_number
        if not self.year:
            self.year = self.academic_year

        # Ensure only one current assignment per student
        if self.is_current:
            StudentGrade.objects.filter(student=self.student, is_current=True).exclude(pk=self.pk).update(is_current=False)

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.student.full_name} - {self.grade.name} ({self.academic_year})"

class StudentSubject(models.Model):
    """
    Enhanced subject registration and tracking for students.
    """
    REGISTRATION_STATUS_CHOICES = [
        ('registered', 'Registered'),
        ('dropped', 'Dropped'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('transferred', 'Transferred'),
    ]

    SUBJECT_TYPE_CHOICES = [
        ('compulsory', 'Compulsory'),
        ('elective', 'Elective'),
        ('optional', 'Optional'),
        ('remedial', 'Remedial'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='subject_assignments')
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    academic_year = models.PositiveIntegerField()
    term = models.IntegerField(default=1)
    subject_type = models.CharField(max_length=20, choices=SUBJECT_TYPE_CHOICES, default='compulsory')
    registration_date = models.DateField()
    registration_status = models.CharField(max_length=20, choices=REGISTRATION_STATUS_CHOICES, default='registered')

    # Performance tracking
    current_average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    highest_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    lowest_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    # Teacher assignment
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.SET_NULL, null=True, blank=True)

    # Legacy fields for backward compatibility
    index_number = models.BigIntegerField(null=True, blank=True)
    _status = models.CharField(max_length=255, null=True, blank=True)
    sr = models.ForeignKey('academics.SubjectRouting', on_delete=models.CASCADE, null=True, blank=True)
    year = models.IntegerField(null=True, blank=True)
    reg_month = models.CharField(max_length=255, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = "Student Subject Assignment"
        verbose_name_plural = "Student Subject Assignments"
        unique_together = ['student', 'subject', 'academic_year', 'term']
        ordering = ['academic_year', 'term', 'subject__name']

    def save(self, *args, **kwargs):
        # Sync legacy fields
        if not self.index_number and self.student:
            self.index_number = self.student.index_number
        if not self._status:
            self._status = self.registration_status
        if not self.year:
            self.year = self.academic_year
        if not self.reg_month and self.registration_date:
            self.reg_month = self.registration_date.strftime('%B')

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.student.full_name} - {self.subject.name} ({self.academic_year})"

class StudentStatusHistory(models.Model):
    """
    Track student status changes over time
    """
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='status_history')
    previous_status = models.CharField(max_length=20, choices=Student.STATUS_CHOICES)
    new_status = models.CharField(max_length=20, choices=Student.STATUS_CHOICES)
    change_date = models.DateField()
    reason = models.TextField()
    supporting_documents = models.TextField(null=True, blank=True, help_text="List of supporting documents")
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_status_changes')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Student Status History"
        verbose_name_plural = "Student Status Histories"
        ordering = ['-change_date']

    def __str__(self):
        return f"{self.student.full_name}: {self.previous_status} → {self.new_status}"

class StudentPromotion(models.Model):
    """
    Track student promotions and academic progression
    """
    PROMOTION_TYPE_CHOICES = [
        ('automatic', 'Automatic Promotion'),
        ('conditional', 'Conditional Promotion'),
        ('merit', 'Merit Promotion'),
        ('repeat', 'Repeat Class'),
        ('skip', 'Skip Grade'),
    ]

    PROMOTION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='promotions')
    from_grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, related_name='promoted_from_grade')
    to_grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, related_name='promoted_to_grade')
    academic_year = models.PositiveIntegerField()
    promotion_type = models.CharField(max_length=20, choices=PROMOTION_TYPE_CHOICES)
    promotion_status = models.CharField(max_length=20, choices=PROMOTION_STATUS_CHOICES, default='pending')

    # Academic performance
    overall_average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    class_position = models.PositiveIntegerField(null=True, blank=True)
    total_students = models.PositiveIntegerField(null=True, blank=True)

    # Promotion criteria
    meets_academic_requirements = models.BooleanField(default=False)
    meets_attendance_requirements = models.BooleanField(default=False)
    meets_discipline_requirements = models.BooleanField(default=False)

    # Dates and approval
    promotion_date = models.DateField()
    effective_date = models.DateField(null=True, blank=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    approval_date = models.DateField(null=True, blank=True)

    # Additional information
    conditions = models.TextField(null=True, blank=True, help_text="Conditions for conditional promotion")
    remarks = models.TextField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='promotions_created')

    class Meta:
        verbose_name = "Student Promotion"
        verbose_name_plural = "Student Promotions"
        unique_together = ['student', 'academic_year']
        ordering = ['-academic_year', 'student__last_name']

    def __str__(self):
        return f"{self.student.full_name}: {self.from_grade.name} → {self.to_grade.name} ({self.academic_year})"

class StudentGraduation(models.Model):
    """
    Track student graduations and completion
    """
    GRADUATION_STATUS_CHOICES = [
        ('eligible', 'Eligible'),
        ('graduated', 'Graduated'),
        ('deferred', 'Deferred'),
        ('incomplete', 'Incomplete'),
    ]

    GRADUATION_TYPE_CHOICES = [
        ('regular', 'Regular Graduation'),
        ('early', 'Early Graduation'),
        ('delayed', 'Delayed Graduation'),
        ('certificate', 'Certificate Completion'),
    ]

    student = models.OneToOneField(Student, on_delete=models.CASCADE, related_name='graduation')
    graduation_year = models.PositiveIntegerField()
    graduation_date = models.DateField()
    graduation_type = models.CharField(max_length=20, choices=GRADUATION_TYPE_CHOICES, default='regular')
    graduation_status = models.CharField(max_length=20, choices=GRADUATION_STATUS_CHOICES, default='eligible')

    # Academic performance
    final_average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    final_grade = models.CharField(max_length=5, null=True, blank=True)
    class_position = models.PositiveIntegerField(null=True, blank=True)
    total_graduates = models.PositiveIntegerField(null=True, blank=True)

    # Graduation requirements
    completed_all_subjects = models.BooleanField(default=False)
    meets_attendance_requirements = models.BooleanField(default=False)
    cleared_all_fees = models.BooleanField(default=False)
    returned_all_books = models.BooleanField(default=False)

    # Certificates and awards
    certificate_number = models.CharField(max_length=50, unique=True, null=True, blank=True)
    awards = models.TextField(null=True, blank=True, help_text="Awards and recognitions received")

    # Next destination
    next_school = models.CharField(max_length=255, null=True, blank=True)
    next_level = models.CharField(max_length=100, null=True, blank=True)
    career_path = models.CharField(max_length=100, null=True, blank=True)

    # Approval and processing
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='graduations_approved')
    approval_date = models.DateField(null=True, blank=True)
    certificate_issued = models.BooleanField(default=False)
    certificate_issue_date = models.DateField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='graduations_created')

    class Meta:
        verbose_name = "Student Graduation"
        verbose_name_plural = "Student Graduations"
        ordering = ['-graduation_year', 'student__last_name']

    def save(self, *args, **kwargs):
        # Auto-generate certificate number
        if not self.certificate_number and self.graduation_status == 'graduated':
            self.certificate_number = self.generate_certificate_number()
        super().save(*args, **kwargs)

    def generate_certificate_number(self):
        """Generate unique certificate number"""
        year = self.graduation_year
        last_cert = StudentGraduation.objects.filter(
            certificate_number__startswith=f"CERT{year}"
        ).order_by('certificate_number').last()

        if last_cert:
            last_number = int(last_cert.certificate_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"CERT{year}{new_number:04d}"

    def __str__(self):
        return f"{self.student.full_name} - Graduation {self.graduation_year}"
