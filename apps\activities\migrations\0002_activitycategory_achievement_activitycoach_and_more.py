# Generated by Django 5.2.1 on 2025-06-09 09:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("activities", "0001_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ActivityCategory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "category_type",
                    models.CharField(
                        choices=[
                            ("sports", "Sports"),
                            ("music", "Music"),
                            ("drama", "Drama & Theatre"),
                            ("clubs", "Clubs & Societies"),
                            ("academic", "Academic Competitions"),
                            ("community", "Community Service"),
                            ("arts", "Arts & Crafts"),
                            ("technology", "Technology & Innovation"),
                            ("debate", "Debate & Public Speaking"),
                            ("cultural", "Cultural Activities"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="CSS icon class", max_length=50
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#3B82F6", help_text="Hex color code", max_length=7
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "Activity Categories",
            },
        ),
        migrations.CreateModel(
            name="Achievement",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=200)),
                (
                    "achievement_type",
                    models.CharField(
                        choices=[
                            ("participation", "Participation"),
                            ("competition", "Competition"),
                            ("leadership", "Leadership"),
                            ("skill", "Skill Development"),
                            ("service", "Community Service"),
                            ("award", "Award/Recognition"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("school", "School Level"),
                            ("district", "District Level"),
                            ("regional", "Regional Level"),
                            ("national", "National Level"),
                            ("international", "International Level"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("date_achieved", models.DateField()),
                (
                    "position",
                    models.CharField(
                        blank=True,
                        help_text="e.g., 1st Place, Gold Medal",
                        max_length=50,
                    ),
                ),
                ("certificate_number", models.CharField(blank=True, max_length=100)),
                ("awarded_by", models.CharField(blank=True, max_length=200)),
                (
                    "points_earned",
                    models.IntegerField(
                        default=0, help_text="Points for school grading system"
                    ),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "student_activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="achievement_records",
                        to="activities.studentactivity",
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ActivityCoach",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "external_name",
                    models.CharField(
                        blank=True, help_text="For external coaches", max_length=200
                    ),
                ),
                ("external_contact", models.CharField(blank=True, max_length=20)),
                ("external_email", models.EmailField(blank=True, max_length=254)),
                (
                    "coach_type",
                    models.CharField(
                        choices=[
                            ("head_coach", "Head Coach"),
                            ("assistant_coach", "Assistant Coach"),
                            ("teacher_in_charge", "Teacher in Charge"),
                            ("external_coach", "External Coach"),
                            ("volunteer", "Volunteer"),
                        ],
                        max_length=20,
                    ),
                ),
                ("specialization", models.CharField(blank=True, max_length=200)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("qualifications", models.TextField(blank=True)),
                ("experience_years", models.IntegerField(default=0)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="coaches",
                        to="activities.cocurricularactivity",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ActivityFeeStructure",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("registration", "Registration Fee"),
                            ("monthly", "Monthly Fee"),
                            ("term", "Term Fee"),
                            ("annual", "Annual Fee"),
                            ("equipment", "Equipment Fee"),
                            ("competition", "Competition Fee"),
                            ("uniform", "Uniform Fee"),
                            ("transport", "Transport Fee"),
                        ],
                        max_length=20,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("description", models.TextField(blank=True)),
                ("is_mandatory", models.BooleanField(default=True)),
                ("due_date", models.DateField(blank=True, null=True)),
                ("academic_year", models.CharField(max_length=20)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fee_structures",
                        to="activities.cocurricularactivity",
                    ),
                ),
            ],
            options={
                "unique_together": {("activity", "fee_type", "academic_year")},
            },
        ),
        migrations.CreateModel(
            name="ActivityFeePayment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("cash", "Cash"),
                            ("mpesa", "M-Pesa"),
                            ("bank_transfer", "Bank Transfer"),
                            ("cheque", "Cheque"),
                            ("card", "Credit/Debit Card"),
                        ],
                        max_length=20,
                    ),
                ),
                ("payment_date", models.DateTimeField(auto_now_add=True)),
                ("reference_number", models.CharField(blank=True, max_length=100)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                        ],
                        default="completed",
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "processed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student_activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fee_payments",
                        to="activities.studentactivity",
                    ),
                ),
                (
                    "fee_structure",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="activities.activityfeestructure",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ActivityFeeDiscount",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "discount_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage"),
                            ("fixed_amount", "Fixed Amount"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "discount_value",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("scholarship", "Scholarship"),
                            ("financial_hardship", "Financial Hardship"),
                            ("sibling_discount", "Sibling Discount"),
                            ("staff_child", "Staff Child"),
                            ("academic_excellence", "Academic Excellence"),
                            ("loyalty", "Loyalty Discount"),
                            ("early_bird", "Early Bird Discount"),
                            ("other", "Other"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student_activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fee_discounts",
                        to="activities.studentactivity",
                    ),
                ),
                (
                    "fee_structure",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="activities.activityfeestructure",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ActivitySchedule",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "day_of_week",
                    models.CharField(
                        choices=[
                            ("monday", "Monday"),
                            ("tuesday", "Tuesday"),
                            ("wednesday", "Wednesday"),
                            ("thursday", "Thursday"),
                            ("friday", "Friday"),
                            ("saturday", "Saturday"),
                            ("sunday", "Sunday"),
                        ],
                        max_length=10,
                    ),
                ),
                ("start_time", models.TimeField()),
                ("end_time", models.TimeField()),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("biweekly", "Bi-weekly"),
                            ("monthly", "Monthly"),
                            ("irregular", "Irregular"),
                        ],
                        default="weekly",
                        max_length=20,
                    ),
                ),
                ("venue", models.CharField(blank=True, max_length=200)),
                ("max_participants", models.IntegerField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedules",
                        to="activities.cocurricularactivity",
                    ),
                ),
            ],
            options={
                "unique_together": {("activity", "day_of_week", "start_time")},
            },
        ),
    ]
