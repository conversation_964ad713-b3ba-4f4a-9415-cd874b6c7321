from django.contrib import admin
from .models import LibraryBook, LibraryCategory, LibraryBorrowing

@admin.register(LibraryCategory)
class LibraryCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description')
    search_fields = ('name',)
    ordering = ('name',)

@admin.register(LibraryBook)
class LibraryBookAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'author', 'isbn', 'category', 'total_copies', 'available_copies', 'status', 'date_added')
    search_fields = ('title', 'author', 'isbn')
    list_filter = ('category', 'status', 'publication_year', 'date_added')
    ordering = ('-date_added', 'title')
    readonly_fields = ('date_added',)

    fieldsets = (
        ('Book Information', {
            'fields': ('title', 'author', 'isbn', 'category', 'publisher', 'publication_year', 'edition', 'pages')
        }),
        ('Library Details', {
            'fields': ('total_copies', 'available_copies', 'shelf_location', 'status')
        }),
        ('Additional Information', {
            'fields': ('description', 'cover_image', 'date_added', 'added_by')
        }),
    )

@admin.register(LibraryBorrowing)
class LibraryBorrowingAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'book', 'issue_date', 'due_date', 'return_date', 'status', 'fine_amount')
    search_fields = ('student__full_name', 'book__title', 'student__index_number')
    list_filter = ('status', 'issue_date', 'due_date', 'return_date')
    ordering = ('-issue_date',)
    readonly_fields = ('issue_date', 'fine_amount')

    fieldsets = (
        ('Borrowing Information', {
            'fields': ('student', 'book', 'issue_date', 'due_date', 'return_date')
        }),
        ('Status & Fines', {
            'fields': ('status', 'fine_amount', 'fine_paid')
        }),
        ('Additional Details', {
            'fields': ('notes', 'issued_by', 'returned_to')
        }),
    )
