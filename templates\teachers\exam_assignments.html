{% extends 'teachers/base.html' %}
{% load static %}

{% block title %}Exam Assignments - {{ teacher.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
    .assignment-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .assignment-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .chief-assignment {
        border-left-color: #28a745;
    }
    .assistant-assignment {
        border-left-color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Exam Assignments</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'teachers:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'teachers:exam_dashboard' %}">Exam Dashboard</a></li>
                        <li class="breadcrumb-item active">Assignments</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{% url 'teachers:exam_assignments' %}" class="btn btn-secondary">Clear</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Chief Invigilator Assignments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Chief Invigilator Assignments</h4>
                </div>
                <div class="card-body">
                    {% if chief_page_obj %}
                        <div class="table-responsive">
                            <table class="table table-nowrap align-middle">
                                <thead>
                                    <tr>
                                        <th>Exam</th>
                                        <th>Subject</th>
                                        <th>Grade</th>
                                        <th>Date & Time</th>
                                        <th>Classroom</th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for assignment in chief_page_obj %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">{{ assignment.exam.name }}</h6>
                                                <p class="text-muted mb-0 font-size-12">{{ assignment.exam.exam_type.name }}</p>
                                            </div>
                                        </td>
                                        <td>{{ assignment.subject.name }}</td>
                                        <td>{{ assignment.grade.name }}</td>
                                        <td>
                                            <div>
                                                <p class="mb-1">{{ assignment.exam_date|date:"M d, Y" }}</p>
                                                <p class="text-muted mb-0 font-size-12">
                                                    {{ assignment.start_time|time:"H:i" }} - {{ assignment.end_time|time:"H:i" }}
                                                </p>
                                            </div>
                                        </td>
                                        <td>{{ assignment.classroom.name|default:"TBA" }}</td>
                                        <td>{{ assignment.duration }} mins</td>
                                        <td>
                                            <span class="badge bg-{% if assignment.status == 'completed' %}success{% elif assignment.status == 'active' %}warning{% elif assignment.status == 'scheduled' %}info{% else %}secondary{% endif %}">
                                                {{ assignment.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-2-fill"></i>
                                                </a>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{% url 'exams:exam_detail' assignment.exam.id %}">View Exam</a></li>
                                                    {% if assignment.status == 'scheduled' or assignment.status == 'active' %}
                                                    <li><a class="dropdown-item" href="{% url 'exams:result_entry' assignment.id %}">Enter Results</a></li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination for Chief Assignments -->
                        {% if chief_page_obj.has_other_pages %}
                        <nav aria-label="Chief assignments pagination">
                            <ul class="pagination justify-content-center">
                                {% if chief_page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?chief_page={{ chief_page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                                </li>
                                {% endif %}
                                
                                {% for num in chief_page_obj.paginator.page_range %}
                                {% if chief_page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > chief_page_obj.number|add:'-3' and num < chief_page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?chief_page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}
                                
                                {% if chief_page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?chief_page={{ chief_page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-shield-check-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No chief invigilator assignments found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Assistant Invigilator Assignments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Assistant Invigilator Assignments</h4>
                </div>
                <div class="card-body">
                    {% if assistant_page_obj %}
                        <div class="table-responsive">
                            <table class="table table-nowrap align-middle">
                                <thead>
                                    <tr>
                                        <th>Exam</th>
                                        <th>Subject</th>
                                        <th>Grade</th>
                                        <th>Date & Time</th>
                                        <th>Classroom</th>
                                        <th>Chief Invigilator</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for assignment in assistant_page_obj %}
                                    <tr>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">{{ assignment.exam.name }}</h6>
                                                <p class="text-muted mb-0 font-size-12">{{ assignment.exam.exam_type.name }}</p>
                                            </div>
                                        </td>
                                        <td>{{ assignment.subject.name }}</td>
                                        <td>{{ assignment.grade.name }}</td>
                                        <td>
                                            <div>
                                                <p class="mb-1">{{ assignment.exam_date|date:"M d, Y" }}</p>
                                                <p class="text-muted mb-0 font-size-12">
                                                    {{ assignment.start_time|time:"H:i" }} - {{ assignment.end_time|time:"H:i" }}
                                                </p>
                                            </div>
                                        </td>
                                        <td>{{ assignment.classroom.name|default:"TBA" }}</td>
                                        <td>{{ assignment.chief_invigilator.get_full_name|default:"TBA" }}</td>
                                        <td>
                                            <span class="badge bg-{% if assignment.status == 'completed' %}success{% elif assignment.status == 'active' %}warning{% elif assignment.status == 'scheduled' %}info{% else %}secondary{% endif %}">
                                                {{ assignment.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-2-fill"></i>
                                                </a>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{% url 'exams:exam_detail' assignment.exam.id %}">View Exam</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination for Assistant Assignments -->
                        {% if assistant_page_obj.has_other_pages %}
                        <nav aria-label="Assistant assignments pagination">
                            <ul class="pagination justify-content-center">
                                {% if assistant_page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?assistant_page={{ assistant_page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                                </li>
                                {% endif %}
                                
                                {% for num in assistant_page_obj.paginator.page_range %}
                                {% if assistant_page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > assistant_page_obj.number|add:'-3' and num < assistant_page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?assistant_page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}
                                
                                {% if assistant_page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?assistant_page={{ assistant_page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-shield-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No assistant invigilator assignments found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh every 5 minutes for real-time updates
    setInterval(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
