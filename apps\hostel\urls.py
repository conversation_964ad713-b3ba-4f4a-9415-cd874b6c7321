from django.urls import path
from . import views

app_name = 'hostel'

urlpatterns = [
    # Dashboard
    path('', views.hostel_dashboard, name='dashboard'),
    
    # Hostel Management
    path('hostels/', views.hostel_list, name='hostel_list'),
    path('hostels/<int:hostel_id>/', views.hostel_detail, name='hostel_detail'),
    path('hostels/add/', views.add_hostel, name='add_hostel'),
    path('hostels/<int:hostel_id>/edit/', views.edit_hostel, name='edit_hostel'),
    
    # Room Management
    path('hostels/<int:hostel_id>/rooms/', views.room_list, name='room_list'),
    path('rooms/<int:room_id>/', views.room_detail, name='room_detail'),
    path('hostels/<int:hostel_id>/rooms/add/', views.add_room, name='add_room'),
    
    # Student Allocation Management
    path('allocations/', views.allocation_list, name='allocation_list'),
    path('allocations/<int:allocation_id>/', views.allocation_detail, name='allocation_detail'),
    path('allocations/add/', views.add_allocation, name='add_allocation'),
    path('allocations/<int:allocation_id>/edit/', views.edit_allocation, name='edit_allocation'),
    path('allocations/<int:allocation_id>/release/', views.release_allocation, name='release_allocation'),

    # Reports
    path('reports/', views.hostel_reports, name='reports'),
]
