from django.urls import path
from .views import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Admin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Teacher<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    StudentRegisterView, ParentRegisterView, AccountantRegisterView,
    parent_child_relationships, add_parent_child_relationship,
    edit_parent_child_relationship, unlock_account, login_attempts_log,
    bulk_parent_child_link
)
from django.contrib.auth import views as auth_views

app_name = 'accounts'

urlpatterns = [
    path('login/', LoginView.as_view(), name='login'),
    path('register/', StudentRegisterView.as_view(), name='register'),  # Default registration
    path('register/admin/', AdminRegisterView.as_view(), name='register_admin'),
    path('register/teacher/', TeacherRegisterView.as_view(), name='register_teacher'),
    path('register/student/', StudentRegisterView.as_view(), name='register_student'),
    path('register/parent/', ParentRegisterView.as_view(), name='register_parent'),
    path('register/accountant/', AccountantRegisterView.as_view(), name='register_accountant'),
    path('password-reset/', auth_views.PasswordResetView.as_view(
        template_name='accounts/password_reset_form.html'
    ), name='password_reset'),
    path('password-reset/done/', auth_views.PasswordResetDoneView.as_view(
        template_name='accounts/password_reset_done.html'
    ), name='password_reset_done'),
    path('reset/<uidb64>/<token>/', auth_views.PasswordResetConfirmView.as_view(
        template_name='accounts/password_reset_confirm.html'
    ), name='password_reset_confirm'),
    path('reset/done/', auth_views.PasswordResetCompleteView.as_view(
        template_name='accounts/password_reset_complete.html'
    ), name='password_reset_complete'),
    path('logout/', auth_views.LogoutView.as_view(next_page='/'), name='logout'),

    # Parent-Child Relationship Management
    path('parent-child-relationships/', parent_child_relationships, name='parent_child_relationships'),
    path('parent-child-relationships/add/', add_parent_child_relationship, name='add_parent_child_relationship'),
    path('parent-child-relationships/edit/<int:pk>/', edit_parent_child_relationship, name='edit_parent_child_relationship'),
    path('parent-child-relationships/bulk-link/', bulk_parent_child_link, name='bulk_parent_child_link'),

    # Account Security Management
    path('unlock-account/', unlock_account, name='unlock_account'),
    path('login-attempts/', login_attempts_log, name='login_attempts_log'),
]