from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
import csv

from .models import (
    AcademicSyllabus, Assignment, StudentAssignment, StudyMaterial,
    Curriculum, SyllabusUnit, LearningMaterial, TermPlan,
    SyllabusProgress, AssignmentCategory, SyllabusReport
)
from .forms import (
    AcademicSyllabusForm, CurriculumForm, SyllabusUnitForm,
    LearningMaterialForm, AssignmentForm, TermPlanForm,
    SyllabusProgressForm, SyllabusSearchForm
)
from apps.academics.models import Subject, Grade
from .models import AcademicYear, Term
from apps.students.models import Student

@login_required
def syllabus_dashboard(request):
    """Syllabus management dashboard with statistics and recent activities."""
    context = {
        'total_syllabi': AcademicSyllabus.objects.count(),
        'active_syllabi': AcademicSyllabus.objects.filter(status='active').count(),
        'total_curricula': Curriculum.objects.count(),
        'total_units': SyllabusUnit.objects.count(),
        'total_materials': LearningMaterial.objects.count(),
        'total_assignments': Assignment.objects.count(),
        'pending_assignments': StudentAssignment.objects.filter(status='pending').count(),
        'completed_assignments': StudentAssignment.objects.filter(status='completed').count(),
        'recent_syllabi': AcademicSyllabus.objects.select_related(
            'subject', 'grade'
        ).order_by('-upload_date')[:10],
        'recent_assignments': Assignment.objects.select_related(
            'subject', 'grade', 'teacher'
        ).order_by('-assign_date')[:10],
        'subject_distribution': AcademicSyllabus.objects.values('subject__name').annotate(
            count=Count('id')
        ).order_by('-count')[:5],
        'grade_distribution': AcademicSyllabus.objects.values('grade__name').annotate(
            count=Count('id')
        ).order_by('-count')[:5],
    }
    return render(request, 'syllabus/dashboard.html', context)

# Syllabus Management Views
@login_required
def syllabus_list(request):
    """List all syllabi with search and filter functionality."""
    form = SyllabusSearchForm(request.GET)
    syllabi = AcademicSyllabus.objects.select_related(
        'subject', 'grade', 'teacher'
    ).annotate(
        unit_count=Count('units'),
        material_count=Count('learning_materials')
    ).all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        subject = form.cleaned_data.get('subject')
        grade = form.cleaned_data.get('grade')
        academic_year = form.cleaned_data.get('academic_year')
        status = form.cleaned_data.get('status')

        if search_query:
            syllabi = syllabi.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(subject__name__icontains=search_query) |
                Q(grade__name__icontains=search_query)
            )

        if subject:
            syllabi = syllabi.filter(subject=subject)

        if grade:
            syllabi = syllabi.filter(grade=grade)

        if academic_year:
            syllabi = syllabi.filter(academic_year=academic_year)

        if status:
            syllabi = syllabi.filter(status=status)

    syllabi = syllabi.order_by('-upload_date')

    paginator = Paginator(syllabi, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'syllabi': page_obj,
    }
    return render(request, 'syllabus/syllabus_list.html', context)

@login_required
def syllabus_detail(request, syllabus_id):
    """Display detailed information about a syllabus."""
    syllabus = get_object_or_404(AcademicSyllabus, id=syllabus_id)
    units = SyllabusUnit.objects.filter(syllabus=syllabus).order_by('order', 'unit_number')
    materials = LearningMaterial.objects.filter(syllabus=syllabus).order_by('-created_at')
    assignments = Assignment.objects.filter(syllabus=syllabus).order_by('-assign_date')
    term_plans = TermPlan.objects.filter(syllabus=syllabus).order_by('-created_at')

    context = {
        'syllabus': syllabus,
        'units': units,
        'materials': materials,
        'assignments': assignments,
        'term_plans': term_plans,
        'total_units': units.count(),
        'total_materials': materials.count(),
        'total_assignments': assignments.count(),
    }
    return render(request, 'syllabus/syllabus_detail.html', context)

@login_required
def add_syllabus(request):
    """Add a new academic syllabus."""
    if request.method == 'POST':
        form = AcademicSyllabusForm(request.POST)
        if form.is_valid():
            syllabus = form.save()
            messages.success(request, f'Syllabus "{syllabus.title}" has been added successfully.')
            return redirect('syllabus:syllabus_detail', syllabus_id=syllabus.id)
    else:
        form = AcademicSyllabusForm()

    context = {'form': form}
    return render(request, 'syllabus/add_syllabus.html', context)

@login_required
def edit_syllabus(request, syllabus_id):
    """Edit an existing syllabus."""
    syllabus = get_object_or_404(AcademicSyllabus, id=syllabus_id)

    if request.method == 'POST':
        form = AcademicSyllabusForm(request.POST, instance=syllabus)
        if form.is_valid():
            form.save()
            messages.success(request, f'Syllabus "{syllabus.title}" has been updated successfully.')
            return redirect('syllabus:syllabus_detail', syllabus_id=syllabus.id)
    else:
        form = AcademicSyllabusForm(instance=syllabus)

    context = {'form': form, 'syllabus': syllabus}
    return render(request, 'syllabus/edit_syllabus.html', context)
