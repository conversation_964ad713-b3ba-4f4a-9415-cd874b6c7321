from django.db import models
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from django.utils import timezone
from django.core.files.storage import default_storage

# Create your models here.

class AcademicSyllabus(models.Model):
    SYLLABUS_STATUSES = [
        ('active', 'Active'),
        ('archived', 'Archived'),
    ]
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE)
    file_name = models.CharField(max_length=255, null=True, blank=True)
    file_type = models.CharField(max_length=50, null=True, blank=True)
    file_size = models.IntegerField(null=True, blank=True)
    year = models.PositiveIntegerField()
    term = models.IntegerField()
    upload_date = models.DateField()
    status = models.CharField(max_length=50, default='active', choices=SYLLABUS_STATUSES)

    def __str__(self):
        return self.title

class Assignment(models.Model):
    ASSIGNMENT_STATUSES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    ]
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE)
    file_name = models.CharField(max_length=255, null=True, blank=True)
    file_type = models.CharField(max_length=50, null=True, blank=True)
    file_size = models.IntegerField(null=True, blank=True)
    assign_date = models.DateField()
    due_date = models.DateField()
    total_marks = models.IntegerField()
    status = models.CharField(max_length=50, default='active', choices=ASSIGNMENT_STATUSES)
    year = models.PositiveIntegerField()
    term = models.IntegerField()

    def __str__(self):
        return self.title

class StudentAssignment(models.Model):
    SUBMISSION_STATUSES = [
        ('pending', 'Pending'),
        ('submitted', 'Submitted'),
        ('graded', 'Graded'),
        ('late', 'Late'),
    ]
    id = models.AutoField(primary_key=True)
    assignment = models.ForeignKey(Assignment, on_delete=models.CASCADE)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    submission_date = models.DateTimeField(null=True, blank=True)
    file_name = models.CharField(max_length=255, null=True, blank=True)
    file_type = models.CharField(max_length=50, null=True, blank=True)
    file_size = models.IntegerField(null=True, blank=True)
    marks = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)
    comments = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, default='pending', choices=SUBMISSION_STATUSES)
    graded_by = models.IntegerField(null=True, blank=True)
    graded_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Student Assignment - {self.id}"

class StudyMaterial(models.Model):
    MATERIAL_TYPES = [
        ('notes', 'Notes'),
        ('presentation', 'Presentation'),
        ('video', 'Video'),
        ('link', 'Link'),
    ]
    MATERIAL_STATUSES = [
        ('active', 'Active'),
        ('archived', 'Archived'),
    ]
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE)
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE)
    file_name = models.CharField(max_length=255, null=True, blank=True)
    file_type = models.CharField(max_length=50, null=True, blank=True)
    file_size = models.IntegerField(null=True, blank=True)
    material_type = models.CharField(max_length=50, choices=MATERIAL_TYPES)
    url = models.CharField(max_length=255, null=True, blank=True)
    upload_date = models.DateField()
    year = models.PositiveIntegerField()
    term = models.IntegerField()
    status = models.CharField(max_length=50, default='active', choices=MATERIAL_STATUSES)

    def __str__(self):
        return self.title

# Enhanced Models for Complete Syllabus Management

class Curriculum(models.Model):
    """Enhanced curriculum framework management"""
    CURRICULUM_TYPES = [
        ('national', 'National Curriculum'),
        ('international', 'International Curriculum'),
        ('local', 'Local Curriculum'),
        ('custom', 'Custom Curriculum'),
    ]

    CURRICULUM_STATUSES = [
        ('active', 'Active'),
        ('draft', 'Draft'),
        ('archived', 'Archived'),
        ('under_review', 'Under Review'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=50, unique=True)
    curriculum_type = models.CharField(max_length=20, choices=CURRICULUM_TYPES)
    description = models.TextField()
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE)
    grade_levels = models.ManyToManyField('academics.Grade', related_name='curricula')
    subjects = models.ManyToManyField('academics.Subject', related_name='curricula')
    total_hours = models.IntegerField(help_text="Total curriculum hours")
    status = models.CharField(max_length=20, choices=CURRICULUM_STATUSES, default='draft')
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_curricula')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Curricula"

    def __str__(self):
        return f"{self.name} ({self.code})"

class SyllabusUnit(models.Model):
    """Detailed syllabus units/chapters management"""
    UNIT_TYPES = [
        ('chapter', 'Chapter'),
        ('topic', 'Topic'),
        ('module', 'Module'),
        ('unit', 'Unit'),
        ('lesson', 'Lesson'),
    ]

    id = models.AutoField(primary_key=True)
    syllabus = models.ForeignKey(AcademicSyllabus, on_delete=models.CASCADE, related_name='units')
    unit_number = models.IntegerField()
    title = models.CharField(max_length=200)
    unit_type = models.CharField(max_length=20, choices=UNIT_TYPES, default='chapter')
    description = models.TextField()
    learning_objectives = models.TextField(help_text="Learning objectives for this unit")
    duration_hours = models.IntegerField(help_text="Estimated hours to complete")
    start_week = models.IntegerField(null=True, blank=True)
    end_week = models.IntegerField(null=True, blank=True)
    prerequisites = models.TextField(blank=True, help_text="Prerequisites for this unit")
    assessment_methods = models.TextField(blank=True, help_text="Assessment methods for this unit")
    resources_required = models.TextField(blank=True, help_text="Resources and materials required")
    is_mandatory = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['syllabus', 'unit_number']
        ordering = ['order', 'unit_number']

    def __str__(self):
        return f"{self.syllabus.title} - Unit {self.unit_number}: {self.title}"

class LearningMaterial(models.Model):
    """Enhanced learning materials management"""
    MATERIAL_TYPES = [
        ('textbook', 'Textbook'),
        ('workbook', 'Workbook'),
        ('reference', 'Reference Book'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('presentation', 'Presentation'),
        ('document', 'Document'),
        ('worksheet', 'Worksheet'),
        ('quiz', 'Quiz'),
        ('interactive', 'Interactive Content'),
        ('external_link', 'External Link'),
    ]

    MATERIAL_FORMATS = [
        ('pdf', 'PDF'),
        ('doc', 'Word Document'),
        ('ppt', 'PowerPoint'),
        ('video', 'Video File'),
        ('audio', 'Audio File'),
        ('image', 'Image'),
        ('url', 'Web Link'),
        ('zip', 'Archive'),
    ]

    ACCESS_LEVELS = [
        ('public', 'Public'),
        ('students', 'Students Only'),
        ('teachers', 'Teachers Only'),
        ('restricted', 'Restricted Access'),
    ]

    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200)
    description = models.TextField()
    syllabus_unit = models.ForeignKey(SyllabusUnit, on_delete=models.CASCADE, related_name='learning_materials', null=True, blank=True)
    syllabus = models.ForeignKey(AcademicSyllabus, on_delete=models.CASCADE, related_name='learning_materials')
    material_type = models.CharField(max_length=20, choices=MATERIAL_TYPES)
    material_format = models.CharField(max_length=20, choices=MATERIAL_FORMATS)
    file_path = models.FileField(upload_to='learning_materials/', null=True, blank=True)
    external_url = models.URLField(blank=True)
    file_size = models.BigIntegerField(null=True, blank=True, help_text="File size in bytes")
    duration_minutes = models.IntegerField(null=True, blank=True, help_text="Duration for video/audio materials")
    access_level = models.CharField(max_length=20, choices=ACCESS_LEVELS, default='students')
    is_mandatory = models.BooleanField(default=False)
    download_count = models.IntegerField(default=0)
    view_count = models.IntegerField(default=0)
    uploaded_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} ({self.get_material_type_display()})"

class TermPlan(models.Model):
    """Term-specific content planning and scheduling"""
    PLAN_STATUSES = [
        ('draft', 'Draft'),
        ('approved', 'Approved'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('archived', 'Archived'),
    ]

    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200)
    syllabus = models.ForeignKey(AcademicSyllabus, on_delete=models.CASCADE, related_name='term_plans')
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE)
    term = models.ForeignKey('Term', on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    total_weeks = models.IntegerField()
    total_hours = models.IntegerField()
    objectives = models.TextField(help_text="Term objectives and goals")
    key_topics = models.TextField(help_text="Key topics to be covered")
    assessment_plan = models.TextField(help_text="Assessment and evaluation plan")
    resources_needed = models.TextField(blank=True, help_text="Resources and materials needed")
    status = models.CharField(max_length=20, choices=PLAN_STATUSES, default='draft')
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_term_plans')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['syllabus', 'academic_year', 'term']

    def clean(self):
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date.")

    def __str__(self):
        return f"{self.syllabus.title} - {self.term.name} {self.academic_year.year}"

class SyllabusProgress(models.Model):
    """Track syllabus completion progress"""
    PROGRESS_STATUSES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('behind_schedule', 'Behind Schedule'),
        ('ahead_schedule', 'Ahead of Schedule'),
    ]

    id = models.AutoField(primary_key=True)
    syllabus_unit = models.ForeignKey(SyllabusUnit, on_delete=models.CASCADE, related_name='progress_records')
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    planned_start_date = models.DateField()
    actual_start_date = models.DateField(null=True, blank=True)
    planned_end_date = models.DateField()
    actual_end_date = models.DateField(null=True, blank=True)
    completion_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    status = models.CharField(max_length=20, choices=PROGRESS_STATUSES, default='not_started')
    notes = models.TextField(blank=True, help_text="Progress notes and observations")
    challenges = models.TextField(blank=True, help_text="Challenges encountered")
    adjustments_made = models.TextField(blank=True, help_text="Adjustments made to original plan")
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['syllabus_unit', 'teacher', 'grade']

    def __str__(self):
        return f"{self.syllabus_unit.title} - {self.grade.name} - {self.completion_percentage}%"

class AssignmentCategory(models.Model):
    """Categories for different types of assignments"""
    CATEGORY_TYPES = [
        ('homework', 'Homework'),
        ('classwork', 'Classwork'),
        ('project', 'Project'),
        ('research', 'Research'),
        ('presentation', 'Presentation'),
        ('practical', 'Practical Work'),
        ('quiz', 'Quiz'),
        ('test', 'Test'),
        ('group_work', 'Group Work'),
        ('individual', 'Individual Work'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField(blank=True)
    default_weight = models.DecimalField(max_digits=5, decimal_places=2, default=1.00, help_text="Default weight for grading")
    color_code = models.CharField(max_length=7, default='#3B82F6', help_text="Hex color code for UI")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Assignment Categories"

    def __str__(self):
        return self.name

class SyllabusReport(models.Model):
    """Generate and store syllabus reports"""
    REPORT_TYPES = [
        ('progress', 'Progress Report'),
        ('completion', 'Completion Report'),
        ('material_usage', 'Material Usage Report'),
        ('assignment_summary', 'Assignment Summary'),
        ('term_summary', 'Term Summary'),
        ('annual_summary', 'Annual Summary'),
    ]

    REPORT_FORMATS = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
        ('html', 'HTML'),
    ]

    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200)
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    report_format = models.CharField(max_length=10, choices=REPORT_FORMATS, default='pdf')
    syllabus = models.ForeignKey(AcademicSyllabus, on_delete=models.CASCADE, null=True, blank=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, null=True, blank=True)
    subject = models.ForeignKey('academics.Subject', on_delete=models.CASCADE, null=True, blank=True)
    academic_year = models.ForeignKey('AcademicYear', on_delete=models.CASCADE)
    term = models.ForeignKey('Term', on_delete=models.CASCADE, null=True, blank=True)
    date_from = models.DateField()
    date_to = models.DateField()
    file_path = models.FileField(upload_to='syllabus_reports/', null=True, blank=True)
    generated_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    generated_at = models.DateTimeField(auto_now_add=True)

    def clean(self):
        if self.date_from and self.date_to and self.date_to <= self.date_from:
            raise ValidationError("End date must be after start date.")

    def __str__(self):
        return f"{self.title} - {self.generated_at.date()}"

# Simple Academic Year and Term models for syllabus management
class AcademicYear(models.Model):
    """Academic year for syllabus management"""
    id = models.AutoField(primary_key=True)
    year = models.CharField(max_length=20, unique=True)
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.year

class Term(models.Model):
    """Academic term for syllabus management"""
    TERM_CHOICES = [
        ('term_1', 'Term 1'),
        ('term_2', 'Term 2'),
        ('term_3', 'Term 3'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=20, choices=TERM_CHOICES)
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, related_name='terms')
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['name', 'academic_year']

    def __str__(self):
        return f"{self.get_name_display()} - {self.academic_year.year}"
