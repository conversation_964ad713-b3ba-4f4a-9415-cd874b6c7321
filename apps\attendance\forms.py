from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
import csv
from django.utils.dateparse import parse_date
from .models import (
    StudentAttendance, TeacherAttendance, AttendancePeriod,
    BulkAttendanceSession, AttendanceReport, AttendanceAlert
)
from apps.students.models import Student
from apps.teachers.models import Teacher
from apps.academics.models import Grade

User = get_user_model()

class StudentAttendanceForm(forms.ModelForm):
    class Meta:
        model = StudentAttendance
        fields = ['student', 'date', 'status', 'reason', 'period']
        widgets = {
            'student': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm'
            }),
            'date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm'
            }),
            'status': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm'
            }),
            'reason': forms.TextInput(attrs={
                'placeholder': 'Enter reason for absence or late arrival',
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm'
            }),
            'period': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm'
            }),
        }

class TeacherAttendanceForm(forms.ModelForm):
    class Meta:
        model = TeacherAttendance
        fields = ['teacher', 'date', 'status', 'reason', 'period']
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'status': forms.Select(),
            'reason': forms.TextInput(attrs={'placeholder': 'Reason (if absent/late)', 'class': 'form-input'}),
            'period': forms.Select(),
        }

class BulkAttendanceForm(forms.Form):
    date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    status = forms.ChoiceField(choices=StudentAttendance.STATUS_CHOICES)
    file = forms.FileField()
    period = forms.ModelChoiceField(queryset=AttendancePeriod.objects.all())

    def handle_upload(self):
        records_processed = 0
        if self.cleaned_data['file'].content_type == 'text/csv':
            reader = csv.reader(self.cleaned_data['file'].read().decode('utf-8').splitlines())
            next(reader)  # Skip header
            for row in reader:
                if len(row) >= 2:
                    from apps.students.models import Student
                    student = Student.objects.get(id=row[0])
                    date = parse_date(row[1])
                    status = row[2]
                    reason = row[3] if len(row) > 3 else ''

                    StudentAttendance.objects.create(
                        student=student,
                        date=date,
                        status=status,
                        reason=reason,
                        period=self.cleaned_data['period']
                    )
                    records_processed += 1
        return records_processed


class ClassAttendanceForm(forms.Form):
    """
    Form for marking attendance for an entire class
    """
    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )
    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    section = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Section (optional)'})
    )
    period = forms.ModelChoiceField(
        queryset=AttendancePeriod.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add dynamic student fields based on selected grade
        if 'grade' in self.data:
            try:
                grade_id = int(self.data.get('grade'))
                grade = Grade.objects.get(id=grade_id)
                students = Student.objects.filter(current_grade=grade, status='active')

                for student in students:
                    self.fields[f'student_{student.id}'] = forms.ChoiceField(
                        choices=StudentAttendance.STATUS_CHOICES,
                        initial='present',
                        widget=forms.Select(attrs={'class': 'form-select-sm'})
                    )
                    self.fields[f'reason_{student.id}'] = forms.CharField(
                        required=False,
                        widget=forms.TextInput(attrs={
                            'class': 'form-input-sm',
                            'placeholder': 'Reason (if absent/late)'
                        })
                    )
            except (ValueError, TypeError, Grade.DoesNotExist):
                pass


class AttendanceSearchForm(forms.Form):
    """
    Advanced search form for attendance records
    """
    search_query = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search by student name, ID, or teacher name...'
        })
    )

    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + StudentAttendance.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="All Grades",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    period = forms.ModelChoiceField(
        queryset=AttendancePeriod.objects.all(),
        required=False,
        empty_label="All Periods",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    attendance_type = forms.ChoiceField(
        choices=[
            ('', 'All Types'),
            ('student', 'Student Attendance'),
            ('teacher', 'Teacher Attendance'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class AttendanceReportForm(forms.Form):
    """
    Form for generating attendance reports
    """
    REPORT_TYPE_CHOICES = [
        ('daily_summary', 'Daily Summary'),
        ('weekly_summary', 'Weekly Summary'),
        ('monthly_summary', 'Monthly Summary'),
        ('term_summary', 'Term Summary'),
        ('student_individual', 'Individual Student Report'),
        ('class_summary', 'Class Summary'),
        ('grade_summary', 'Grade Summary'),
        ('teacher_summary', 'Teacher Summary'),
        ('attendance_trends', 'Attendance Trends'),
        ('absence_analysis', 'Absence Analysis'),
    ]

    FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
        ('html', 'HTML'),
    ]

    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    format = forms.ChoiceField(
        choices=FORMAT_CHOICES,
        initial='pdf',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="All Grades",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    student = forms.ModelChoiceField(
        queryset=Student.objects.filter(status='active'),
        required=False,
        empty_label="All Students",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    teacher = forms.ModelChoiceField(
        queryset=Teacher.objects.filter(status='active'),
        required=False,
        empty_label="All Teachers",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    include_statistics = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )

    include_charts = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )


class AttendanceAnalyticsForm(forms.Form):
    """
    Form for attendance analytics and insights
    """
    ANALYSIS_TYPE_CHOICES = [
        ('attendance_trends', 'Attendance Trends'),
        ('performance_correlation', 'Performance Correlation'),
        ('pattern_analysis', 'Pattern Analysis'),
        ('risk_identification', 'Risk Identification'),
        ('comparative_analysis', 'Comparative Analysis'),
    ]

    analysis_type = forms.ChoiceField(
        choices=ANALYSIS_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="All Grades",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    minimum_attendance_rate = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        initial=75.00,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-input',
            'step': '0.01',
            'min': '0',
            'max': '100'
        })
    )

    include_predictions = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )


class BulkAttendanceImportForm(forms.Form):
    """
    Enhanced form for bulk attendance import
    """
    IMPORT_TYPE_CHOICES = [
        ('csv', 'CSV File'),
        ('excel', 'Excel File'),
        ('json', 'JSON File'),
    ]

    import_type = forms.ChoiceField(
        choices=IMPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    file = forms.FileField(
        widget=forms.FileInput(attrs={
            'class': 'form-file',
            'accept': '.csv,.xlsx,.xls,.json'
        })
    )

    date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-input'})
    )

    period = forms.ModelChoiceField(
        queryset=AttendancePeriod.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="All Grades",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    overwrite_existing = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )

    validate_students = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )

    send_notifications = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )