{% extends 'teachers/base.html' %}

{% block teachers_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Exam Dashboard</h1>
            <p class="mt-2 text-gray-600">Manage your exam assignments and student results</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
            <a href="{% url 'teachers:exam_assignments' %}" class="action-button btn-primary">
                <i class="fas fa-clipboard-list mr-2"></i>View Assignments
            </a>
            <a href="{% url 'teachers:result_entry' %}" class="action-button btn-success">
                <i class="fas fa-edit mr-2"></i>Enter Results
            </a>
            <a href="{% url 'teachers:student_management' %}" class="action-button btn-info">
                <i class="fas fa-users mr-2"></i>My Students
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-shield-alt text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ chief_invigilated_exams|length|default:0 }}</div>
            <div class="text-sm opacity-90">Chief Invigilator</div>
            <div class="text-xs opacity-75 mt-1">Assignments</div>
        </div>

        <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-edit text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ pending_results|length|default:0 }}</div>
            <div class="text-sm opacity-90">Pending Results</div>
            <div class="text-xs opacity-75 mt-1">To Enter</div>
        </div>

        <div class="stats-card" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-bell text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ notifications|length|default:0 }}</div>
            <div class="text-sm opacity-90">Notifications</div>
            <div class="text-xs opacity-75 mt-1">Recent</div>
        </div>

        <div class="stats-card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-chart-bar text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ subject_performance|length|default:0 }}</div>
            <div class="text-sm opacity-90">Performance Reports</div>
            <div class="text-xs opacity-75 mt-1">Available</div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Chief Invigilator Assignments -->
        <div class="form-section">
            <h3 class="section-header">Chief Invigilator Assignments</h3>
            {% if chief_invigilated_exams %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for assignment in chief_invigilated_exams %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ assignment.exam.name }}</div>
                                    <div class="text-sm text-gray-500">{{ assignment.exam.exam_type.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ assignment.subject.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ assignment.exam_date|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="status-badge status-{{ assignment.status }}">{{ assignment.get_status_display }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    <a href="{% url 'teachers:exam_assignments' %}" class="action-button btn-primary">View All Assignments</a>
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-shield-alt text-4xl mb-4"></i>
                    <p>No chief invigilator assignments</p>
                </div>
            {% endif %}
        </div>

        <!-- Pending Result Entries -->
        <div class="form-section">
            <h3 class="section-header">Pending Result Entries</h3>
            {% if pending_results %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for result in pending_results %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ result.exam.name }}</div>
                                    <div class="text-sm text-gray-500">{{ result.exam.exam_type.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ result.subject.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ result.grade.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ result.results_count|default:0 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'teachers:result_entry' %}?exam={{ result.exam.id }}&subject={{ result.subject.id }}&grade={{ result.grade.id }}"
                                       class="action-button btn-primary">
                                        Enter Results
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    <a href="{% url 'teachers:result_entry' %}" class="action-button btn-primary">View All Pending</a>
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-edit text-4xl mb-4"></i>
                    <p>No pending result entries</p>
                </div>
            {% endif %}
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Notifications -->
        <div class="form-section">
            <h3 class="section-header">Recent Notifications</h3>
            {% if notifications %}
                <div class="space-y-3">
                    {% for notification in notifications %}
                    <div class="flex items-start p-3 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0 mr-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-bell text-blue-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h6 class="text-sm font-medium text-gray-900 mb-1">{{ notification.title }}</h6>
                            <p class="text-sm text-gray-600 mb-1">{{ notification.message|truncatewords:15 }}</p>
                            <p class="text-xs text-gray-500">{{ notification.created_at|timesince }} ago</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-bell text-4xl mb-4"></i>
                    <p>No recent notifications</p>
                </div>
            {% endif %}
        </div>

        <!-- Subject Performance -->
        <div class="form-section">
            <h3 class="section-header">Subject Performance Analysis</h3>
            {% if subject_performance %}
                <div class="space-y-4">
                    {% for performance in subject_performance %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <h6 class="text-sm font-medium text-gray-900 mb-1">{{ performance.subject.name }}</h6>
                            <p class="text-xs text-gray-500 mb-2">{{ performance.exam.name }} - {{ performance.grade.name }}</p>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-{% if performance.average_percentage >= 75 %}green{% elif performance.average_percentage >= 50 %}yellow{% else %}red{% endif %}-500 h-2 rounded-full"
                                     style="width: {{ performance.average_percentage }}%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Average: {{ performance.average_percentage|floatformat:1 }}%</p>
                        </div>
                        <div class="ml-4">
                            <span class="employment-badge {% if performance.average_percentage >= 75 %}bg-green-500{% elif performance.average_percentage >= 50 %}bg-yellow-500{% else %}bg-red-500{% endif %}">
                                {{ performance.average_percentage|floatformat:1 }}%
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-chart-bar text-4xl mb-4"></i>
                    <p>No performance data available</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Petty Cash Quick Access -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                    <i class="fas fa-money-bill-wave text-xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Petty Cash Requests</h2>
                    <p class="text-gray-600">Manage your petty cash requests and expenses</p>
                </div>
            </div>
            <a href="{% url 'petty_cash:dashboard' %}"
               class="text-purple-600 hover:text-purple-800 font-medium">
                View All
            </a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-800">New Request</p>
                        <p class="text-xs text-blue-600 mt-1">Submit petty cash request</p>
                    </div>
                    <a href="{% url 'petty_cash:create_request' %}"
                       class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-800">My Requests</p>
                        <p class="text-xs text-green-600 mt-1">View request status</p>
                    </div>
                    <a href="{% url 'petty_cash:request_list' %}"
                       class="bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg transition-colors">
                        <i class="fas fa-list"></i>
                    </a>
                </div>
            </div>

            <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-800">Expenses</p>
                        <p class="text-xs text-purple-600 mt-1">Record expenses</p>
                    </div>
                    <a href="{% url 'petty_cash:expense_list' %}"
                       class="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-lg transition-colors">
                        <i class="fas fa-receipt"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh notifications every 5 minutes
setInterval(function() {
    // You can add AJAX call here to refresh notifications
}, 300000);
</script>
{% endblock %}
