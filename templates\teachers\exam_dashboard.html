{% extends 'teachers/base.html' %}
{% load static %}

{% block title %}Exam Dashboard - {{ teacher.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
    .exam-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .exam-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .pending-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .notification-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    .performance-card {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Exam Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'teachers:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Exam Dashboard</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Chief Invigilator</p>
                            <h4 class="mb-2">{{ chief_invigilated_exams|length }}</h4>
                            <p class="text-muted mb-0"><span class="text-success fw-bold font-size-12 me-2">Assignments</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-primary rounded-3">
                                <i class="ri-shield-check-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card pending-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Pending Results</p>
                            <h4 class="mb-2">{{ pending_results|length }}</h4>
                            <p class="text-muted mb-0"><span class="text-warning fw-bold font-size-12 me-2">To Enter</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-warning rounded-3">
                                <i class="ri-file-edit-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card notification-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Notifications</p>
                            <h4 class="mb-2">{{ notifications|length }}</h4>
                            <p class="text-muted mb-0"><span class="text-info fw-bold font-size-12 me-2">Recent</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-info rounded-3">
                                <i class="ri-notification-3-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card performance-card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-truncate font-size-14 mb-2">Performance Reports</p>
                            <h4 class="mb-2">{{ subject_performance|length }}</h4>
                            <p class="text-muted mb-0"><span class="text-success fw-bold font-size-12 me-2">Available</span></p>
                        </div>
                        <div class="avatar-sm">
                            <span class="avatar-title bg-light text-success rounded-3">
                                <i class="ri-bar-chart-line font-size-24"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Chief Invigilator Assignments -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Chief Invigilator Assignments</h4>
                </div>
                <div class="card-body">
                    {% if chief_invigilated_exams %}
                        <div class="table-responsive">
                            <table class="table table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th>Exam</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for assignment in chief_invigilated_exams %}
                                    <tr>
                                        <td>{{ assignment.exam.name }}</td>
                                        <td>{{ assignment.subject.name }}</td>
                                        <td>{{ assignment.exam_date|date:"M d, Y" }}</td>
                                        <td>
                                            <span class="badge bg-{% if assignment.status == 'completed' %}success{% elif assignment.status == 'active' %}warning{% else %}secondary{% endif %}">
                                                {{ assignment.get_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'teachers:exam_assignments' %}" class="btn btn-primary btn-sm">View All Assignments</a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-shield-check-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No chief invigilator assignments</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Pending Result Entries -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Pending Result Entries</h4>
                </div>
                <div class="card-body">
                    {% if pending_results %}
                        <div class="table-responsive">
                            <table class="table table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th>Exam</th>
                                        <th>Subject</th>
                                        <th>Grade</th>
                                        <th>Results</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for result in pending_results %}
                                    <tr>
                                        <td>{{ result.exam.name }}</td>
                                        <td>{{ result.subject.name }}</td>
                                        <td>{{ result.grade.name }}</td>
                                        <td>{{ result.results_count }}</td>
                                        <td>
                                            <a href="{% url 'exams:result_entry' result.timetable_entry.id %}" class="btn btn-sm btn-outline-primary">
                                                Enter Results
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'teachers:result_entry' %}" class="btn btn-primary btn-sm">View All Pending</a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-file-edit-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No pending result entries</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Notifications -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Recent Notifications</h4>
                </div>
                <div class="card-body">
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="d-flex align-items-start border-bottom pb-3 mb-3">
                            <div class="avatar-xs me-3">
                                <span class="avatar-title rounded-circle bg-primary">
                                    <i class="ri-notification-3-line"></i>
                                </span>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ notification.title }}</h6>
                                <p class="text-muted mb-1">{{ notification.message|truncatewords:15 }}</p>
                                <p class="text-muted mb-0 font-size-12">{{ notification.created_at|timesince }} ago</p>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-notification-3-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No recent notifications</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Subject Performance -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Subject Performance Analysis</h4>
                </div>
                <div class="card-body">
                    {% if subject_performance %}
                        {% for performance in subject_performance %}
                        <div class="d-flex align-items-center border-bottom pb-3 mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ performance.subject.name }}</h6>
                                <p class="text-muted mb-1">{{ performance.exam.name }} - {{ performance.grade.name }}</p>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-success" style="width: {{ performance.average_percentage }}%"></div>
                                </div>
                                <p class="text-muted mb-0 font-size-12 mt-1">Average: {{ performance.average_percentage|floatformat:1 }}%</p>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{% if performance.average_percentage >= 75 %}success{% elif performance.average_percentage >= 50 %}warning{% else %}danger{% endif %}">
                                    {{ performance.average_percentage|floatformat:1 }}%
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="ri-bar-chart-line font-size-48 text-muted"></i>
                            <p class="text-muted mt-2">No performance data available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh notifications every 5 minutes
    setInterval(function() {
        // You can add AJAX call here to refresh notifications
    }, 300000);
</script>
{% endblock %}
