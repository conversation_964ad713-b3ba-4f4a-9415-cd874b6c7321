from django.contrib import admin
from .models import StudentHealthRecords, StudentHealthVisit

@admin.register(StudentHealthRecords)
class StudentHealthRecordsAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'blood_group', 'allergies', 'chronic_conditions', 'medications', 'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship', 'insurance_provider', 'insurance_number', 'last_updated')
    search_fields = ('student__full_name', 'blood_group', 'emergency_contact_name', 'insurance_provider')
    list_filter = ('blood_group', 'last_updated')
    ordering = ('-last_updated', 'student')

@admin.register(StudentHealthVisit)
class StudentHealthVisitAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'visit_date', 'complaint', 'diagnosis', 'treatment', 'medication', 'attended_by', 'referred_to_hospital', 'hospital_name', 'parent_notified', 'notification_date', 'follow_up_date', 'status')
    search_fields = ('student__full_name', 'complaint', 'diagnosis', 'attended_by', 'hospital_name')
    list_filter = ('status', 'referred_to_hospital', 'parent_notified', 'visit_date')
    ordering = ('-visit_date', 'student')
