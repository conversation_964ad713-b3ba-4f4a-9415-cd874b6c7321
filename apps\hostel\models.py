from django.db import models
from django.core.exceptions import ValidationError
from datetime import date

# Create your models here.

class Hostel(models.Model):
    HOSTEL_GENDERS = [
        ('boys', 'Boys'),
        ('girls', 'Girls'),
        ('mixed', 'Mixed'),
    ]
    HOSTEL_STATUSES = [
        ('active', 'Active'),
        ('maintenance', 'Maintenance'),
        ('inactive', 'Inactive'),
    ]
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    capacity = models.IntegerField()
    gender = models.CharField(max_length=50, choices=HOSTEL_GENDERS)
    description = models.TextField(null=True, blank=True)
    warden_name = models.CharField(max_length=255, null=True, blank=True)
    warden_contact = models.CharField(max_length=20, null=True, blank=True)
    building = models.CharField(max_length=100, null=True, blank=True)
    floor = models.CharField(max_length=20, null=True, blank=True)
    status = models.Char<PERSON><PERSON>(max_length=50, default='active', choices=HOSTEL_STATUSES)

    def __str__(self):
        return self.name

class HostelRoom(models.Model):
    ROOM_TYPES = [
        ('single', 'Single'),
        ('double', 'Double'),
        ('dormitory', 'Dormitory'),
    ]
    ROOM_STATUSES = [
        ('available', 'Available'),
        ('full', 'Full'),
        ('maintenance', 'Maintenance'),
    ]
    id = models.AutoField(primary_key=True)
    hostel = models.ForeignKey(Hostel, on_delete=models.CASCADE, related_name='hostelroom')
    room_number = models.CharField(max_length=20)
    capacity = models.IntegerField()
    current_occupancy = models.IntegerField(default=0)
    room_type = models.CharField(max_length=50, null=True, blank=True, choices=ROOM_TYPES)
    floor = models.CharField(max_length=20, null=True, blank=True)
    status = models.CharField(max_length=50, default='available', choices=ROOM_STATUSES)

    def __str__(self):
        return f"{self.hostel.name} - Room {self.room_number}"

class HostelAllocation(models.Model):
    ALLOCATION_STATUSES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE)
    hostel = models.ForeignKey(Hostel, on_delete=models.CASCADE, related_name='hostelallocation')
    room = models.ForeignKey(HostelRoom, on_delete=models.CASCADE, related_name='hostelallocation')
    bed_number = models.IntegerField(null=True, blank=True)
    allocation_date = models.DateField()
    release_date = models.DateField(null=True, blank=True)
    fee = models.DecimalField(max_digits=11, decimal_places=2)
    status = models.CharField(max_length=50, default='active', choices=ALLOCATION_STATUSES)
    allocated_by = models.IntegerField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"Hostel Allocation - {self.id}"

    class Meta:
        unique_together = ['student', 'status']  # One active allocation per student

    def clean(self):
        # Validate that student doesn't have another active allocation
        if self.status == 'active':
            existing = HostelAllocation.objects.filter(
                student=self.student,
                status='active'
            ).exclude(pk=self.pk)
            if existing.exists():
                raise ValidationError("Student already has an active hostel allocation.")

# Enhanced Models for Complete Hostel Management

class HostelBed(models.Model):
    """Individual bed management within rooms"""
    BED_STATUSES = [
        ('available', 'Available'),
        ('occupied', 'Occupied'),
        ('maintenance', 'Under Maintenance'),
        ('reserved', 'Reserved'),
    ]

    id = models.AutoField(primary_key=True)
    room = models.ForeignKey(HostelRoom, on_delete=models.CASCADE, related_name='beds')
    bed_number = models.CharField(max_length=10)
    status = models.CharField(max_length=20, choices=BED_STATUSES, default='available')
    current_occupant = models.ForeignKey(
        'students.Student',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='current_bed'
    )
    last_maintenance = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        unique_together = ['room', 'bed_number']

    def __str__(self):
        return f"{self.room} - Bed {self.bed_number}"

class MealPlan(models.Model):
    """Meal plan management for boarding students"""
    MEAL_TYPES = [
        ('breakfast', 'Breakfast Only'),
        ('lunch', 'Lunch Only'),
        ('dinner', 'Dinner Only'),
        ('breakfast_lunch', 'Breakfast & Lunch'),
        ('lunch_dinner', 'Lunch & Dinner'),
        ('full_board', 'Full Board (All Meals)'),
        ('custom', 'Custom Plan'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    meal_type = models.CharField(max_length=20, choices=MEAL_TYPES)
    description = models.TextField(blank=True)
    monthly_fee = models.DecimalField(max_digits=10, decimal_places=2)
    includes_breakfast = models.BooleanField(default=False)
    includes_lunch = models.BooleanField(default=False)
    includes_dinner = models.BooleanField(default=False)
    includes_snacks = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} - KES {self.monthly_fee}"

class HostelWarden(models.Model):
    """Dedicated warden management"""
    WARDEN_TYPES = [
        ('head_warden', 'Head Warden'),
        ('assistant_warden', 'Assistant Warden'),
        ('night_warden', 'Night Warden'),
        ('duty_warden', 'Duty Warden'),
    ]

    id = models.AutoField(primary_key=True)
    user = models.OneToOneField('accounts.User', on_delete=models.CASCADE, related_name='warden_profile')
    employee_id = models.CharField(max_length=20, unique=True)
    warden_type = models.CharField(max_length=20, choices=WARDEN_TYPES)
    assigned_hostels = models.ManyToManyField(Hostel, related_name='wardens')
    phone_number = models.CharField(max_length=15)
    emergency_contact = models.CharField(max_length=15, blank=True)
    address = models.TextField(blank=True)
    hire_date = models.DateField()
    is_active = models.BooleanField(default=True)
    shift_start = models.TimeField(null=True, blank=True)
    shift_end = models.TimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_warden_type_display()}"

class BoardingFeeStructure(models.Model):
    """Fee structure for different boarding options"""
    FEE_TYPES = [
        ('accommodation', 'Accommodation Only'),
        ('accommodation_meals', 'Accommodation + Meals'),
        ('full_boarding', 'Full Boarding Package'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    fee_type = models.CharField(max_length=20, choices=FEE_TYPES)
    hostel = models.ForeignKey(Hostel, on_delete=models.CASCADE, related_name='fee_structures')
    room_type = models.CharField(max_length=20, choices=HostelRoom.ROOM_TYPES, blank=True)
    accommodation_fee = models.DecimalField(max_digits=10, decimal_places=2)
    meal_plan = models.ForeignKey(MealPlan, on_delete=models.SET_NULL, null=True, blank=True)
    security_deposit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    maintenance_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_monthly_fee = models.DecimalField(max_digits=10, decimal_places=2)
    academic_year = models.CharField(max_length=20)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        # Auto-calculate total fee
        self.total_monthly_fee = self.accommodation_fee + self.maintenance_fee
        if self.meal_plan:
            self.total_monthly_fee += self.meal_plan.monthly_fee
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} - {self.hostel.name} - KES {self.total_monthly_fee}"

class BoardingFeePayment(models.Model):
    """Track boarding fee payments"""
    PAYMENT_METHODS = [
        ('cash', 'Cash'),
        ('mpesa', 'M-Pesa'),
        ('bank_transfer', 'Bank Transfer'),
        ('cheque', 'Cheque'),
        ('card', 'Credit/Debit Card'),
    ]

    PAYMENT_STATUSES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]

    id = models.AutoField(primary_key=True)
    allocation = models.ForeignKey(HostelAllocation, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    payment_date = models.DateTimeField(auto_now_add=True)
    payment_for_month = models.DateField()  # Which month this payment covers
    reference_number = models.CharField(max_length=100, blank=True)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUSES, default='completed')
    notes = models.TextField(blank=True)
    processed_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)

    class Meta:
        unique_together = ['allocation', 'payment_for_month']

    def __str__(self):
        return f"Payment - {self.allocation.student.full_name} - {self.payment_for_month.strftime('%B %Y')}"

class BoardingFeeDiscount(models.Model):
    """Manage discounts for boarding fees"""
    DISCOUNT_TYPES = [
        ('percentage', 'Percentage'),
        ('fixed_amount', 'Fixed Amount'),
    ]

    DISCOUNT_REASONS = [
        ('scholarship', 'Scholarship'),
        ('financial_hardship', 'Financial Hardship'),
        ('sibling_discount', 'Sibling Discount'),
        ('staff_child', 'Staff Child'),
        ('academic_excellence', 'Academic Excellence'),
        ('other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    allocation = models.ForeignKey(HostelAllocation, on_delete=models.CASCADE, related_name='discounts')
    discount_type = models.CharField(max_length=20, choices=DISCOUNT_TYPES)
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.CharField(max_length=30, choices=DISCOUNT_REASONS)
    description = models.TextField(blank=True)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def calculate_discount_amount(self, base_fee):
        """Calculate the actual discount amount"""
        if self.discount_type == 'percentage':
            return base_fee * (self.discount_value / 100)
        else:
            return min(self.discount_value, base_fee)

    def __str__(self):
        return f"Discount - {self.allocation.student.full_name} - {self.get_reason_display()}"

class HostelFacility(models.Model):
    """Manage hostel facilities and amenities"""
    FACILITY_TYPES = [
        ('common_room', 'Common Room'),
        ('study_hall', 'Study Hall'),
        ('dining_hall', 'Dining Hall'),
        ('kitchen', 'Kitchen'),
        ('laundry', 'Laundry'),
        ('bathroom', 'Bathroom'),
        ('recreation', 'Recreation Area'),
        ('medical_room', 'Medical Room'),
        ('storage', 'Storage'),
        ('other', 'Other'),
    ]

    FACILITY_STATUSES = [
        ('operational', 'Operational'),
        ('maintenance', 'Under Maintenance'),
        ('closed', 'Closed'),
        ('renovation', 'Under Renovation'),
    ]

    id = models.AutoField(primary_key=True)
    hostel = models.ForeignKey(Hostel, on_delete=models.CASCADE, related_name='facilities')
    name = models.CharField(max_length=100)
    facility_type = models.CharField(max_length=20, choices=FACILITY_TYPES)
    description = models.TextField(blank=True)
    location = models.CharField(max_length=100, blank=True)  # Floor, wing, etc.
    capacity = models.IntegerField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=FACILITY_STATUSES, default='operational')
    last_maintenance = models.DateField(null=True, blank=True)
    next_maintenance = models.DateField(null=True, blank=True)
    maintenance_notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.hostel.name} - {self.name}"

class BoardingStudentStatus(models.Model):
    """Track detailed boarding status and history"""
    STATUS_TYPES = [
        ('checked_in', 'Checked In'),
        ('checked_out', 'Checked Out'),
        ('on_leave', 'On Leave'),
        ('suspended', 'Suspended'),
        ('expelled', 'Expelled'),
        ('transferred', 'Transferred'),
    ]

    id = models.AutoField(primary_key=True)
    allocation = models.ForeignKey(HostelAllocation, on_delete=models.CASCADE, related_name='status_history')
    status = models.CharField(max_length=20, choices=STATUS_TYPES)
    status_date = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True)
    expected_return = models.DateTimeField(null=True, blank=True)
    actual_return = models.DateTimeField(null=True, blank=True)
    recorded_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-status_date']

    def __str__(self):
        return f"{self.allocation.student.full_name} - {self.get_status_display()} - {self.status_date.date()}"
