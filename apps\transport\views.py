from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, F
from django.db import models
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
import csv
import json

from .models import TransportRoute, TransportVehicle, TransportStop, TransportAssignment
from .forms import (
    TransportRouteForm, TransportVehicleForm, TransportStopForm,
    TransportAssignmentForm, TransportSearchForm, TransportFeePaymentForm,
    TransportDiscountForm, TransportReportForm
)
from apps.students.models import Student

@login_required
def transport_dashboard(request):
    """Transport management dashboard with statistics and recent activities."""
    context = {
        'total_routes': TransportRoute.objects.count(),
        'total_vehicles': TransportVehicle.objects.count(),
        'active_vehicles': TransportVehicle.objects.filter(status='active').count(),
        'total_assignments': TransportAssignment.objects.filter(status='active').count(),
        'total_students': TransportAssignment.objects.filter(status='active').values('student').distinct().count(),
        'recent_assignments': TransportAssignment.objects.select_related(
            'student', 'route', 'vehicle', 'stop'
        ).order_by('-start_date')[:10],
        'vehicle_utilization': TransportVehicle.objects.annotate(
            assigned_students=Count('transportassignment', filter=Q(transportassignment__status='active'))
        ).filter(status='active')[:5],
        'route_popularity': TransportRoute.objects.annotate(
            student_count=Count('transportassignment', filter=Q(transportassignment__status='active'))
        ).order_by('-student_count')[:5],
    }
    return render(request, 'transport/dashboard.html', context)

# Route Management Views
@login_required
def route_list(request):
    """List all transport routes."""
    routes = TransportRoute.objects.annotate(
        student_count=Count('transportassignment', filter=Q(transportassignment__status='active')),
        stop_count=Count('transportstop')
    ).order_by('name')

    paginator = Paginator(routes, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'routes': page_obj,
    }
    return render(request, 'transport/route_list.html', context)

@login_required
def route_detail(request, route_id):
    """Display detailed information about a route."""
    route = get_object_or_404(TransportRoute, id=route_id)
    stops = TransportStop.objects.filter(route=route).order_by('sequence')
    assignments = TransportAssignment.objects.filter(
        route=route, status='active'
    ).select_related('student', 'vehicle', 'stop')

    context = {
        'route': route,
        'stops': stops,
        'assignments': assignments,
        'total_students': assignments.count(),
    }
    return render(request, 'transport/route_detail.html', context)

@login_required
def add_route(request):
    """Add a new transport route."""
    if request.method == 'POST':
        form = TransportRouteForm(request.POST)
        if form.is_valid():
            route = form.save()
            messages.success(request, f'Route "{route.name}" has been added successfully.')
            return redirect('transport:route_detail', route_id=route.id)
    else:
        form = TransportRouteForm()

    context = {'form': form}
    return render(request, 'transport/add_route.html', context)

@login_required
def edit_route(request, route_id):
    """Edit an existing route."""
    route = get_object_or_404(TransportRoute, id=route_id)

    if request.method == 'POST':
        form = TransportRouteForm(request.POST, instance=route)
        if form.is_valid():
            form.save()
            messages.success(request, f'Route "{route.name}" has been updated successfully.')
            return redirect('transport:route_detail', route_id=route.id)
    else:
        form = TransportRouteForm(instance=route)

    context = {'form': form, 'route': route}
    return render(request, 'transport/edit_route.html', context)

# Vehicle Management Views
@login_required
def vehicle_list(request):
    """List all transport vehicles."""
    vehicles = TransportVehicle.objects.annotate(
        assigned_students=Count('transportassignment', filter=Q(transportassignment__status='active'))
    ).order_by('name')

    paginator = Paginator(vehicles, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'vehicles': page_obj,
    }
    return render(request, 'transport/vehicle_list.html', context)

@login_required
def vehicle_detail(request, vehicle_id):
    """Display detailed information about a vehicle."""
    vehicle = get_object_or_404(TransportVehicle, id=vehicle_id)
    assignments = TransportAssignment.objects.filter(
        vehicle=vehicle, status='active'
    ).select_related('student', 'route', 'stop')

    context = {
        'vehicle': vehicle,
        'assignments': assignments,
        'utilization_percentage': (assignments.count() / vehicle.capacity * 100) if vehicle.capacity > 0 else 0,
    }
    return render(request, 'transport/vehicle_detail.html', context)

@login_required
def add_vehicle(request):
    """Add a new transport vehicle."""
    if request.method == 'POST':
        form = TransportVehicleForm(request.POST)
        if form.is_valid():
            vehicle = form.save()
            messages.success(request, f'Vehicle "{vehicle.name}" has been added successfully.')
            return redirect('transport:vehicle_detail', vehicle_id=vehicle.id)
    else:
        form = TransportVehicleForm()

    context = {'form': form}
    return render(request, 'transport/add_vehicle.html', context)

@login_required
def edit_vehicle(request, vehicle_id):
    """Edit an existing vehicle."""
    vehicle = get_object_or_404(TransportVehicle, id=vehicle_id)

    if request.method == 'POST':
        form = TransportVehicleForm(request.POST, instance=vehicle)
        if form.is_valid():
            form.save()
            messages.success(request, f'Vehicle "{vehicle.name}" has been updated successfully.')
            return redirect('transport:vehicle_detail', vehicle_id=vehicle.id)
    else:
        form = TransportVehicleForm(instance=vehicle)

    context = {'form': form, 'vehicle': vehicle}
    return render(request, 'transport/edit_vehicle.html', context)

# Stop Management Views
@login_required
def stop_list(request, route_id):
    """List all stops for a specific route."""
    route = get_object_or_404(TransportRoute, id=route_id)
    stops = TransportStop.objects.filter(route=route).order_by('sequence')

    context = {
        'route': route,
        'stops': stops,
    }
    return render(request, 'transport/stop_list.html', context)

@login_required
def add_stop(request, route_id):
    """Add a new stop to a route."""
    route = get_object_or_404(TransportRoute, id=route_id)

    if request.method == 'POST':
        form = TransportStopForm(request.POST)
        if form.is_valid():
            stop = form.save(commit=False)
            stop.route = route
            stop.save()
            messages.success(request, f'Stop "{stop.name}" has been added to route "{route.name}".')
            return redirect('transport:stop_list', route_id=route.id)
    else:
        form = TransportStopForm(initial={'route': route})

    context = {'form': form, 'route': route}
    return render(request, 'transport/add_stop.html', context)

@login_required
def edit_stop(request, stop_id):
    """Edit an existing stop."""
    stop = get_object_or_404(TransportStop, id=stop_id)

    if request.method == 'POST':
        form = TransportStopForm(request.POST, instance=stop)
        if form.is_valid():
            form.save()
            messages.success(request, f'Stop "{stop.name}" has been updated successfully.')
            return redirect('transport:stop_list', route_id=stop.route.id)
    else:
        form = TransportStopForm(instance=stop)

    context = {'form': form, 'stop': stop}
    return render(request, 'transport/edit_stop.html', context)

# Student Assignment Views
@login_required
def assignment_list(request):
    """List all transport assignments with search and filter functionality."""
    form = TransportSearchForm(request.GET)
    assignments = TransportAssignment.objects.select_related(
        'student', 'route', 'vehicle', 'stop'
    ).all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        route = form.cleaned_data.get('route')
        vehicle = form.cleaned_data.get('vehicle')
        status = form.cleaned_data.get('status')

        if search_query:
            assignments = assignments.filter(
                Q(student__full_name__icontains=search_query) |
                Q(student__index_number__icontains=search_query) |
                Q(route__name__icontains=search_query) |
                Q(vehicle__name__icontains=search_query)
            )

        if route:
            assignments = assignments.filter(route=route)

        if vehicle:
            assignments = assignments.filter(vehicle=vehicle)

        if status:
            assignments = assignments.filter(status=status)

    assignments = assignments.order_by('-start_date')

    paginator = Paginator(assignments, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'assignments': page_obj,
    }
    return render(request, 'transport/assignment_list.html', context)

@login_required
def assignment_detail(request, assignment_id):
    """Display detailed information about a transport assignment."""
    assignment = get_object_or_404(TransportAssignment, id=assignment_id)

    context = {'assignment': assignment}
    return render(request, 'transport/assignment_detail.html', context)

@login_required
def add_assignment(request):
    """Assign a student to transport."""
    if request.method == 'POST':
        form = TransportAssignmentForm(request.POST)
        if form.is_valid():
            student = form.cleaned_data['student_index']
            assignment = form.save(commit=False)
            assignment.student = student
            # No need to set created_at as it doesn't exist in the model
            assignment.save()

            messages.success(request, f'Transport assignment created for {student.full_name}.')
            return redirect('transport:assignment_detail', assignment_id=assignment.id)
    else:
        form = TransportAssignmentForm()

    context = {'form': form}
    return render(request, 'transport/add_assignment.html', context)

@login_required
def edit_assignment(request, assignment_id):
    """Edit an existing transport assignment."""
    assignment = get_object_or_404(TransportAssignment, id=assignment_id)

    if request.method == 'POST':
        form = TransportAssignmentForm(request.POST, instance=assignment)
        if form.is_valid():
            student = form.cleaned_data['student_index']
            assignment = form.save(commit=False)
            assignment.student = student
            assignment.save()

            messages.success(request, f'Transport assignment updated for {student.full_name}.')
            return redirect('transport:assignment_detail', assignment_id=assignment.id)
    else:
        form = TransportAssignmentForm(instance=assignment)
        form.fields['student_index'].initial = assignment.student.index_number

    context = {'form': form, 'assignment': assignment}
    return render(request, 'transport/edit_assignment.html', context)

# Fee Management Views
@login_required
def fee_payment(request, assignment_id):
    """Record a transport fee payment."""
    assignment = get_object_or_404(TransportAssignment, id=assignment_id)

    if request.method == 'POST':
        form = TransportFeePaymentForm(request.POST)
        if form.is_valid():
            # Here you would integrate with your payment system
            # For now, we'll just show a success message
            amount = form.cleaned_data['amount']
            payment_method = form.cleaned_data['payment_method']
            reference = form.cleaned_data['reference_number']

            messages.success(
                request,
                f'Payment of KES {amount} recorded for {assignment.student.full_name} '
                f'via {payment_method}. Reference: {reference or "N/A"}'
            )
            return redirect('transport:assignment_detail', assignment_id=assignment.id)
    else:
        form = TransportFeePaymentForm(initial={
            'assignment_id': assignment_id,
            'amount': assignment.fee
        })

    context = {'form': form, 'assignment': assignment}
    return render(request, 'transport/fee_payment.html', context)

@login_required
def apply_discount(request, assignment_id):
    """Apply a discount to transport fee."""
    assignment = get_object_or_404(TransportAssignment, id=assignment_id)

    if request.method == 'POST':
        form = TransportDiscountForm(request.POST)
        if form.is_valid():
            discount_type = form.cleaned_data['discount_type']
            discount_value = form.cleaned_data['discount_value']
            reason = form.cleaned_data['reason']

            # Calculate new fee
            if discount_type == 'percentage':
                new_fee = assignment.fee * (1 - discount_value / 100)
            else:  # fixed amount
                new_fee = max(0, assignment.fee - discount_value)

            assignment.fee = new_fee
            assignment.save()

            messages.success(
                request,
                f'Discount applied to {assignment.student.full_name}. '
                f'New fee: KES {new_fee}. Reason: {reason}'
            )
            return redirect('transport:assignment_detail', assignment_id=assignment.id)
    else:
        form = TransportDiscountForm(initial={'assignment_id': assignment_id})

    context = {'form': form, 'assignment': assignment}
    return render(request, 'transport/apply_discount.html', context)

# Reporting and Analytics Views
@login_required
def transport_reports(request):
    """Generate various transport reports."""
    if request.method == 'POST':
        form = TransportReportForm(request.POST)
        if form.is_valid():
            report_type = form.cleaned_data['report_type']
            start_date = form.cleaned_data['start_date']
            end_date = form.cleaned_data['end_date']
            route = form.cleaned_data.get('route')
            vehicle = form.cleaned_data.get('vehicle')

            # Generate report based on type
            if report_type == 'assignments':
                return generate_assignments_report(request, start_date, end_date, route, vehicle)
            elif report_type == 'payments':
                return generate_payments_report(request, start_date, end_date, route, vehicle)
            elif report_type == 'routes':
                return generate_routes_report(request, start_date, end_date)
            elif report_type == 'vehicles':
                return generate_vehicles_report(request, start_date, end_date)
            elif report_type == 'overdue':
                return generate_overdue_report(request, start_date, end_date, route, vehicle)
    else:
        form = TransportReportForm(initial={
            'start_date': date.today() - timedelta(days=30),
            'end_date': date.today()
        })

    context = {'form': form}
    return render(request, 'transport/reports.html', context)

@login_required
def transport_statistics(request):
    """Display transport statistics and analytics."""
    # Basic statistics
    total_routes = TransportRoute.objects.count()
    total_vehicles = TransportVehicle.objects.count()
    active_assignments = TransportAssignment.objects.filter(status='active').count()
    total_revenue = TransportAssignment.objects.filter(status='active').aggregate(
        total=Sum('fee')
    )['total'] or 0

    # Route utilization
    route_stats = TransportRoute.objects.annotate(
        student_count=Count('transportassignment', filter=Q(transportassignment__status='active')),
        revenue=Sum('transportassignment__fee', filter=Q(transportassignment__status='active'))
    ).order_by('-student_count')

    # Vehicle utilization
    vehicle_stats = TransportVehicle.objects.annotate(
        assigned_students=Count('transportassignment', filter=Q(transportassignment__status='active')),
        utilization_rate=Count('transportassignment', filter=Q(transportassignment__status='active')) * 100.0 / F('capacity')
    ).filter(status='active').order_by('-utilization_rate')

    # Monthly trends (last 12 months)
    from django.db.models.functions import TruncMonth
    monthly_stats = TransportAssignment.objects.filter(
        start_date__gte=date.today() - timedelta(days=365)
    ).annotate(
        month=TruncMonth('start_date')
    ).values('month').annotate(
        assignments=Count('id'),
        revenue=Sum('fee')
    ).order_by('-month')[:12]

    context = {
        'total_routes': total_routes,
        'total_vehicles': total_vehicles,
        'active_assignments': active_assignments,
        'total_revenue': total_revenue,
        'route_stats': route_stats,
        'vehicle_stats': vehicle_stats,
        'monthly_stats': monthly_stats,
    }
    return render(request, 'transport/statistics.html', context)

# Report Generation Helper Functions
def generate_assignments_report(request, start_date, end_date, route=None, vehicle=None):
    """Generate CSV report for transport assignments."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="transport_assignments.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Student Name', 'Index Number', 'Route', 'Vehicle', 'Stop',
        'Fee', 'Start Date', 'End Date', 'Status'
    ])

    assignments = TransportAssignment.objects.filter(
        start_date__range=[start_date, end_date]
    ).select_related('student', 'route', 'vehicle', 'stop')

    if route:
        assignments = assignments.filter(route=route)
    if vehicle:
        assignments = assignments.filter(vehicle=vehicle)

    for assignment in assignments:
        writer.writerow([
            assignment.student.full_name,
            assignment.student.index_number,
            assignment.route.name,
            assignment.vehicle.name,
            assignment.stop.name,
            assignment.fee,
            assignment.start_date,
            assignment.end_date or '',
            assignment.status,
        ])

    return response

def generate_payments_report(request, start_date, end_date, route=None, vehicle=None):
    """Generate CSV report for transport fee payments."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="transport_payments.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Student Name', 'Index Number', 'Route', 'Vehicle',
        'Fee Amount', 'Payment Date', 'Status'
    ])

    # This would integrate with your payment system
    # For now, we'll show active assignments as "paid"
    assignments = TransportAssignment.objects.filter(
        status='active',
        start_date__range=[start_date, end_date]
    ).select_related('student', 'route', 'vehicle')

    if route:
        assignments = assignments.filter(route=route)
    if vehicle:
        assignments = assignments.filter(vehicle=vehicle)

    for assignment in assignments:
        writer.writerow([
            assignment.student.full_name,
            assignment.student.index_number,
            assignment.route.name,
            assignment.vehicle.name,
            assignment.fee,
            assignment.start_date,
            'Paid',
        ])

    return response

def generate_routes_report(request, start_date, end_date):
    """Generate CSV report for route utilization."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="route_utilization.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Route Name', 'Distance (km)', 'Estimated Time (min)',
        'Fee', 'Active Students', 'Total Revenue'
    ])

    routes = TransportRoute.objects.annotate(
        student_count=Count('transportassignment', filter=Q(transportassignment__status='active')),
        revenue=Sum('transportassignment__fee', filter=Q(transportassignment__status='active'))
    )

    for route in routes:
        writer.writerow([
            route.name,
            route.distance,
            route.estimated_time,
            route.fee,
            route.student_count,
            route.revenue or 0,
        ])

    return response

def generate_vehicles_report(request, start_date, end_date):
    """Generate CSV report for vehicle usage."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="vehicle_usage.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Vehicle Name', 'Registration', 'Type', 'Capacity',
        'Assigned Students', 'Utilization %', 'Driver', 'Status'
    ])

    vehicles = TransportVehicle.objects.annotate(
        assigned_students=Count('transportassignment', filter=Q(transportassignment__status='active'))
    )

    for vehicle in vehicles:
        utilization = (vehicle.assigned_students / vehicle.capacity * 100) if vehicle.capacity > 0 else 0
        writer.writerow([
            vehicle.name,
            vehicle.registration_number,
            vehicle.get_vehicle_type_display(),
            vehicle.capacity,
            vehicle.assigned_students,
            f"{utilization:.1f}%",
            vehicle.driver_name,
            vehicle.get_status_display(),
        ])

    return response

def generate_overdue_report(request, start_date, end_date, route=None, vehicle=None):
    """Generate CSV report for overdue payments."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="overdue_payments.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Student Name', 'Index Number', 'Route', 'Vehicle',
        'Fee Amount', 'Due Date', 'Days Overdue'
    ])

    # This would integrate with your payment tracking system
    # For now, we'll show assignments that started more than 30 days ago as potentially overdue
    overdue_date = date.today() - timedelta(days=30)
    assignments = TransportAssignment.objects.filter(
        status='active',
        start_date__lt=overdue_date
    ).select_related('student', 'route', 'vehicle')

    if route:
        assignments = assignments.filter(route=route)
    if vehicle:
        assignments = assignments.filter(vehicle=vehicle)

    for assignment in assignments:
        days_overdue = (date.today() - assignment.start_date).days - 30
        writer.writerow([
            assignment.student.full_name,
            assignment.student.index_number,
            assignment.route.name,
            assignment.vehicle.name,
            assignment.fee,
            assignment.start_date + timedelta(days=30),
            days_overdue,
        ])

    return response

# AJAX Views for Dynamic Forms
@login_required
def get_route_stops(request, route_id):
    """Get stops for a specific route (AJAX endpoint)."""
    stops = TransportStop.objects.filter(route_id=route_id).order_by('sequence')
    stops_data = [{'id': stop.id, 'name': stop.name, 'sequence': stop.sequence} for stop in stops]
    return JsonResponse({'stops': stops_data})

@login_required
def get_vehicle_capacity(request, vehicle_id):
    """Get vehicle capacity and current utilization (AJAX endpoint)."""
    vehicle = get_object_or_404(TransportVehicle, id=vehicle_id)
    current_assignments = TransportAssignment.objects.filter(
        vehicle=vehicle, status='active'
    ).count()

    return JsonResponse({
        'capacity': vehicle.capacity,
        'current_assignments': current_assignments,
        'available_seats': vehicle.capacity - current_assignments
    })
