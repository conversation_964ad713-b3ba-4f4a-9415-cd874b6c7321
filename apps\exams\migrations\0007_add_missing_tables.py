# Generated manually to add missing tables

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0006_add_exam_type_id'),
        ('academics', '0002_initial'),
        ('students', '0001_initial'),
        ('teachers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # Create ExamType table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_examtype (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) UNIQUE NOT NULL,
                category VARCHAR(30) NOT NULL,
                description TEXT,
                default_duration_minutes INTEGER DEFAULT 120,
                default_total_marks INTEGER DEFAULT 100,
                weight_percentage DECIMAL(5,2) DEFAULT 100.00,
                requires_timetable BOOLEAN DEFAULT 1,
                allows_retake BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_examtype;"
        ),
        
        # Create ClassRanking table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_classranking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ranking_criteria VARCHAR(20) DEFAULT 'overall_average',
                include_absent_students BOOLEAN DEFAULT 0,
                minimum_subjects_required INTEGER DEFAULT 1,
                total_students_ranked INTEGER NOT NULL,
                highest_score DECIMAL(6,2) NOT NULL,
                lowest_score DECIMAL(6,2) NOT NULL,
                class_average DECIMAL(6,2) NOT NULL,
                generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                generated_by_id INTEGER,
                grade_id INTEGER NOT NULL,
                exam_id INTEGER NOT NULL,
                FOREIGN KEY (generated_by_id) REFERENCES auth_user(id),
                FOREIGN KEY (grade_id) REFERENCES academics_grade(id),
                FOREIGN KEY (exam_id) REFERENCES exams_exam(id),
                UNIQUE(exam_id, grade_id, ranking_criteria)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_classranking;"
        ),
        
        # Create StudentRanking table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_studentranking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position INTEGER NOT NULL,
                score DECIMAL(6,2) NOT NULL,
                percentile DECIMAL(5,2) NOT NULL,
                subjects_count INTEGER NOT NULL,
                average_score DECIMAL(5,2) NOT NULL,
                improvement_from_previous DECIMAL(6,2),
                class_ranking_id INTEGER NOT NULL,
                student_id INTEGER NOT NULL,
                overall_performance_id INTEGER NOT NULL,
                FOREIGN KEY (class_ranking_id) REFERENCES exams_classranking(id),
                FOREIGN KEY (student_id) REFERENCES students_student(id),
                FOREIGN KEY (overall_performance_id) REFERENCES exams_studentoverallperformance(id),
                UNIQUE(class_ranking_id, student_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_studentranking;"
        ),
        
        # Create GradingSystem table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_gradingsystem (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) UNIQUE NOT NULL,
                system_type VARCHAR(20) NOT NULL,
                description TEXT,
                is_default BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                use_weighted_average BOOLEAN DEFAULT 0,
                round_to_decimal_places INTEGER DEFAULT 2,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_gradingsystem;"
        ),
        
        # Create GradeScale table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_gradescale (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                letter_grade VARCHAR(5) NOT NULL,
                grade_point DECIMAL(4,2) NOT NULL,
                min_percentage DECIMAL(5,2) NOT NULL,
                max_percentage DECIMAL(5,2) NOT NULL,
                description VARCHAR(100),
                remarks VARCHAR(255),
                is_passing_grade BOOLEAN DEFAULT 1,
                color_code VARCHAR(7) DEFAULT '#3B82F6',
                grading_system_id INTEGER NOT NULL,
                FOREIGN KEY (grading_system_id) REFERENCES exams_gradingsystem(id),
                UNIQUE(grading_system_id, letter_grade)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_gradescale;"
        ),
        
        # Create EnhancedStudentExamResult table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_enhancedstudentexamresult (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marks_obtained DECIMAL(6,2) NOT NULL,
                total_marks DECIMAL(6,2) NOT NULL,
                percentage DECIMAL(5,2) NOT NULL,
                letter_grade VARCHAR(5),
                grade_point DECIMAL(4,2),
                subject_position INTEGER,
                grade_position INTEGER,
                percentile DECIMAL(5,2),
                attendance_status VARCHAR(20) DEFAULT 'present',
                start_time TIME,
                submission_time TIME,
                time_taken_minutes INTEGER,
                status VARCHAR(20) DEFAULT 'draft',
                is_verified BOOLEAN DEFAULT 0,
                verification_notes TEXT,
                marked_date DATETIME,
                verified_date DATETIME,
                teacher_remarks TEXT,
                special_considerations TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                marked_by_id INTEGER,
                verified_by_id INTEGER,
                student_id INTEGER NOT NULL,
                exam_id INTEGER NOT NULL,
                timetable_entry_id INTEGER NOT NULL,
                FOREIGN KEY (marked_by_id) REFERENCES auth_user(id),
                FOREIGN KEY (verified_by_id) REFERENCES auth_user(id),
                FOREIGN KEY (student_id) REFERENCES students_student(id),
                FOREIGN KEY (exam_id) REFERENCES exams_exam(id),
                FOREIGN KEY (timetable_entry_id) REFERENCES exams_examtimetable(id),
                UNIQUE(student_id, exam_id, timetable_entry_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_enhancedstudentexamresult;"
        ),
        
        # Create StudentOverallPerformance table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_studentoverallperformance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_subjects INTEGER NOT NULL,
                subjects_passed INTEGER NOT NULL,
                subjects_failed INTEGER NOT NULL,
                total_marks_obtained DECIMAL(8,2) NOT NULL,
                total_marks_possible DECIMAL(8,2) NOT NULL,
                overall_percentage DECIMAL(5,2) NOT NULL,
                overall_grade VARCHAR(5),
                overall_gpa DECIMAL(4,2),
                class_position INTEGER,
                grade_position INTEGER,
                school_position INTEGER,
                total_students_in_class INTEGER DEFAULT 0,
                total_students_in_grade INTEGER DEFAULT 0,
                highest_subject_score DECIMAL(5,2),
                lowest_subject_score DECIMAL(5,2),
                previous_exam_percentage DECIMAL(5,2),
                percentage_change DECIMAL(6,2),
                performance_trend VARCHAR(20),
                attendance_percentage DECIMAL(5,2),
                class_teacher_remarks TEXT,
                principal_remarks TEXT,
                recommendations TEXT,
                is_promoted BOOLEAN,
                promotion_status VARCHAR(100),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                best_subject_id INTEGER,
                weakest_subject_id INTEGER,
                exam_id INTEGER NOT NULL,
                student_id INTEGER NOT NULL,
                FOREIGN KEY (best_subject_id) REFERENCES academics_subject(id),
                FOREIGN KEY (weakest_subject_id) REFERENCES academics_subject(id),
                FOREIGN KEY (exam_id) REFERENCES exams_exam(id),
                FOREIGN KEY (student_id) REFERENCES students_student(id),
                UNIQUE(student_id, exam_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_studentoverallperformance;"
        ),
        
        # Create SubjectPerformanceAnalysis table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_subjectperformanceanalysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_students INTEGER NOT NULL,
                students_present INTEGER NOT NULL,
                students_passed INTEGER NOT NULL,
                students_failed INTEGER NOT NULL,
                highest_score DECIMAL(5,2) NOT NULL,
                lowest_score DECIMAL(5,2) NOT NULL,
                average_score DECIMAL(5,2) NOT NULL,
                median_score DECIMAL(5,2),
                standard_deviation DECIMAL(6,3),
                grade_a_count INTEGER DEFAULT 0,
                grade_b_count INTEGER DEFAULT 0,
                grade_c_count INTEGER DEFAULT 0,
                grade_d_count INTEGER DEFAULT 0,
                grade_f_count INTEGER DEFAULT 0,
                pass_rate DECIMAL(5,2) NOT NULL,
                attendance_rate DECIMAL(5,2) NOT NULL,
                difficulty_index DECIMAL(5,2),
                teacher_remarks TEXT,
                improvement_suggestions TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                exam_id INTEGER NOT NULL,
                subject_id INTEGER NOT NULL,
                grade_id INTEGER NOT NULL,
                teacher_id INTEGER,
                FOREIGN KEY (exam_id) REFERENCES exams_exam(id),
                FOREIGN KEY (subject_id) REFERENCES academics_subject(id),
                FOREIGN KEY (grade_id) REFERENCES academics_grade(id),
                FOREIGN KEY (teacher_id) REFERENCES teachers_teacher(id),
                UNIQUE(exam_id, subject_id, grade_id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_subjectperformanceanalysis;"
        ),
        
        # Create ReportCard table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_reportcard (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_number VARCHAR(100) UNIQUE NOT NULL,
                report_type VARCHAR(20) DEFAULT 'term_report',
                academic_year INTEGER NOT NULL,
                term INTEGER NOT NULL,
                include_subject_details BOOLEAN DEFAULT 1,
                include_attendance BOOLEAN DEFAULT 1,
                include_conduct_grades BOOLEAN DEFAULT 1,
                include_teacher_remarks BOOLEAN DEFAULT 1,
                include_recommendations BOOLEAN DEFAULT 1,
                status VARCHAR(20) DEFAULT 'draft',
                generated_at DATETIME,
                approved_at DATETIME,
                distributed_at DATETIME,
                pdf_file VARCHAR(100),
                file_size INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                approved_by_id INTEGER,
                generated_by_id INTEGER,
                student_id INTEGER NOT NULL,
                exam_id INTEGER NOT NULL,
                overall_performance_id INTEGER NOT NULL,
                FOREIGN KEY (approved_by_id) REFERENCES auth_user(id),
                FOREIGN KEY (generated_by_id) REFERENCES auth_user(id),
                FOREIGN KEY (student_id) REFERENCES students_student(id),
                FOREIGN KEY (exam_id) REFERENCES exams_exam(id),
                FOREIGN KEY (overall_performance_id) REFERENCES exams_studentoverallperformance(id),
                UNIQUE(student_id, exam_id, report_type)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_reportcard;"
        ),
        
        # Create PerformanceComparison table if it doesn't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS exams_performancecomparison (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comparison_name VARCHAR(200) NOT NULL,
                comparison_type VARCHAR(20) NOT NULL,
                description TEXT,
                total_students_analyzed INTEGER DEFAULT 0,
                improvement_count INTEGER DEFAULT 0,
                decline_count INTEGER DEFAULT 0,
                stable_count INTEGER DEFAULT 0,
                average_improvement DECIMAL(6,2),
                highest_improvement DECIMAL(6,2),
                highest_decline DECIMAL(6,2),
                analysis_report TEXT,
                recommendations TEXT,
                analyzed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                analyzed_by_id INTEGER,
                FOREIGN KEY (analyzed_by_id) REFERENCES auth_user(id)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS exams_performancecomparison;"
        ),
    ]
