from django.db import models

# Create your models here.

class Timetable(models.Model):
    id = models.AutoField(primary_key=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    day = models.CharField(max_length=255)
    subject = models.Foreign<PERSON>ey('academics.Subject', on_delete=models.CASCADE)
    teacher = models.ForeignKey('teachers.Teacher', on_delete=models.CASCADE)
    classroom = models.ForeignKey('academics.ClassRoom', on_delete=models.CASCADE)
    start_time = models.DecimalField(max_digits=11, decimal_places=2)
    end_time = models.DecimalField(max_digits=11, decimal_places=2)
    period_number = models.IntegerField(null=True, blank=True)
    is_break = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.grade.name} - {self.day}"