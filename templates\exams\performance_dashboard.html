{% extends 'exams/base.html' %}

{% block exam_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Performance Analytics</h1>
                    <p class="text-sm text-gray-600 mt-1">Comprehensive analysis of exam performance across the school</p>
                </div>
                <div class="flex space-x-3">
                    <select id="examFilter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Exams</option>
                        {% for exam in recent_exams %}
                        <option value="{{ exam.id }}" {% if exam.id == selected_exam_id %}selected{% endif %}>
                            {{ exam.name }}
                        </option>
                        {% endfor %}
                    </select>
                    <button onclick="exportReport()" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-download mr-2"></i>Export Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">School Average</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ school_average|floatformat:1 }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-trophy text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Top Performer</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ top_performer.percentage|floatformat:1 }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pass Rate</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ pass_rate|floatformat:1 }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-star text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Excellence Rate</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ excellence_rate|floatformat:1 }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Grade Performance Chart -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Performance by Grade</h2>
            </div>
            <div class="p-6">
                <canvas id="gradePerformanceChart" width="400" height="300"></canvas>
            </div>
        </div>

        <!-- Subject Performance Chart -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Performance by Subject</h2>
            </div>
            <div class="p-6">
                <canvas id="subjectPerformanceChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Performance Trends -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Performance Trends</h2>
        </div>
        <div class="p-6">
            <canvas id="trendsChart" width="800" height="400"></canvas>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Top Students -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Top Performing Students</h2>
            </div>
            <div class="overflow-hidden">
                <ul class="divide-y divide-gray-200">
                    {% for student in top_students %}
                    <li class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-blue-800">{{ forloop.counter }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ student.student.full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ student.student.current_grade.name }}</div>
                                </div>
                            </div>
                            <div class="text-sm font-medium text-gray-900">
                                {{ student.average_percentage|floatformat:1 }}%
                            </div>
                        </div>
                    </li>
                    {% empty %}
                    <li class="px-6 py-4 text-center text-sm text-gray-500">
                        No performance data available.
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <!-- Top Subjects -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Best Performing Subjects</h2>
            </div>
            <div class="overflow-hidden">
                <ul class="divide-y divide-gray-200">
                    {% for subject in top_subjects %}
                    <li class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-green-800">{{ forloop.counter }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ subject.subject.name }}</div>
                                    <div class="text-sm text-gray-500">{{ subject.total_students }} students</div>
                                </div>
                            </div>
                            <div class="text-sm font-medium text-gray-900">
                                {{ subject.average_percentage|floatformat:1 }}%
                            </div>
                        </div>
                    </li>
                    {% empty %}
                    <li class="px-6 py-4 text-center text-sm text-gray-500">
                        No subject data available.
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>

    <!-- Grade Analysis Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Detailed Grade Analysis</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pass Rate</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excellence Rate</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Improvement</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for grade_data in grade_analysis %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ grade_data.grade.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ grade_data.total_students }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ grade_data.average_percentage|floatformat:1 }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ grade_data.pass_rate|floatformat:1 }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ grade_data.excellence_rate|floatformat:1 }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if grade_data.improvement > 0 %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-arrow-up mr-1"></i>{{ grade_data.improvement|floatformat:1 }}%
                                </span>
                            {% elif grade_data.improvement < 0 %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-arrow-down mr-1"></i>{{ grade_data.improvement|floatformat:1 }}%
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    No change
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'exams:class_report' selected_exam_id grade_data.grade.id %}" 
                               class="text-blue-600 hover:text-blue-900">View Report</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                            No grade analysis data available.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Grade Performance Chart
const gradeCtx = document.getElementById('gradePerformanceChart').getContext('2d');
const gradeChart = new Chart(gradeCtx, {
    type: 'bar',
    data: {
        labels: {{ grade_labels|safe }},
        datasets: [{
            label: 'Average Performance (%)',
            data: {{ grade_averages|safe }},
            backgroundColor: 'rgba(59, 130, 246, 0.5)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// Subject Performance Chart
const subjectCtx = document.getElementById('subjectPerformanceChart').getContext('2d');
const subjectChart = new Chart(subjectCtx, {
    type: 'doughnut',
    data: {
        labels: {{ subject_labels|safe }},
        datasets: [{
            data: {{ subject_averages|safe }},
            backgroundColor: [
                '#EF4444', '#F97316', '#EAB308', '#22C55E', '#3B82F6', '#8B5CF6', '#EC4899'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Trends Chart
const trendsCtx = document.getElementById('trendsChart').getContext('2d');
const trendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: {{ trend_labels|safe }},
        datasets: [{
            label: 'School Average',
            data: {{ trend_data|safe }},
            borderColor: 'rgba(59, 130, 246, 1)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// Filter functionality
document.getElementById('examFilter').addEventListener('change', function() {
    const examId = this.value;
    const url = new URL(window.location);
    if (examId) {
        url.searchParams.set('exam', examId);
    } else {
        url.searchParams.delete('exam');
    }
    window.location.href = url.toString();
});

// Export functionality
function exportReport() {
    const examId = document.getElementById('examFilter').value;
    let url = '{% url "exams:performance_dashboard" %}export/';
    if (examId) {
        url += `?exam=${examId}`;
    }
    window.open(url, '_blank');
}
</script>
{% endblock %}
