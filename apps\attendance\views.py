from django.shortcuts import render, get_object_or_404, redirect, redirect
from django.core.paginator import Paginator
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q, Count, Avg, Sum
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from datetime import date, timedelta, datetime
import csv
import json
from reportlab.lib.pagesizes import letter, landscape
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle
import io

from .forms import (
    StudentAttendanceForm, TeacherAttendanceForm, BulkAttendanceForm,
    ClassAttendanceForm, AttendanceSearchForm, AttendanceReportForm,
    AttendanceAnalyticsForm, BulkAttendanceImportForm
)
from .models import (
    StudentAttendance, TeacherAttendance, AttendancePeriod,
    AttendanceStatistics, AttendanceAlert, BulkAttendanceSession, AttendanceReport
)
from apps.students.models import Student
from apps.teachers.models import Teacher
from apps.academics.models import Grade

# Create your views here.

@login_required
def manage_attendance_periods(request):
    if request.method == 'POST':
        period_id = request.POST.get('id')
        if period_id:
            period = get_object_or_404(AttendancePeriod, id=period_id)
            period.name = request.POST.get('name')
            period.start_date = request.POST.get('start_date')
            period.end_date = request.POST.get('end_date')
            period.is_active = request.POST.get('is_active') == 'on'
            period.save()
            return redirect('attendance:manage_periods')
        else:
            AttendancePeriod.objects.create(
                name=request.POST.get('name'),
                start_date=request.POST.get('start_date'),
                end_date=request.POST.get('end_date'),
                is_active=request.POST.get('is_active') == 'on'
            )
            return redirect('attendance:manage_periods')

    periods = AttendancePeriod.objects.all().order_by('-start_date')
    return render(request, 'attendance/manage_periods.html', {'periods': periods})

@login_required
def student_attendance_entry(request):
    if request.method == 'POST':
        form = StudentAttendanceForm(request.POST)
        if form.is_valid():
            attendance = form.save(commit=False)
            if attendance.status == 'absent' and not attendance.notified:
                # Send notification for absence
                send_absence_notification(attendance)
                attendance.notified = True
            attendance.save()
            return HttpResponse('Attendance recorded successfully')
    else:
        form = StudentAttendanceForm()
    return render(request, 'attendance/student_attendance_entry.html', {'form': form})

@login_required
def bulk_student_attendance_entry(request):
    if request.method == 'POST':
        form = BulkAttendanceForm(request.POST, request.FILES)
        if form.is_valid():
            records = form.handle_upload()
            return HttpResponse(f'{records} records processed successfully')
    else:
        form = BulkAttendanceForm()
    return render(request, 'attendance/bulk_student_attendance_entry.html', {'form': form})

@login_required
def teacher_attendance_entry(request):
    if request.method == 'POST':
        form = TeacherAttendanceForm(request.POST)
        if form.is_valid():
            attendance = form.save(commit=False)
            if attendance.status == 'absent' and not attendance.notified:
                # Send notification for absence
                send_absence_notification(attendance)
                attendance.notified = True
            attendance.save()
            return HttpResponse('Attendance recorded successfully')
    else:
        form = TeacherAttendanceForm()
    return render(request, 'attendance/teacher_attendance_entry.html', {'form': form})

@login_required
def student_attendance_history(request, student_id=None):
    from apps.students.models import Student
    student = None
    if student_id:
        student = Student.objects.get(pk=student_id)
        attendance_list = StudentAttendance.objects.filter(student=student).order_by('-date')
    else:
        attendance_list = StudentAttendance.objects.all().order_by('-date')
    paginator = Paginator(attendance_list, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'attendance/student_attendance_history.html', {'page_obj': page_obj, 'student': student})

@login_required
def class_attendance_history(request, grade_id=None):
    from apps.academics.models import Grade
    grade = None
    if grade_id:
        grade = Grade.objects.get(pk=grade_id)
        students = grade.student_set.all()
        attendance_list = StudentAttendance.objects.filter(student__in=students).order_by('-date')
    else:
        attendance_list = StudentAttendance.objects.all().order_by('-date')
    paginator = Paginator(attendance_list, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'attendance/class_attendance_history.html', {'page_obj': page_obj, 'grade': grade})

@login_required
def teacher_attendance_history(request, teacher_id=None):
    from apps.teachers.models import Teacher
    teacher = None
    if teacher_id:
        teacher = Teacher.objects.get(pk=teacher_id)
        attendance_list = TeacherAttendance.objects.filter(teacher=teacher).order_by('-date')
    else:
        attendance_list = TeacherAttendance.objects.all().order_by('-date')
    paginator = Paginator(attendance_list, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'attendance/teacher_attendance_history.html', {'page_obj': page_obj, 'teacher': teacher})

@login_required
def export_student_attendance_csv(request, student_id=None):
    from apps.students.models import Student
    student = None
    if student_id:
        student = Student.objects.get(pk=student_id)
        attendance_list = StudentAttendance.objects.filter(student=student).order_by('-date')
    else:
        attendance_list = StudentAttendance.objects.all().order_by('-date')

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename={"student_attendance" + ("_" + str(student_id) if student_id else "") + ".csv"}'

    writer = csv.writer(response)
    writer.writerow(['Student', 'Date', 'Status', 'Reason', 'Recorded By', 'Period'])

    for att in attendance_list:
        writer.writerow([
            str(att.student),
            str(att.date),
            att.get_status_display(),
            att.reason or '',
            str(att.recorded_by or ''),
            str(att.period or '')
        ])

    return response

@login_required
def export_class_attendance_csv(request, grade_id=None):
    from apps.academics.models import Grade
    grade = None
    if grade_id:
        grade = Grade.objects.get(pk=grade_id)
        students = grade.student_set.all()
        attendance_list = StudentAttendance.objects.filter(student__in=students).order_by('-date')
    else:
        attendance_list = StudentAttendance.objects.all().order_by('-date')

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename={"class_attendance" + ("_" + str(grade_id) if grade_id else "") + ".csv"}'

    writer = csv.writer(response)
    writer.writerow(['Student', 'Date', 'Status', 'Reason', 'Recorded By', 'Period'])

    for att in attendance_list:
        writer.writerow([
            str(att.student),
            str(att.date),
            att.get_status_display(),
            att.reason or '',
            str(att.recorded_by or ''),
            str(att.period or '')
        ])

    return response

@login_required
def export_teacher_attendance_csv(request, teacher_id=None):
    from apps.teachers.models import Teacher
    teacher = None
    if teacher_id:
        teacher = Teacher.objects.get(pk=teacher_id)
        attendance_list = TeacherAttendance.objects.filter(teacher=teacher).order_by('-date')
    else:
        attendance_list = TeacherAttendance.objects.all().order_by('-date')

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename={"teacher_attendance" + ("_" + str(teacher_id) if teacher_id else "") + ".csv"}'

    writer = csv.writer(response)
    writer.writerow(['Teacher', 'Date', 'Status', 'Reason', 'Recorded By', 'Period'])

    for att in attendance_list:
        writer.writerow([
            str(att.teacher),
            str(att.date),
            att.get_status_display(),
            att.reason or '',
            str(att.recorded_by or ''),
            str(att.period or '')
        ])

    return response

@login_required
def export_student_attendance_pdf(request, student_id=None):
    from apps.students.models import Student
    student = None
    if student_id:
        student = Student.objects.get(pk=student_id)
        attendance_list = StudentAttendance.objects.filter(student=student).order_by('-date')
    else:
        attendance_list = StudentAttendance.objects.all().order_by('-date')
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer, pagesize=landscape(letter))
    data = [['Student', 'Date', 'Status', 'Reason', 'Recorded By', 'Period']]
    for att in attendance_list:
        data.append([str(att.student), str(att.date), att.get_status_display(), att.reason or '', str(att.recorded_by or ''), str(att.period or '')])
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    table.wrapOn(p, 800, 600)
    table.drawOn(p, 30, 500 - 20 * len(data))
    p.save()
    buffer.seek(0)
    response = HttpResponse(buffer, content_type='application/pdf')
    filename = f"student_attendance{'_' + str(student_id) if student_id else ''}.pdf"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

@login_required
def export_class_attendance_pdf(request, grade_id=None):
    from apps.academics.models import Grade
    grade = None
    if grade_id:
        grade = Grade.objects.get(pk=grade_id)
        students = grade.student_set.all()
        attendance_list = StudentAttendance.objects.filter(student__in=students).order_by('-date')
    else:
        attendance_list = StudentAttendance.objects.all().order_by('-date')
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer, pagesize=landscape(letter))
    data = [['Student', 'Date', 'Status', 'Reason', 'Recorded By', 'Period']]
    for att in attendance_list:
        data.append([str(att.student), str(att.date), att.get_status_display(), att.reason or '', str(att.recorded_by or ''), str(att.period or '')])
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    table.wrapOn(p, 800, 600)
    table.drawOn(p, 30, 500 - 20 * len(data))
    p.save()
    buffer.seek(0)
    response = HttpResponse(buffer, content_type='application/pdf')
    filename = f"class_attendance{'_' + str(grade_id) if grade_id else ''}.pdf"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

@login_required
def export_teacher_attendance_pdf(request, teacher_id=None):
    from apps.teachers.models import Teacher
    teacher = None
    if teacher_id:
        teacher = Teacher.objects.get(pk=teacher_id)
        attendance_list = TeacherAttendance.objects.filter(teacher=teacher).order_by('-date')
    else:
        attendance_list = TeacherAttendance.objects.all().order_by('-date')
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer, pagesize=landscape(letter))
    data = [['Teacher', 'Date', 'Status', 'Reason', 'Recorded By', 'Period']]
    for att in attendance_list:
        data.append([str(att.teacher), str(att.date), att.get_status_display(), att.reason or '', str(att.recorded_by or ''), str(att.period or '')])
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    table.wrapOn(p, 800, 600)
    table.drawOn(p, 30, 500 - 20 * len(data))
    p.save()
    buffer.seek(0)
    response = HttpResponse(buffer, content_type='application/pdf')
    filename = f"teacher_attendance{'_' + str(teacher_id) if teacher_id else ''}.pdf"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

def send_absence_notification(attendance):
    # This is a placeholder function for sending notifications
    # In a real implementation, this would send an email or push notification
    # to the student's/teacher's parent/guardian or the student/teacher themselves
    if hasattr(attendance, 'student'):
        recipient = attendance.student.email
        subject = "Absence Notification"
        message = f"{attendance.student.first_name} {attendance.student.last_name} was marked absent on {attendance.date}. Reason: {attendance.reason or 'No reason provided'}"
    elif hasattr(attendance, 'teacher'):
        recipient = attendance.teacher.email
        subject = "Absence Notification"
        message = f"{attendance.teacher.first_name} {attendance.teacher.last_name} was marked absent on {attendance.date}. Reason: {attendance.reason or 'No reason provided'}"

    # Send email (placeholder)
    print(f"Sending email to {recipient}: {subject} - {message}")


@login_required
def attendance_dashboard(request):
    """
    Enhanced attendance dashboard with analytics
    """
    today = date.today()

    # Today's statistics
    today_student_attendance = StudentAttendance.objects.filter(date=today)
    today_teacher_attendance = TeacherAttendance.objects.filter(date=today)

    # Calculate statistics
    total_students = Student.objects.filter(status='active').count()
    total_teachers = Teacher.objects.filter(status='active').count()

    students_present_today = today_student_attendance.filter(status__in=['present', 'late']).count()
    teachers_present_today = today_teacher_attendance.filter(status__in=['present', 'late']).count()

    student_attendance_rate = (students_present_today / total_students * 100) if total_students > 0 else 0
    teacher_attendance_rate = (teachers_present_today / total_teachers * 100) if total_teachers > 0 else 0

    # Weekly trends
    week_start = today - timedelta(days=today.weekday())
    weekly_attendance = []

    for i in range(7):
        day = week_start + timedelta(days=i)
        day_attendance = StudentAttendance.objects.filter(date=day)
        present_count = day_attendance.filter(status__in=['present', 'late']).count()
        total_count = day_attendance.count()
        rate = (present_count / total_count * 100) if total_count > 0 else 0

        weekly_attendance.append({
            'date': day,
            'rate': round(rate, 1),
            'present': present_count,
            'total': total_count
        })

    # Recent alerts
    recent_alerts = AttendanceAlert.objects.filter(status='active').order_by('-triggered_date')[:5]

    # Low attendance students
    low_attendance_students = []
    for student in Student.objects.filter(status='active')[:10]:  # Limit for performance
        rate = StudentAttendance.get_attendance_rate(student, week_start, today)
        if rate < 75:  # Below 75% attendance
            low_attendance_students.append({
                'student': student,
                'rate': round(rate, 1)
            })

    context = {
        'today': today,
        'total_students': total_students,
        'total_teachers': total_teachers,
        'students_present_today': students_present_today,
        'teachers_present_today': teachers_present_today,
        'student_attendance_rate': round(student_attendance_rate, 1),
        'teacher_attendance_rate': round(teacher_attendance_rate, 1),
        'weekly_attendance': weekly_attendance,
        'recent_alerts': recent_alerts,
        'low_attendance_students': low_attendance_students,
    }

    return render(request, 'attendance/dashboard.html', context)


@login_required
def class_attendance_marking(request):
    """
    Class-wise attendance marking interface
    """
    if request.method == 'POST':
        form = ClassAttendanceForm(request.POST)
        if form.is_valid():
            date_val = form.cleaned_data['date']
            grade = form.cleaned_data['grade']
            section = form.cleaned_data['section']
            period = form.cleaned_data['period']

            # Get students for the class
            students = Student.objects.filter(current_grade=grade, status='active')
            if section:
                students = students.filter(current_section=section)

            created_count = 0
            updated_count = 0

            for student in students:
                status_field = f'student_{student.id}'
                reason_field = f'reason_{student.id}'

                if status_field in form.cleaned_data:
                    status = form.cleaned_data[status_field]
                    reason = form.cleaned_data.get(reason_field, '')

                    attendance, created = StudentAttendance.objects.update_or_create(
                        student=student,
                        date=date_val,
                        defaults={
                            'status': status,
                            'reason': reason,
                            'period': period,
                            'recorded_by': request.user
                        }
                    )

                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

                    # Send notification for absences
                    if status == 'absent' and not attendance.notified:
                        send_absence_notification(attendance)
                        attendance.notified = True
                        attendance.save()

            messages.success(
                request,
                f'Attendance marked successfully. Created: {created_count}, Updated: {updated_count}'
            )
            return redirect('attendance:class_marking')
    else:
        form = ClassAttendanceForm()

    context = {
        'form': form,
        'title': 'Class Attendance Marking'
    }

    return render(request, 'attendance/class_marking.html', context)


@login_required
def bulk_attendance_import(request):
    """
    Bulk attendance import from CSV/Excel files
    """
    if request.method == 'POST':
        form = BulkAttendanceForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                records = form.handle_upload()
                messages.success(request, f'{records} attendance records imported successfully!')
                return redirect('attendance:dashboard')
            except Exception as e:
                messages.error(request, f'Error importing attendance: {str(e)}')
    else:
        form = BulkAttendanceForm()

    return render(request, 'attendance/bulk_import.html', {'form': form})


@login_required
def generate_attendance_report(request):
    """
    Generate attendance reports in various formats
    """
    if request.method == 'POST':
        report_type = request.POST.get('report_type')
        format_type = request.POST.get('format', 'pdf')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')

        # Generate report based on type and format
        if report_type == 'student':
            if format_type == 'csv':
                return export_student_attendance_csv(request)
            else:
                return export_student_attendance_pdf(request)
        elif report_type == 'class':
            if format_type == 'csv':
                return export_class_attendance_csv(request)
            else:
                return export_class_attendance_pdf(request)
        elif report_type == 'teacher':
            if format_type == 'csv':
                return export_teacher_attendance_csv(request)
            else:
                return export_teacher_attendance_pdf(request)

    return render(request, 'attendance/generate_report.html')


@login_required
def attendance_reports_list(request):
    """
    List of generated attendance reports
    """
    # For now, redirect to analytics page
    # In a full implementation, this would show a list of previously generated reports
    return render(request, 'attendance/reports_list.html', {
        'reports': [],  # Placeholder for actual reports
        'message': 'Report history feature coming soon!'
    })


@login_required
def attendance_analytics(request):
    """
    Advanced attendance analytics and insights
    """
    form = AttendanceAnalyticsForm(request.GET)
    analytics_data = {}

    if form.is_valid():
        analysis_type = form.cleaned_data['analysis_type']
        start_date = form.cleaned_data['start_date']
        end_date = form.cleaned_data['end_date']
        grade = form.cleaned_data.get('grade')

        # Base queryset
        student_attendance = StudentAttendance.objects.filter(
            date__range=[start_date, end_date]
        )

        if grade:
            student_attendance = student_attendance.filter(student__current_grade=grade)

        if analysis_type == 'attendance_trends':
            # Calculate daily attendance rates
            daily_rates = []
            current_date = start_date

            while current_date <= end_date:
                day_attendance = student_attendance.filter(date=current_date)
                total = day_attendance.count()
                present = day_attendance.filter(status__in=['present', 'late']).count()
                rate = (present / total * 100) if total > 0 else 0

                daily_rates.append({
                    'date': current_date,
                    'rate': round(rate, 1),
                    'total': total,
                    'present': present
                })

                current_date += timedelta(days=1)

            analytics_data['daily_rates'] = daily_rates

        elif analysis_type == 'risk_identification':
            # Identify at-risk students
            at_risk_students = []
            min_rate = form.cleaned_data.get('minimum_attendance_rate', 75)

            students = Student.objects.filter(status='active')
            if grade:
                students = students.filter(current_grade=grade)

            for student in students:
                rate = StudentAttendance.get_attendance_rate(student, start_date, end_date)
                consecutive_absences = StudentAttendance.get_consecutive_absences(student)

                if rate < min_rate or consecutive_absences >= 3:
                    at_risk_students.append({
                        'student': student,
                        'attendance_rate': round(rate, 1),
                        'consecutive_absences': consecutive_absences,
                        'risk_level': 'high' if rate < 60 or consecutive_absences >= 5 else 'medium'
                    })

            analytics_data['at_risk_students'] = sorted(
                at_risk_students,
                key=lambda x: x['attendance_rate']
            )

    context = {
        'form': form,
        'analytics_data': analytics_data,
    }

    return render(request, 'attendance/analytics.html', context)