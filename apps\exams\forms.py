from django import forms
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from decimal import Decimal
from .models import (
    ExamType, Exam, GradingSystem, GradeScale, ExamTimetable,
    EnhancedStudentExamResult, StudentOverallPerformance, SubjectPerformanceAnalysis,
    ReportCard, PerformanceComparison, ClassRanking, StudentRanking,
    ExamRangeGrade, StudentExam, StudentOverallExamResult, BulkResultUpload,
    ExamNotification
)
from apps.academics.models import Grade, Subject, ClassRoom
from apps.teachers.models import Teacher
from apps.students.models import Student

class ExamTypeForm(forms.ModelForm):
    class Meta:
        model = ExamType
        fields = [
            'name', 'category', 'description', 'default_duration_minutes',
            'default_total_marks', 'weight_percentage', 'requires_timetable',
            'allows_retake', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Exam type name'
            }),
            'category': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Exam type description'
            }),
            'default_duration_minutes': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Duration in minutes'
            }),
            'default_total_marks': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Total marks'
            }),
            'weight_percentage': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Weight percentage',
                'step': '0.01'
            }),
            'requires_timetable': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'allows_retake': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
        }

class ExamForm(forms.ModelForm):
    class Meta:
        model = Exam
        fields = [
            'name', 'exam_type', 'academic_year', 'term', 'description', 'instructions',
            'total_marks', 'pass_marks', 'duration_minutes', 'marking_scheme',
            'start_date', 'end_date', 'registration_start', 'registration_end',
            'grades', 'subjects', 'allow_calculator', 'allow_reference_materials',
            'requires_signature'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Exam name'
            }),
            'exam_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'academic_year': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Academic year (e.g., 2024)'
            }),
            'term': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Term number'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Exam description'
            }),
            'instructions': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 4,
                'placeholder': 'Exam instructions for students'
            }),
            'total_marks': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Total marks'
            }),
            'pass_marks': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Pass marks'
            }),
            'duration_minutes': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Duration in minutes'
            }),
            'marking_scheme': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'registration_start': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'registration_end': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'grades': forms.CheckboxSelectMultiple(attrs={
                'class': 'space-y-2'
            }),
            'subjects': forms.CheckboxSelectMultiple(attrs={
                'class': 'space-y-2'
            }),
            'allow_calculator': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'allow_reference_materials': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'requires_signature': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default academic year to current year
        if not self.instance.pk:
            self.fields['academic_year'].initial = date.today().year

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        registration_start = cleaned_data.get('registration_start')
        registration_end = cleaned_data.get('registration_end')
        pass_marks = cleaned_data.get('pass_marks')
        total_marks = cleaned_data.get('total_marks')

        # Validate date ranges
        if start_date and end_date and start_date > end_date:
            raise ValidationError("Start date must be before end date")

        if registration_start and registration_end and registration_start > registration_end:
            raise ValidationError("Registration start date must be before registration end date")

        if registration_end and start_date and registration_end > start_date:
            raise ValidationError("Registration must end before exam starts")

        # Validate marks
        if pass_marks and total_marks and pass_marks > total_marks:
            raise ValidationError("Pass marks cannot be greater than total marks")

        return cleaned_data

class ExamTimetableForm(forms.ModelForm):
    class Meta:
        model = ExamTimetable
        fields = [
            'exam', 'grade', 'subject', 'exam_date', 'start_time', 'end_time',
            'duration_minutes', 'session_type', 'classroom', 'chief_invigilator',
            'assistant_invigilators', 'total_marks', 'instructions',
            'special_requirements', 'materials_allowed', 'materials_provided'
        ]
        widgets = {
            'exam': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'grade': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'subject': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'exam_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'start_time': forms.TimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'time'
            }),
            'end_time': forms.TimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'time'
            }),
            'duration_minutes': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Duration in minutes'
            }),
            'session_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'classroom': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'chief_invigilator': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'assistant_invigilators': forms.CheckboxSelectMultiple(attrs={
                'class': 'space-y-2'
            }),
            'total_marks': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Total marks'
            }),
            'instructions': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Exam instructions'
            }),
            'special_requirements': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Special requirements'
            }),
            'materials_allowed': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Materials allowed'
            }),
            'materials_provided': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Materials provided'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        exam_date = cleaned_data.get('exam_date')
        exam = cleaned_data.get('exam')

        # Validate time range
        if start_time and end_time and start_time >= end_time:
            raise ValidationError("Start time must be before end time")

        # Validate exam date within exam period
        if exam_date and exam:
            if exam_date < exam.start_date or exam_date > exam.end_date:
                raise ValidationError("Exam date must be within the exam period")

        return cleaned_data

class GradingSystemForm(forms.ModelForm):
    """Form for creating and editing grading systems."""

    class Meta:
        model = GradingSystem
        fields = [
            'name', 'system_type', 'curriculum_type', 'description',
            'use_weighted_average', 'round_to_decimal_places',
            'minimum_pass_percentage', 'excellence_threshold',
            'knec_compliant', 'official_system', 'grades', 'is_default', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Enter grading system name'
            }),
            'system_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'curriculum_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Enter description'
            }),
            'round_to_decimal_places': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 0,
                'max': 4
            }),
            'minimum_pass_percentage': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 0,
                'max': 100,
                'step': 0.01
            }),
            'excellence_threshold': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 0,
                'max': 100,
                'step': 0.01
            }),
            'grades': forms.CheckboxSelectMultiple(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'use_weighted_average': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'knec_compliant': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'official_system': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'is_default': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        minimum_pass = cleaned_data.get('minimum_pass_percentage')
        excellence_threshold = cleaned_data.get('excellence_threshold')

        # Validate that excellence threshold is higher than minimum pass
        if minimum_pass and excellence_threshold and excellence_threshold <= minimum_pass:
            raise ValidationError("Excellence threshold must be higher than minimum pass percentage")

        return cleaned_data

class BulkResultUploadForm(forms.ModelForm):
    """Form for bulk result upload."""

    class Meta:
        model = BulkResultUpload
        fields = ['uploaded_file']
        widgets = {
            'uploaded_file': forms.FileInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'accept': '.xlsx,.xls,.csv'
            })
        }

    def clean_uploaded_file(self):
        file = self.cleaned_data.get('uploaded_file')
        if file:
            # Check file extension
            if not file.name.endswith(('.xlsx', '.xls', '.csv')):
                raise ValidationError("Please upload an Excel (.xlsx, .xls) or CSV (.csv) file.")

            # Check file size (max 10MB)
            if file.size > 10 * 1024 * 1024:
                raise ValidationError("File size must be less than 10MB.")

        return file

class StudentResultForm(forms.ModelForm):
    """Form for individual student result entry."""

    class Meta:
        model = EnhancedStudentExamResult
        fields = [
            'marks_obtained', 'attendance_status', 'teacher_remarks'
        ]
        widgets = {
            'marks_obtained': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 0,
                'step': 0.01,
                'placeholder': 'Enter marks'
            }),
            'attendance_status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'teacher_remarks': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Enter remarks (optional)'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.timetable_entry = kwargs.pop('timetable_entry', None)
        super().__init__(*args, **kwargs)

        if self.timetable_entry:
            # Set max value for marks based on timetable total marks
            self.fields['marks_obtained'].widget.attrs['max'] = self.timetable_entry.total_marks

    def clean_marks_obtained(self):
        marks = self.cleaned_data.get('marks_obtained')
        if marks is not None and self.timetable_entry:
            if marks > self.timetable_entry.total_marks:
                raise ValidationError(f"Marks cannot exceed {self.timetable_entry.total_marks}")
            if marks < 0:
                raise ValidationError("Marks cannot be negative")
        return marks

class ExamSearchForm(forms.Form):
    """Form for searching and filtering exams."""

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Search exams...'
        })
    )

    exam_type = forms.ModelChoiceField(
        queryset=ExamType.objects.filter(is_active=True),
        required=False,
        empty_label="All Types",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    status = forms.ChoiceField(
        choices=[('', 'All Status')] + Exam.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="All Grades",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )

class TimetableGenerationForm(forms.Form):
    """Form for generating exam timetables."""

    exam = forms.ModelChoiceField(
        queryset=Exam.objects.filter(status__in=['scheduled', 'active']),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    grades = forms.ModelMultipleChoiceField(
        queryset=Grade.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-checkbox h-4 w-4 text-blue-600'
        })
    )

    subjects = forms.ModelMultipleChoiceField(
        queryset=Subject.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-checkbox h-4 w-4 text-blue-600'
        })
    )

    start_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )

    start_time = forms.TimeField(
        initial='08:00',
        widget=forms.TimeInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'time'
        })
    )

    duration_minutes = forms.IntegerField(
        initial=120,
        min_value=30,
        max_value=300,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    break_minutes = forms.IntegerField(
        initial=30,
        min_value=0,
        max_value=120,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

class PerformanceAnalysisForm(forms.Form):
    """Form for performance analysis filters."""

    exam = forms.ModelChoiceField(
        queryset=Exam.objects.filter(status__in=['completed', 'results_published']),
        required=False,
        empty_label="All Exams",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="All Grades",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    subject = forms.ModelChoiceField(
        queryset=Subject.objects.all(),
        required=False,
        empty_label="All Subjects",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )

    analysis_type = forms.ChoiceField(
        choices=[
            ('overall', 'Overall Performance'),
            ('subject_wise', 'Subject-wise Analysis'),
            ('grade_wise', 'Grade-wise Analysis'),
            ('trend_analysis', 'Trend Analysis'),
            ('comparison', 'Comparison Analysis'),
        ],
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

class ReportGenerationForm(forms.Form):
    """Form for generating various reports."""

    REPORT_TYPES = [
        ('student_report_card', 'Student Report Card'),
        ('class_report', 'Class Performance Report'),
        ('subject_analysis', 'Subject Analysis Report'),
        ('grade_comparison', 'Grade Comparison Report'),
        ('performance_trends', 'Performance Trends Report'),
        ('administrative_summary', 'Administrative Summary'),
    ]

    report_type = forms.ChoiceField(
        choices=REPORT_TYPES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    exam = forms.ModelChoiceField(
        queryset=Exam.objects.filter(status__in=['completed', 'results_published']),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        empty_label="Select Grade (if applicable)",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    student = forms.ModelChoiceField(
        queryset=Student.objects.filter(status='active'),
        required=False,
        empty_label="Select Student (if applicable)",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    format = forms.ChoiceField(
        choices=[
            ('html', 'HTML (Web View)'),
            ('pdf', 'PDF Download'),
            ('excel', 'Excel Download'),
        ],
        initial='html',
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    include_charts = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox h-4 w-4 text-blue-600'
        })
    )

    include_comments = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox h-4 w-4 text-blue-600'
        })
    )

class NotificationForm(forms.ModelForm):
    """Form for creating exam notifications."""

    class Meta:
        model = ExamNotification
        fields = [
            'exam', 'notification_type', 'recipient_type', 'title', 'message',
            'send_email', 'send_sms', 'send_push', 'scheduled_for'
        ]
        widgets = {
            'exam': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'notification_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'recipient_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Enter notification title'
            }),
            'message': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 4,
                'placeholder': 'Enter notification message'
            }),
            'scheduled_for': forms.DateTimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'datetime-local'
            }),
            'send_email': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'send_sms': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'send_push': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
        }
