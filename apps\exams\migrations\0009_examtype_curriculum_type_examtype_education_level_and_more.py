# Generated by Django 5.2.1 on 2025-07-01 12:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("exams", "0008_add_exam_date_column"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="examtype",
            name="curriculum_type",
            field=models.CharField(
                choices=[("8-4-4", "8-4-4 System"), ("cbc", "CBC System")],
                default="8-4-4",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="examtype",
            name="education_level",
            field=models.CharField(
                choices=[
                    ("primary", "Primary (Standard 1-8)"),
                    ("secondary", "Secondary (Form 1-4)"),
                    ("cbc_primary", "CBC Primary (Grade 1-6)"),
                    ("cbc_junior", "CBC Junior Secondary (Grade 7-9)"),
                    ("cbc_senior", "CBC Senior Secondary (Grade 10-12)"),
                ],
                default="primary",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="examtype",
            name="is_national_exam",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="examtype",
            name="knec_compliant",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="gradingsystem",
            name="curriculum_type",
            field=models.CharField(
                choices=[
                    ("8-4-4", "8-4-4 System"),
                    ("cbc", "Competency Based Curriculum"),
                    ("both", "Both Systems"),
                ],
                default="8-4-4",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="gradingsystem",
            name="excellence_threshold",
            field=models.DecimalField(decimal_places=2, default=80.0, max_digits=5),
        ),
        migrations.AddField(
            model_name="gradingsystem",
            name="knec_compliant",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="gradingsystem",
            name="minimum_pass_percentage",
            field=models.DecimalField(decimal_places=2, default=40.0, max_digits=5),
        ),
        migrations.AddField(
            model_name="gradingsystem",
            name="official_system",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="examtype",
            name="category",
            field=models.CharField(
                choices=[
                    ("continuous_assessment", "Continuous Assessment Tests (CATs)"),
                    ("mid_term", "Mid-Term Examination"),
                    ("end_term", "End-Term Examination"),
                    ("end_year", "End-Year Examination"),
                    ("mock", "Mock Examination"),
                    ("national", "National Examination"),
                    ("kcpe", "Kenya Certificate of Primary Education (KCPE)"),
                    ("kcse", "Kenya Certificate of Secondary Education (KCSE)"),
                    ("entrance", "Entrance Examination"),
                    ("placement", "Placement Test"),
                    ("diagnostic", "Diagnostic Test"),
                    ("remedial", "Remedial Test"),
                    ("competency", "Competency Based Assessment"),
                ],
                max_length=30,
            ),
        ),
        migrations.AlterField(
            model_name="gradingsystem",
            name="system_type",
            field=models.CharField(
                choices=[
                    ("percentage", "Percentage System (Primary)"),
                    ("letter_grade", "Letter Grade System (Secondary)"),
                    ("gpa_4", "4-Point GPA System"),
                    ("gpa_5", "5-Point GPA System"),
                    ("points", "Points System"),
                    ("kenyan_primary", "Kenyan Primary System (0-100)"),
                    ("kenyan_secondary", "Kenyan Secondary System (A-E)"),
                    ("cbc_primary", "CBC Primary System"),
                    ("cbc_secondary", "CBC Secondary System"),
                    ("custom", "Custom System"),
                ],
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="BulkResultUpload",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("uploaded_file", models.FileField(upload_to="exam_uploads/")),
                ("original_filename", models.CharField(max_length=255)),
                ("total_records", models.IntegerField(default=0)),
                ("successful_records", models.IntegerField(default=0)),
                ("failed_records", models.IntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("partially_completed", "Partially Completed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("error_log", models.TextField(blank=True)),
                ("processing_notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bulk_uploads",
                        to="exams.exam",
                    ),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="processed_uploads",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ExamAuditLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("create", "Created"),
                            ("update", "Updated"),
                            ("delete", "Deleted"),
                            ("publish", "Published"),
                            ("unpublish", "Unpublished"),
                            ("result_entry", "Result Entry"),
                            ("result_update", "Result Update"),
                            ("result_verification", "Result Verification"),
                            ("bulk_upload", "Bulk Upload"),
                            ("report_generation", "Report Generation"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("old_values", models.JSONField(blank=True, null=True)),
                ("new_values", models.JSONField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "exam",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_logs",
                        to="exams.exam",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="ExamNotification",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("exam_created", "Exam Created"),
                            ("exam_published", "Exam Published"),
                            ("timetable_published", "Timetable Published"),
                            ("results_deadline", "Results Submission Deadline"),
                            ("results_published", "Results Published"),
                            ("report_generated", "Report Generated"),
                            ("exam_reminder", "Exam Reminder"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "recipient_type",
                    models.CharField(
                        choices=[
                            ("teacher", "Teacher"),
                            ("admin", "Administrator"),
                            ("student", "Student"),
                            ("parent", "Parent"),
                            ("all", "All Users"),
                        ],
                        max_length=10,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                ("send_email", models.BooleanField(default=True)),
                ("send_sms", models.BooleanField(default=False)),
                ("send_push", models.BooleanField(default=True)),
                ("is_sent", models.BooleanField(default=False)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("delivery_count", models.IntegerField(default=0)),
                ("scheduled_for", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="exams.exam",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
