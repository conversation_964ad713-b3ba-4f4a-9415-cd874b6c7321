from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.files.storage import default_storage
import json
import os

User = get_user_model()


class ReportTemplate(models.Model):
    """
    Model for storing report templates and configurations
    """
    REPORT_TYPES = [
        ('student_performance', 'Student Performance'),
        ('attendance_summary', 'Attendance Summary'),
        ('financial_overview', 'Financial Overview'),
        ('teacher_workload', 'Teacher Workload'),
        ('class_analytics', 'Class Analytics'),
        ('exam_results', 'Exam Results'),
        ('fee_collection', 'Fee Collection'),
        ('library_usage', 'Library Usage'),
        ('transport_utilization', 'Transport Utilization'),
        ('hostel_occupancy', 'Hostel Occupancy'),
        ('custom', 'Custom Report'),
    ]
    
    OUTPUT_FORMATS = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
        ('html', 'HTML'),
        ('json', 'JSON'),
    ]
    
    FREQUENCY_CHOICES = [
        ('manual', 'Manual'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('annually', 'Annually'),
    ]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    report_type = models.CharField(max_length=30, choices=REPORT_TYPES)
    output_format = models.CharField(max_length=10, choices=OUTPUT_FORMATS, default='pdf')
    
    # Template configuration
    template_config = models.JSONField(default=dict)
    sql_query = models.TextField(null=True, blank=True)
    chart_config = models.JSONField(default=dict)
    
    # Scheduling
    is_automated = models.BooleanField(default=False)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, default='manual')
    next_run = models.DateTimeField(null=True, blank=True)
    
    # Permissions and sharing
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_reports')
    is_public = models.BooleanField(default=False)
    allowed_users = models.ManyToManyField(User, blank=True, related_name='accessible_reports')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Report Template"
        verbose_name_plural = "Report Templates"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.report_type})"


class GeneratedReport(models.Model):
    """
    Model for storing generated report instances
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]
    
    id = models.AutoField(primary_key=True)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='generated_reports')
    title = models.CharField(max_length=255)
    
    # Generation details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    parameters = models.JSONField(default=dict)
    file_path = models.FileField(upload_to='reports/', null=True, blank=True)
    file_size = models.PositiveIntegerField(null=True, blank=True)
    
    # Statistics
    total_records = models.PositiveIntegerField(default=0)
    generation_time = models.DurationField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    
    # Access tracking
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reporting_generated_reports')
    generated_at = models.DateTimeField(auto_now_add=True)
    accessed_count = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name = "Generated Report"
        verbose_name_plural = "Generated Reports"
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.title} - {self.status}"
    
    @property
    def is_expired(self):
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class Dashboard(models.Model):
    """
    Model for custom dashboards with widgets
    """
    DASHBOARD_TYPES = [
        ('admin', 'Administrative Dashboard'),
        ('teacher', 'Teacher Dashboard'),
        ('student', 'Student Dashboard'),
        ('parent', 'Parent Dashboard'),
        ('financial', 'Financial Dashboard'),
        ('academic', 'Academic Dashboard'),
        ('custom', 'Custom Dashboard'),
    ]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    dashboard_type = models.CharField(max_length=20, choices=DASHBOARD_TYPES)
    
    # Layout configuration
    layout_config = models.JSONField(default=dict)
    refresh_interval = models.PositiveIntegerField(default=300)  # seconds
    
    # Permissions
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_dashboards')
    is_default = models.BooleanField(default=False)
    is_public = models.BooleanField(default=False)
    allowed_users = models.ManyToManyField(User, blank=True, related_name='accessible_dashboards')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Dashboard"
        verbose_name_plural = "Dashboards"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.dashboard_type})"


class DashboardWidget(models.Model):
    """
    Model for dashboard widgets
    """
    WIDGET_TYPES = [
        ('chart', 'Chart'),
        ('table', 'Table'),
        ('metric', 'Metric'),
        ('progress', 'Progress Bar'),
        ('list', 'List'),
        ('calendar', 'Calendar'),
        ('map', 'Map'),
        ('iframe', 'Embedded Content'),
    ]
    
    CHART_TYPES = [
        ('line', 'Line Chart'),
        ('bar', 'Bar Chart'),
        ('pie', 'Pie Chart'),
        ('doughnut', 'Doughnut Chart'),
        ('area', 'Area Chart'),
        ('scatter', 'Scatter Plot'),
        ('gauge', 'Gauge Chart'),
    ]
    
    id = models.AutoField(primary_key=True)
    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE, related_name='widgets')
    title = models.CharField(max_length=255)
    widget_type = models.CharField(max_length=20, choices=WIDGET_TYPES)
    chart_type = models.CharField(max_length=20, choices=CHART_TYPES, null=True, blank=True)
    
    # Position and size
    position_x = models.PositiveIntegerField(default=0)
    position_y = models.PositiveIntegerField(default=0)
    width = models.PositiveIntegerField(default=4)
    height = models.PositiveIntegerField(default=3)
    
    # Data configuration
    data_source = models.CharField(max_length=255)  # API endpoint or query
    data_config = models.JSONField(default=dict)
    chart_config = models.JSONField(default=dict)
    
    # Refresh settings
    auto_refresh = models.BooleanField(default=True)
    refresh_interval = models.PositiveIntegerField(default=300)  # seconds
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    
    class Meta:
        verbose_name = "Dashboard Widget"
        verbose_name_plural = "Dashboard Widgets"
        ordering = ['dashboard', 'order']
    
    def __str__(self):
        return f"{self.title} ({self.widget_type})"


class ReportSchedule(models.Model):
    """
    Model for scheduling automated report generation
    """
    id = models.AutoField(primary_key=True)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='schedules')
    name = models.CharField(max_length=255)
    
    # Schedule configuration
    cron_expression = models.CharField(max_length=100)
    timezone = models.CharField(max_length=50, default='UTC')
    
    # Recipients
    email_recipients = models.JSONField(default=list)
    user_recipients = models.ManyToManyField(User, blank=True, related_name='report_subscriptions')
    
    # Settings
    is_active = models.BooleanField(default=True)
    last_run = models.DateTimeField(null=True, blank=True)
    next_run = models.DateTimeField(null=True, blank=True)
    run_count = models.PositiveIntegerField(default=0)
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_schedules')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Report Schedule"
        verbose_name_plural = "Report Schedules"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.cron_expression}"


class DataVisualization(models.Model):
    """
    Model for storing data visualization configurations
    """
    VISUALIZATION_TYPES = [
        ('chart', 'Chart'),
        ('graph', 'Graph'),
        ('heatmap', 'Heatmap'),
        ('treemap', 'Treemap'),
        ('sankey', 'Sankey Diagram'),
        ('wordcloud', 'Word Cloud'),
        ('timeline', 'Timeline'),
        ('network', 'Network Diagram'),
    ]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    visualization_type = models.CharField(max_length=20, choices=VISUALIZATION_TYPES)
    
    # Data and configuration
    data_query = models.TextField()
    config = models.JSONField(default=dict)
    
    # Sharing and permissions
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='visualizations')
    is_public = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Data Visualization"
        verbose_name_plural = "Data Visualizations"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.visualization_type})"
