from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.http import JsonResponse, HttpResponseBadRequest
from django.db.models import Count, Q
from datetime import timed<PERSON><PERSON>
from .models import Notification, NotificationPreference, Chat, OnlineChat, GroupMessage, MyFriends
from .forms import NotificationForm, NotificationPreferenceForm, FeedbackForm, MessageForm
from celery import shared_task
import json

@login_required
def compose_message(request):
    """Handle message composition and sending"""
    if request.method == 'POST':
        form = MessageForm(request.POST)
        if form.is_valid():
            message = form.save(commit=False)
            message.sender_index = request.user.id
            message.date = timezone.now().date()
            message.time = timezone.now().time()
            message.save()
            return redirect('communication:chat_history')
    else:
        form = MessageForm()

    context = {
        'form': form,
        'user_type': getattr(request.user, 'type', 'user'),
    }
    return render(request, 'communication/compose_message.html', context)

# Create your views here.

@login_required
def communication_dashboard(request):
    """Communication Dashboard - Central hub for all communication features"""
    user = request.user
    today = timezone.now().date()

    # Quick stats
    total_notifications = user.notifications.count()
    unread_notifications = user.notifications.filter(read_at__isnull=True).count()
    recent_notifications = user.notifications.order_by('-created_at')[:5]

    # Chat stats
    from django.db.models import Q
    total_chats = Chat.objects.filter(Q(sender_index=user.id) | Q(receiver_index=user.id)).count()
    unread_chats = Chat.objects.filter(receiver_index=user.id, _isread=0).count()

    context = {
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
        'recent_notifications': recent_notifications,
        'total_chats': total_chats,
        'unread_chats': unread_chats,
        'user_type': getattr(user, 'type', 'user'),
    }

    return render(request, 'communication/dashboard.html', context)

@login_required
def communication_dashboard(request):
    """
    Communication Dashboard - Central hub for all communication features
    """
    user = request.user
    today = timezone.now().date()

    # Notification Statistics
    total_notifications = user.notifications.count()
    unread_notifications = user.notifications.filter(read_at__isnull=True).count()
    recent_notifications = user.notifications.order_by('-created_at')[:5]

    # Chat Statistics
    from django.db.models import Q
    total_chats = Chat.objects.filter(
        Q(sender_index=user.id) | Q(receiver_index=user.id)
    ).count()

    unread_chats = Chat.objects.filter(
        receiver_index=user.id,
        _isread=0
    ).count()

    recent_chats = Chat.objects.filter(
        Q(sender_index=user.id) | Q(receiver_index=user.id)
    ).order_by('-date', '-time')[:5]

    # Online Chat Statistics
    online_chats_today = OnlineChat.objects.filter(
        user_index=user.id,
        date=today
    ).count()

    # Group Message Statistics
    group_messages_today = GroupMessage.objects.filter(
        sender_index=user.id,
        date=today
    ).count()

    # Friends/Contacts
    total_friends = MyFriends.objects.filter(my_index=user.id).count()

    # Notification preferences
    preferences, created = NotificationPreference.objects.get_or_create(user=user)

    context = {
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
        'recent_notifications': recent_notifications,
        'total_chats': total_chats,
        'unread_chats': unread_chats,
        'recent_chats': recent_chats,
        'online_chats_today': online_chats_today,
        'group_messages_today': group_messages_today,
        'total_friends': total_friends,
        'notification_preferences': preferences,
        'user_type': user.type if hasattr(user, 'type') else 'user',
    }

    return render(request, 'communication/dashboard.html', context)

@login_required
def communication_dashboard(request):
    """
    Communication Dashboard - Central hub for all communication features
    """
    user = request.user
    today = timezone.now().date()

    # Notification Statistics
    total_notifications = user.notifications.count()
    unread_notifications = user.notifications.filter(read_at__isnull=True).count()
    recent_notifications = user.notifications.order_by('-created_at')[:5]

    # Chat Statistics
    total_chats = Chat.objects.filter(
        Q(sender_index=user.id) | Q(receiver_index=user.id)
    ).count()

    unread_chats = Chat.objects.filter(
        receiver_index=user.id,
        _isread=0
    ).count()

    recent_chats = Chat.objects.filter(
        Q(sender_index=user.id) | Q(receiver_index=user.id)
    ).order_by('-date', '-time')[:5]

    # Online Chat Statistics
    online_chats_today = OnlineChat.objects.filter(
        user_index=user.id,
        date=today
    ).count()

    # Group Message Statistics
    group_messages_today = GroupMessage.objects.filter(
        sender_index=user.id,
        date=today
    ).count()

    # Friends/Contacts
    total_friends = MyFriends.objects.filter(my_index=user.id).count()

    # Notification preferences
    preferences, created = NotificationPreference.objects.get_or_create(user=user)

    context = {
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
        'recent_notifications': recent_notifications,
        'total_chats': total_chats,
        'unread_chats': unread_chats,
        'recent_chats': recent_chats,
        'online_chats_today': online_chats_today,
        'group_messages_today': group_messages_today,
        'total_friends': total_friends,
        'notification_preferences': preferences,
        'user_type': user.type if hasattr(user, 'type') else 'user',
    }

    return render(request, 'communication/dashboard.html', context)

@login_required
def notifications_list(request):
    notifications = request.user.notifications.all().order_by('-created_at')
    return render(request, 'communication/notifications_list.html', {'notifications': notifications})

@login_required
def notification_detail(request, pk):
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    if not notification.read_at:
        notification.mark_as_read()
    return render(request, 'communication/notification_detail.html', {'notification': notification})

@login_required
def create_notification(request):
    if request.method == 'POST':
        form = NotificationForm(request.POST)
        if form.is_valid():
            notification = form.save(commit=False)
            notification.user = request.user
            if notification.scheduled_time and notification.scheduled_time > timezone.now():
                # Schedule the notification to be sent later
                send_notification.apply_async((notification.id,), eta=notification.scheduled_time)
            else:
                # Send the notification immediately
                send_notification(notification.id)
            return redirect('communication:notifications_list')
    else:
        form = NotificationForm()
    return render(request, 'communication/create_notification.html', {'form': form})

@login_required
def notification_preferences(request):
    preferences, created = NotificationPreference.objects.get_or_create(user=request.user)
    if request.method == 'POST':
        form = NotificationPreferenceForm(request.POST, instance=preferences)
        if form.is_valid():
            form.save()
            return redirect('communication:notifications_list')
    else:
        form = NotificationPreferenceForm(instance=preferences)
    return render(request, 'communication/notification_preferences.html', {'form': form})

@login_required
def chat_history(request):
    chats = Chat.objects.filter(sender_index=request.user.id).order_by('-date', '-time')
    return render(request, 'communication/chat_history.html', {'chats': chats})

@login_required
def online_chat(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        chat = OnlineChat.objects.create(
            conversation_id=data['conversation_id'],
            user_index=request.user.id,
            msg=data['msg'],
            user_type='user',
            _isread=0,
            date=timezone.now().date(),
            time=timezone.now().time()
        )
        # Send notification to the other user
        send_chat_notification.delay(chat.id)
        return JsonResponse({'status': 'success', 'chat_id': chat.id})
    else:
        chats = OnlineChat.objects.filter(user_index=request.user.id).order_by('-date', '-time')
        return render(request, 'communication/online_chat.html', {'chats': chats})

@login_required
def group_chat(request, group_id):
    if request.method == 'POST':
        data = json.loads(request.body)
        message = GroupMessage.objects.create(
            conversation_id=data['conversation_id'],
            message=data['message'],
            sender_index=request.user.id,
            sender_type='user',
            group_id=group_id,
            grade='',
            date=timezone.now().date(),
            time=timezone.now().time()
        )
        # Send notification to group members
        send_group_chat_notification.delay(message.id)
        return JsonResponse({'status': 'success', 'message_id': message.id})
    else:
        messages = GroupMessage.objects.filter(group_id=group_id).order_by('-date', '-time')
        return render(request, 'communication/group_chat.html', {'messages': messages, 'group_id': group_id})

@login_required
def feedback(request):
    if request.method == 'POST':
        form = FeedbackForm(request.POST)
        if form.is_valid():
            feedback = form.save(commit=False)
            feedback.user = request.user
            feedback.save()
            return redirect('communication:feedback_success')
    else:
        form = FeedbackForm()
    return render(request, 'communication/feedback.html', {'form': form})

@login_required
def feedback_success(request):
    return render(request, 'communication/feedback_success.html')

@login_required
def api_notifications(request):
    """API endpoint for notifications"""
    if request.method == 'GET':
        notifications = request.user.notifications.filter(
            read_at__isnull=True
        ).order_by('-created_at')[:10]  # Get latest 10 unread notifications

        data = []
        for notification in notifications:
            data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type,
                'created_at': notification.created_at.isoformat(),
                'read': notification.read_at is not None,
            })

        return JsonResponse({
            'notifications': data,
            'unread_count': notifications.count()
        })

    return JsonResponse({'error': 'Method not allowed'}, status=405)

@shared_task
def send_notification(notification_id):
    try:
        notification = Notification.objects.get(id=notification_id)
        # Here you would add the actual sending logic
        # For example, send an email, SMS, or in-app notification
        notification.mark_as_sent()
    except Notification.DoesNotExist:
        pass

@shared_task
def send_scheduled_notifications():
    now = timezone.now()
    notifications = Notification.objects.filter(
        status='pending',
        scheduled_time__lte=now
    )
    for notification in notifications:
        send_notification(notification.id)

@shared_task
def send_chat_notification(chat_id):
    try:
        chat = OnlineChat.objects.get(id=chat_id)
        # Create a notification for the chat
        Notification.objects.create(
            user_id=chat.user_index,
            title="New Chat Message",
            message=chat.msg,
            notification_type='system',
            channel='in_app',
            related_object=chat
        )
    except OnlineChat.DoesNotExist:
        pass

@shared_task
def send_group_chat_notification(message_id):
    try:
        message = GroupMessage.objects.get(id=message_id)
        # Get all members of the group and send them notifications
        friends = MyFriends.objects.filter(group_id=message.group_id)
        for friend in friends:
            Notification.objects.create(
                user_id=friend.friend_index,
                title="New Group Message",
                message=message.message,
                notification_type='system',
                channel='in_app',
                related_object=message
            )
    except GroupMessage.DoesNotExist:
        pass
