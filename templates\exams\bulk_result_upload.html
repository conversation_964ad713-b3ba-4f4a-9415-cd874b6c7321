{% extends 'exams/base.html' %}

{% block exam_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Bulk Result Upload</h1>
                    <p class="text-sm text-gray-600 mt-1">{{ exam.name }} - Upload results from Excel or CSV file</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'exams:exam_detail' exam.id %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Exam
                    </a>
                    <a href="{% url 'exams:download_template' exam.id %}" 
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-download mr-2"></i>Download Template
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Upload Instructions</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Download the template file to ensure correct format</li>
                        <li>Fill in student marks, attendance status, and remarks</li>
                        <li>Supported formats: Excel (.xlsx, .xls) and CSV (.csv)</li>
                        <li>Maximum file size: 10MB</li>
                        <li>Ensure student IDs match exactly with system records</li>
                        <li>Leave marks blank for absent students</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Upload File</h2>
        </div>
        
        <form method="post" enctype="multipart/form-data" class="px-6 py-4" id="uploadForm">
            {% csrf_token %}
            
            <div class="mb-6">
                <label for="id_uploaded_file" class="block text-sm font-medium text-gray-700 mb-2">
                    Select File
                </label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors"
                     id="dropZone">
                    <div class="space-y-1 text-center">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <div class="flex text-sm text-gray-600">
                            <label for="id_uploaded_file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                <span>Upload a file</span>
                                {{ form.uploaded_file }}
                            </label>
                            <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500">Excel or CSV files up to 10MB</p>
                    </div>
                </div>
                {% if form.uploaded_file.errors %}
                    <div class="mt-2 text-sm text-red-600">
                        {{ form.uploaded_file.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <!-- File Preview -->
            <div id="filePreview" class="hidden mb-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-file-excel text-green-600 text-2xl mr-3"></i>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900" id="fileName"></p>
                            <p class="text-xs text-gray-500" id="fileSize"></p>
                        </div>
                        <button type="button" onclick="removeFile()" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Upload Options -->
            <div class="mb-6">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Upload Options</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="validateOnly" name="validate_only" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="validateOnly" class="ml-2 text-sm text-gray-700">
                            Validate only (don't save results)
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="overwriteExisting" name="overwrite_existing" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="overwriteExisting" class="ml-2 text-sm text-gray-700">
                            Overwrite existing results
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="sendNotifications" name="send_notifications" checked
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="sendNotifications" class="ml-2 text-sm text-gray-700">
                            Send notifications to teachers
                        </label>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3">
                <a href="{% url 'exams:exam_detail' exam.id %}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" id="submitBtn" disabled
                        class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-upload mr-2"></i>Upload Results
                </button>
            </div>
        </form>
    </div>

    <!-- Upload Progress -->
    <div id="uploadProgress" class="hidden bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Upload Progress</h2>
        </div>
        <div class="px-6 py-4">
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Processing...</span>
                    <span id="progressPercent">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
            <div id="progressMessages" class="space-y-2">
                <!-- Progress messages will be added here -->
            </div>
        </div>
    </div>

    <!-- Previous Uploads -->
    {% if previous_uploads %}
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Previous Uploads</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Upload Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Records</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uploaded By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for upload in previous_uploads %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ upload.original_filename }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ upload.uploaded_at|date:"M d, Y H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if upload.status == 'completed' %}bg-green-100 text-green-800
                                {% elif upload.status == 'failed' %}bg-red-100 text-red-800
                                {% elif upload.status == 'processing' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ upload.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ upload.total_records }} total, {{ upload.successful_records }} successful
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ upload.uploaded_by.get_full_name|default:upload.uploaded_by.username }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'exams:bulk_upload_detail' upload.id %}" 
                               class="text-blue-600 hover:text-blue-900 mr-3">View Details</a>
                            {% if upload.error_file %}
                            <a href="{{ upload.error_file.url }}" 
                               class="text-red-600 hover:text-red-900">Download Errors</a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

<script>
// File upload handling
const fileInput = document.getElementById('id_uploaded_file');
const dropZone = document.getElementById('dropZone');
const filePreview = document.getElementById('filePreview');
const submitBtn = document.getElementById('submitBtn');
const uploadForm = document.getElementById('uploadForm');

// Drag and drop functionality
dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    dropZone.classList.add('border-blue-400', 'bg-blue-50');
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-blue-400', 'bg-blue-50');
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-blue-400', 'bg-blue-50');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
    }
});

// File input change
fileInput.addEventListener('change', handleFileSelect);

function handleFileSelect() {
    const file = fileInput.files[0];
    if (file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        filePreview.classList.remove('hidden');
        submitBtn.disabled = false;
    }
}

function removeFile() {
    fileInput.value = '';
    filePreview.classList.add('hidden');
    submitBtn.disabled = true;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission with progress
uploadForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');
    const progressMessages = document.getElementById('progressMessages');
    
    // Show progress
    progressDiv.classList.remove('hidden');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
    
    // Simulate progress (in real implementation, use XMLHttpRequest for actual progress)
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        progressBar.style.width = progress + '%';
        progressPercent.textContent = Math.round(progress) + '%';
    }, 500);
    
    // Submit form
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressPercent.textContent = '100%';
        
        if (data.success) {
            progressMessages.innerHTML = '<div class="text-green-600"><i class="fas fa-check-circle mr-2"></i>Upload completed successfully!</div>';
            setTimeout(() => {
                window.location.href = data.redirect_url || '{% url "exams:exam_detail" exam.id %}';
            }, 2000);
        } else {
            progressMessages.innerHTML = '<div class="text-red-600"><i class="fas fa-exclamation-circle mr-2"></i>' + data.error + '</div>';
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-upload mr-2"></i>Upload Results';
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        progressMessages.innerHTML = '<div class="text-red-600"><i class="fas fa-exclamation-circle mr-2"></i>Upload failed. Please try again.</div>';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-upload mr-2"></i>Upload Results';
    });
});
</script>
{% endblock %}
