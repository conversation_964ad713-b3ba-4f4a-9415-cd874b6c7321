from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from datetime import date, timedelta
from decimal import Decimal
import json

from .models import (
    SalaryGrade, SalaryComponent, EmployeeSalaryStructure,
    EmployeeSalaryComponent, PayrollPeriod, EnhancedPayroll,
    PayrollComponent, PaySlip, TeacherSalary
)
from apps.teachers.models import Teacher

@login_required
def salary_dashboard(request):
    """Salary management dashboard with statistics and overview."""
    today = date.today()
    current_month = today.month
    current_year = today.year

    # Calculate key salary metrics
    total_employees = EmployeeSalaryStructure.objects.filter(is_active=True).count()

    # Current month payroll
    current_payroll = EnhancedPayroll.objects.filter(
        payroll_period__start_date__year=current_year,
        payroll_period__start_date__month=current_month,
        status__in=['approved', 'paid']
    )

    monthly_payroll_cost = current_payroll.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00')

    # Pending payrolls
    pending_payrolls = EnhancedPayroll.objects.filter(
        status__in=['draft', 'calculated']
    ).count()

    # Recent payroll periods
    recent_periods = PayrollPeriod.objects.order_by('-start_date')[:5]

    # Salary grades
    salary_grades = SalaryGrade.objects.filter(is_active=True).order_by('grade_type', 'minimum_salary')

    # Recent payslips
    recent_payslips = PaySlip.objects.select_related('payroll__employee').order_by('-generated_at')[:10]

    context = {
        'total_employees': total_employees,
        'monthly_payroll_cost': monthly_payroll_cost,
        'pending_payrolls': pending_payrolls,
        'recent_periods': recent_periods,
        'salary_grades': salary_grades,
        'recent_payslips': recent_payslips,
        'total_salary_grades': SalaryGrade.objects.filter(is_active=True).count(),
        'total_components': SalaryComponent.objects.filter(is_active=True).count(),
        'active_periods': PayrollPeriod.objects.filter(status__in=['draft', 'processing']).count(),
    }
    return render(request, 'salary/dashboard.html', context)

@login_required
def employee_salary_list(request):
    """List all employee salary structures."""
    salary_structures = EmployeeSalaryStructure.objects.select_related(
        'employee', 'salary_grade'
    ).filter(is_active=True).order_by('employee__full_name')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        salary_structures = salary_structures.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__employee_id__icontains=search_query) |
            Q(salary_grade__grade_name__icontains=search_query)
        )

    # Filter by grade
    grade_filter = request.GET.get('grade')
    if grade_filter:
        salary_structures = salary_structures.filter(salary_grade_id=grade_filter)

    paginator = Paginator(salary_structures, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'salary_structures': page_obj,
        'salary_grades': SalaryGrade.objects.filter(is_active=True),
        'search_query': search_query,
        'grade_filter': grade_filter,
    }
    return render(request, 'salary/employee_list.html', context)

@login_required
def employee_salary_detail(request, pk):
    """View employee salary details."""
    salary_structure = get_object_or_404(EmployeeSalaryStructure, pk=pk)
    components = EmployeeSalaryComponent.objects.filter(
        salary_structure=salary_structure,
        is_active=True
    ).select_related('component')

    recent_payrolls = EnhancedPayroll.objects.filter(
        employee=salary_structure.employee
    ).order_by('-payroll_period__start_date')[:10]

    context = {
        'salary_structure': salary_structure,
        'components': components,
        'recent_payrolls': recent_payrolls,
    }
    return render(request, 'salary/employee_detail.html', context)

@login_required
def add_employee_salary(request):
    """Add new employee salary structure."""
    if request.method == 'POST':
        employee_id = request.POST.get('employee')
        grade_id = request.POST.get('salary_grade')
        basic_salary = request.POST.get('basic_salary')
        effective_date = request.POST.get('effective_date')

        if employee_id and grade_id and basic_salary and effective_date:
            employee = get_object_or_404(Teacher, pk=employee_id)
            salary_grade = get_object_or_404(SalaryGrade, pk=grade_id)

            # Deactivate existing salary structure
            EmployeeSalaryStructure.objects.filter(
                employee=employee,
                is_active=True
            ).update(is_active=False, end_date=effective_date)

            # Create new salary structure
            salary_structure = EmployeeSalaryStructure.objects.create(
                employee=employee,
                salary_grade=salary_grade,
                basic_salary=Decimal(basic_salary),
                effective_date=effective_date,
                bank_name=request.POST.get('bank_name', ''),
                account_number=request.POST.get('account_number', ''),
                account_name=request.POST.get('account_name', ''),
                tax_number=request.POST.get('tax_number', ''),
                tax_rate=Decimal(request.POST.get('tax_rate', '0')),
                created_by=request.user
            )

            messages.success(request, f'Salary structure created for {employee.full_name}.')
            return redirect('salary:employee_detail', pk=salary_structure.pk)

    context = {
        'employees': Teacher.objects.filter(status='active').order_by('full_name'),
        'salary_grades': SalaryGrade.objects.filter(is_active=True).order_by('grade_name'),
    }
    return render(request, 'salary/add_employee_salary.html', context)

@login_required
def edit_employee_salary(request, pk):
    """Edit employee salary structure."""
    salary_structure = get_object_or_404(EmployeeSalaryStructure, pk=pk)

    if request.method == 'POST':
        salary_structure.basic_salary = Decimal(request.POST.get('basic_salary'))
        salary_structure.bank_name = request.POST.get('bank_name', '')
        salary_structure.account_number = request.POST.get('account_number', '')
        salary_structure.account_name = request.POST.get('account_name', '')
        salary_structure.tax_number = request.POST.get('tax_number', '')
        salary_structure.tax_rate = Decimal(request.POST.get('tax_rate', '0'))
        salary_structure.notes = request.POST.get('notes', '')
        salary_structure.save()

        messages.success(request, f'Salary structure updated for {salary_structure.employee.full_name}.')
        return redirect('salary:employee_detail', pk=salary_structure.pk)

    context = {
        'salary_structure': salary_structure,
        'salary_grades': SalaryGrade.objects.filter(is_active=True).order_by('grade_name'),
    }
    return render(request, 'salary/edit_employee_salary.html', context)

@login_required
def salary_grade_list(request):
    """List all salary grades."""
    grades = SalaryGrade.objects.filter(is_active=True).order_by('grade_type', 'minimum_salary')

    context = {
        'grades': grades,
    }
    return render(request, 'salary/grade_list.html', context)

@login_required
def add_salary_grade(request):
    """Add new salary grade."""
    if request.method == 'POST':
        grade = SalaryGrade.objects.create(
            grade_name=request.POST.get('grade_name'),
            grade_type=request.POST.get('grade_type'),
            minimum_salary=Decimal(request.POST.get('minimum_salary')),
            maximum_salary=Decimal(request.POST.get('maximum_salary')),
            annual_increment=Decimal(request.POST.get('annual_increment', '0')),
            description=request.POST.get('description', '')
        )

        messages.success(request, f'Salary grade "{grade.grade_name}" created successfully.')
        return redirect('salary:grade_list')

    return render(request, 'salary/add_grade.html')

@login_required
def edit_salary_grade(request, pk):
    """Edit salary grade."""
    grade = get_object_or_404(SalaryGrade, pk=pk)

    if request.method == 'POST':
        grade.grade_name = request.POST.get('grade_name')
        grade.grade_type = request.POST.get('grade_type')
        grade.minimum_salary = Decimal(request.POST.get('minimum_salary'))
        grade.maximum_salary = Decimal(request.POST.get('maximum_salary'))
        grade.annual_increment = Decimal(request.POST.get('annual_increment', '0'))
        grade.description = request.POST.get('description', '')
        grade.save()

        messages.success(request, f'Salary grade "{grade.grade_name}" updated successfully.')
        return redirect('salary:grade_list')

    context = {'grade': grade}
    return render(request, 'salary/edit_grade.html', context)

@login_required
def delete_salary_grade(request, pk):
    """Delete salary grade."""
    grade = get_object_or_404(SalaryGrade, pk=pk)

    if request.method == 'POST':
        grade.is_active = False
        grade.save()
        messages.success(request, f'Salary grade "{grade.grade_name}" deleted successfully.')
        return redirect('salary:grade_list')

    context = {'grade': grade}
    return render(request, 'salary/delete_grade.html', context)

@login_required
def salary_component_list(request):
    """List all salary components."""
    components = SalaryComponent.objects.filter(is_active=True).order_by('component_type', 'name')

    context = {
        'components': components,
    }
    return render(request, 'salary/component_list.html', context)

@login_required
def add_salary_component(request):
    """Add new salary component."""
    if request.method == 'POST':
        component = SalaryComponent.objects.create(
            name=request.POST.get('name'),
            component_type=request.POST.get('component_type'),
            calculation_method=request.POST.get('calculation_method'),
            fixed_amount=Decimal(request.POST.get('fixed_amount', '0')) if request.POST.get('fixed_amount') else None,
            percentage_rate=Decimal(request.POST.get('percentage_rate', '0')) if request.POST.get('percentage_rate') else None,
            hourly_rate=Decimal(request.POST.get('hourly_rate', '0')) if request.POST.get('hourly_rate') else None,
            daily_rate=Decimal(request.POST.get('daily_rate', '0')) if request.POST.get('daily_rate') else None,
            is_mandatory=request.POST.get('is_mandatory') == 'on',
            is_taxable=request.POST.get('is_taxable') == 'on',
            description=request.POST.get('description', '')
        )

        messages.success(request, f'Salary component "{component.name}" created successfully.')
        return redirect('salary:component_list')

    return render(request, 'salary/add_component.html')

@login_required
def edit_salary_component(request, pk):
    """Edit salary component."""
    component = get_object_or_404(SalaryComponent, pk=pk)

    if request.method == 'POST':
        component.name = request.POST.get('name')
        component.component_type = request.POST.get('component_type')
        component.calculation_method = request.POST.get('calculation_method')
        component.fixed_amount = Decimal(request.POST.get('fixed_amount', '0')) if request.POST.get('fixed_amount') else None
        component.percentage_rate = Decimal(request.POST.get('percentage_rate', '0')) if request.POST.get('percentage_rate') else None
        component.hourly_rate = Decimal(request.POST.get('hourly_rate', '0')) if request.POST.get('hourly_rate') else None
        component.daily_rate = Decimal(request.POST.get('daily_rate', '0')) if request.POST.get('daily_rate') else None
        component.is_mandatory = request.POST.get('is_mandatory') == 'on'
        component.is_taxable = request.POST.get('is_taxable') == 'on'
        component.description = request.POST.get('description', '')
        component.save()

        messages.success(request, f'Salary component "{component.name}" updated successfully.')
        return redirect('salary:component_list')

    context = {'component': component}
    return render(request, 'salary/edit_component.html', context)

@login_required
def delete_salary_component(request, pk):
    """Delete salary component."""
    component = get_object_or_404(SalaryComponent, pk=pk)

    if request.method == 'POST':
        component.is_active = False
        component.save()
        messages.success(request, f'Salary component "{component.name}" deleted successfully.')
        return redirect('salary:component_list')

    context = {'component': component}
    return render(request, 'salary/delete_component.html', context)

@login_required
def payroll_list(request):
    """List all payroll records."""
    payrolls = EnhancedPayroll.objects.select_related(
        'employee', 'payroll_period'
    ).order_by('-payroll_period__start_date', 'employee__full_name')

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        payrolls = payrolls.filter(status=status_filter)

    # Filter by period
    period_filter = request.GET.get('period')
    if period_filter:
        payrolls = payrolls.filter(payroll_period_id=period_filter)

    # Calculate statistics
    total_records = payrolls.count()
    approved_count = payrolls.filter(status='approved').count()
    pending_count = payrolls.filter(status__in=['draft', 'calculated', 'processing']).count()
    total_payroll = payrolls.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00')

    paginator = Paginator(payrolls, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'payrolls': page_obj,
        'status_choices': EnhancedPayroll.PAYROLL_STATUSES,
        'payroll_periods': PayrollPeriod.objects.order_by('-start_date')[:12],
        'status_filter': status_filter,
        'period_filter': period_filter,
        'total_records': total_records,
        'approved_count': approved_count,
        'pending_count': pending_count,
        'total_payroll': total_payroll,
    }
    return render(request, 'salary/payroll_list.html', context)

@login_required
def payroll_period_list(request):
    """List all payroll periods."""
    periods = PayrollPeriod.objects.order_by('-start_date')

    paginator = Paginator(periods, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'periods': page_obj,
    }
    return render(request, 'salary/payroll_period_list.html', context)

@login_required
def add_payroll_period(request):
    """Add new payroll period."""
    if request.method == 'POST':
        period = PayrollPeriod.objects.create(
            period_name=request.POST.get('period_name'),
            period_type=request.POST.get('period_type'),
            start_date=request.POST.get('start_date'),
            end_date=request.POST.get('end_date'),
            pay_date=request.POST.get('pay_date'),
            notes=request.POST.get('notes', '')
        )

        messages.success(request, f'Payroll period "{period.period_name}" created successfully.')
        return redirect('salary:payroll_period_detail', pk=period.pk)

    return render(request, 'salary/add_payroll_period.html')

@login_required
def payroll_period_detail(request, pk):
    """View payroll period details."""
    period = get_object_or_404(PayrollPeriod, pk=pk)
    payrolls = EnhancedPayroll.objects.filter(payroll_period=period).select_related('employee')

    context = {
        'period': period,
        'payrolls': payrolls,
        'total_employees': payrolls.count(),
        'total_gross': payrolls.aggregate(total=Sum('gross_salary'))['total'] or Decimal('0.00'),
        'total_net': payrolls.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00'),
    }
    return render(request, 'salary/payroll_period_detail.html', context)

@login_required
def process_payroll(request, pk):
    """Process payroll for a period."""
    period = get_object_or_404(PayrollPeriod, pk=pk)

    if request.method == 'POST':
        # Get all active employees
        active_employees = EmployeeSalaryStructure.objects.filter(
            is_active=True,
            effective_date__lte=period.end_date
        ).select_related('employee', 'salary_grade')

        created_count = 0
        for salary_structure in active_employees:
            # Check if payroll already exists
            if not EnhancedPayroll.objects.filter(
                employee=salary_structure.employee,
                payroll_period=period
            ).exists():
                # Create payroll record
                payroll = EnhancedPayroll.objects.create(
                    employee=salary_structure.employee,
                    payroll_period=period,
                    salary_structure=salary_structure,
                    basic_salary=salary_structure.basic_salary,
                    working_days=22,  # Default working days
                    days_worked=22,   # Default days worked
                    created_by=request.user
                )

                # Calculate salary components
                calculate_payroll_components(payroll)
                created_count += 1

        period.status = 'processing'
        period.total_employees = created_count
        period.processed_by = request.user
        period.save()

        messages.success(request, f'Payroll processed for {created_count} employees.')
        return redirect('salary:payroll_period_detail', pk=period.pk)

    context = {'period': period}
    return render(request, 'salary/process_payroll.html', context)

def calculate_payroll_components(payroll):
    """Calculate payroll components for an employee."""
    total_allowances = Decimal('0.00')
    total_deductions = Decimal('0.00')

    # Get employee salary components
    components = EmployeeSalaryComponent.objects.filter(
        salary_structure=payroll.salary_structure,
        is_active=True,
        effective_date__lte=payroll.payroll_period.end_date
    ).select_related('component')

    for emp_component in components:
        amount = emp_component.get_calculated_amount(
            basic_salary=payroll.basic_salary,
            hours=payroll.overtime_hours,
            days=payroll.days_worked
        )

        # Create payroll component record
        PayrollComponent.objects.create(
            payroll=payroll,
            component=emp_component.component,
            calculated_amount=amount,
            base_amount=payroll.basic_salary
        )

        if emp_component.component.component_type in ['allowance', 'bonus', 'overtime']:
            total_allowances += amount
        elif emp_component.component.component_type == 'deduction':
            total_deductions += amount

    # Calculate tax
    tax_amount = (payroll.basic_salary + total_allowances) * (payroll.salary_structure.tax_rate / 100)

    # Update payroll totals
    payroll.total_allowances = total_allowances
    payroll.total_deductions = total_deductions
    payroll.tax_amount = tax_amount
    payroll.status = 'calculated'
    payroll.save()

@login_required
def approve_payroll(request, pk):
    """Approve payroll period."""
    period = get_object_or_404(PayrollPeriod, pk=pk)

    if request.method == 'POST':
        # Update all payrolls in this period to approved
        EnhancedPayroll.objects.filter(payroll_period=period).update(status='approved')

        period.status = 'approved'
        period.approved_by = request.user
        period.save()

        messages.success(request, f'Payroll period "{period.period_name}" approved successfully.')
        return redirect('salary:payroll_period_detail', pk=period.pk)

    context = {'period': period}
    return render(request, 'salary/approve_payroll.html', context)

@login_required
def payslip_list(request):
    """List all payslips."""
    payslips = PaySlip.objects.select_related(
        'payroll__employee', 'payroll__payroll_period'
    ).order_by('-generated_at')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        payslips = payslips.filter(
            Q(payroll__employee__full_name__icontains=search_query) |
            Q(payslip_number__icontains=search_query)
        )

    paginator = Paginator(payslips, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'payslips': page_obj,
        'search_query': search_query,
    }
    return render(request, 'salary/payslip_list.html', context)

@login_required
def payslip_detail(request, pk):
    """View payslip details."""
    payslip = get_object_or_404(PaySlip, pk=pk)
    components = PayrollComponent.objects.filter(payroll=payslip.payroll)

    context = {
        'payslip': payslip,
        'components': components,
    }
    return render(request, 'salary/payslip_detail.html', context)

@login_required
def download_payslip(request, pk):
    """Download payslip as PDF."""
    payslip = get_object_or_404(PaySlip, pk=pk)

    # In a real implementation, this would generate and return a PDF
    messages.info(request, 'PDF download feature will be implemented soon.')
    return redirect('salary:payslip_detail', pk=pk)

@login_required
def generate_payslips(request, payroll_period_id):
    """Generate payslips for a payroll period."""
    period = get_object_or_404(PayrollPeriod, pk=payroll_period_id)

    if request.method == 'POST':
        payrolls = EnhancedPayroll.objects.filter(
            payroll_period=period,
            status='approved'
        )

        generated_count = 0
        for payroll in payrolls:
            if not hasattr(payroll, 'payslip'):
                PaySlip.objects.create(
                    payroll=payroll,
                    generated_by=request.user
                )
                generated_count += 1

        messages.success(request, f'Generated {generated_count} payslips.')
        return redirect('salary:payroll_period_detail', pk=period.pk)

    context = {'period': period}
    return render(request, 'salary/generate_payslips.html', context)

@login_required
def salary_reports(request):
    """Salary reports dashboard."""
    context = {
        'total_employees': EmployeeSalaryStructure.objects.filter(is_active=True).count(),
        'total_payroll_periods': PayrollPeriod.objects.count(),
        'total_payslips': PaySlip.objects.count(),
    }
    return render(request, 'salary/reports.html', context)

@login_required
def payroll_summary_report(request):
    """Payroll summary report."""
    periods = PayrollPeriod.objects.order_by('-start_date')[:12]

    # Calculate summary statistics
    total_periods = periods.count()
    total_gross_payroll = periods.aggregate(total=Sum('total_gross_salary'))['total'] or Decimal('0.00')
    total_net_payroll = periods.aggregate(total=Sum('total_net_salary'))['total'] or Decimal('0.00')
    avg_employees = periods.aggregate(avg=Avg('total_employees'))['avg'] or 0

    # Calculate average salary per employee across all periods
    total_employees_across_periods = periods.aggregate(total=Sum('total_employees'))['total'] or 0
    avg_salary = total_net_payroll / total_employees_across_periods if total_employees_across_periods > 0 else Decimal('0.00')

    # Get enhanced data for each period
    periods_with_data = []
    for period in periods:
        # Get payroll records for this period
        period_payrolls = EnhancedPayroll.objects.filter(payroll_period=period)
        period_gross = period_payrolls.aggregate(total=Sum('gross_salary'))['total'] or Decimal('0.00')
        period_net = period_payrolls.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00')
        period_employees = period_payrolls.count()

        periods_with_data.append({
            'period': period,
            'calculated_gross': period_gross,
            'calculated_net': period_net,
            'calculated_employees': period_employees,
        })

    context = {
        'periods': periods,
        'periods_with_data': periods_with_data,
        'total_periods': total_periods,
        'total_gross_payroll': total_gross_payroll,
        'total_net_payroll': total_net_payroll,
        'avg_employees': avg_employees,
        'avg_salary': avg_salary,
    }
    return render(request, 'salary/payroll_summary_report.html', context)

@login_required
def employee_salary_report(request):
    """Employee salary report."""
    salary_structures = EmployeeSalaryStructure.objects.filter(
        is_active=True
    ).select_related('employee', 'salary_grade').order_by('employee__full_name')

    # Calculate additional data for each structure
    enhanced_structures = []
    total_monthly_payroll = Decimal('0.00')

    for structure in salary_structures:
        annual_salary = structure.basic_salary * 12
        enhanced_structures.append({
            'structure': structure,
            'annual_salary': annual_salary,
        })
        total_monthly_payroll += structure.basic_salary

    # Calculate statistics
    total_employees = len(enhanced_structures)
    total_annual_payroll = total_monthly_payroll * 12
    avg_monthly_salary = total_monthly_payroll / total_employees if total_employees > 0 else Decimal('0.00')
    avg_annual_salary = avg_monthly_salary * 12

    context = {
        'salary_structures': salary_structures,
        'enhanced_structures': enhanced_structures,
        'total_employees': total_employees,
        'total_monthly_payroll': total_monthly_payroll,
        'total_annual_payroll': total_annual_payroll,
        'avg_monthly_salary': avg_monthly_salary,
        'avg_annual_salary': avg_annual_salary,
    }
    return render(request, 'salary/employee_salary_report.html', context)

@login_required
def monthly_payroll_report(request):
    """Monthly payroll report."""
    # Get current month or selected month
    selected_month = request.GET.get('month')
    selected_year = request.GET.get('year')

    from django.utils import timezone
    today = timezone.now().date()
    if selected_month and selected_year:
        month = int(selected_month)
        year = int(selected_year)
    else:
        month = today.month
        year = today.year

    # Get payroll periods for the selected month
    periods = PayrollPeriod.objects.filter(
        start_date__year=year,
        start_date__month=month
    ).order_by('-start_date')

    # Get payroll records for the month
    payrolls = EnhancedPayroll.objects.filter(
        payroll_period__in=periods
    ).select_related('employee', 'payroll_period')

    # Calculate statistics
    total_employees = payrolls.values('employee').distinct().count()
    total_gross = payrolls.aggregate(total=Sum('gross_salary'))['total'] or Decimal('0.00')
    total_net = payrolls.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00')
    total_deductions = payrolls.aggregate(total=Sum('total_deductions'))['total'] or Decimal('0.00')

    from datetime import date
    context = {
        'periods': periods,
        'payrolls': payrolls,
        'selected_month': month,
        'selected_year': year,
        'total_employees': total_employees,
        'total_gross': total_gross,
        'total_net': total_net,
        'total_deductions': total_deductions,
        'month_name': date(year, month, 1).strftime('%B %Y'),
    }
    return render(request, 'salary/monthly_payroll_report.html', context)

@login_required
def annual_payroll_report(request):
    """Annual payroll report."""
    selected_year = request.GET.get('year')
    from django.utils import timezone
    from datetime import date
    today = timezone.now().date()
    year = int(selected_year) if selected_year else today.year

    # Get all payroll periods for the year
    periods = PayrollPeriod.objects.filter(
        start_date__year=year
    ).order_by('start_date')

    # Get payroll records for the year
    payrolls = EnhancedPayroll.objects.filter(
        payroll_period__in=periods
    ).select_related('employee', 'payroll_period')

    # Monthly breakdown
    monthly_data = []
    prev_net = Decimal('0.00')

    for month in range(1, 13):
        month_periods = periods.filter(start_date__month=month)
        month_payrolls = payrolls.filter(payroll_period__in=month_periods)

        total_gross = month_payrolls.aggregate(total=Sum('gross_salary'))['total'] or Decimal('0.00')
        total_net = month_payrolls.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00')
        total_deductions = month_payrolls.aggregate(total=Sum('total_deductions'))['total'] or Decimal('0.00')
        employee_count = month_payrolls.values('employee').distinct().count()

        # Calculate savings (total deductions + tax)
        total_tax = month_payrolls.aggregate(total=Sum('tax_amount'))['total'] or Decimal('0.00')
        savings = total_deductions + total_tax

        # Calculate growth rate compared to previous month
        growth_rate = 0
        if prev_net > 0 and total_net > 0:
            growth_rate = ((total_net - prev_net) / prev_net) * 100

        monthly_data.append({
            'month': month,
            'month_name': date(year, month, 1).strftime('%B'),
            'total_gross': total_gross,
            'total_net': total_net,
            'total_deductions': total_deductions,
            'total_tax': total_tax,
            'employee_count': employee_count,
            'savings': savings,
            'growth_rate': growth_rate,
        })

        if total_net > 0:
            prev_net = total_net

    # Annual totals
    annual_gross = payrolls.aggregate(total=Sum('gross_salary'))['total'] or Decimal('0.00')
    annual_net = payrolls.aggregate(total=Sum('net_salary'))['total'] or Decimal('0.00')
    annual_deductions = payrolls.aggregate(total=Sum('total_deductions'))['total'] or Decimal('0.00')
    annual_tax = payrolls.aggregate(total=Sum('tax_amount'))['total'] or Decimal('0.00')
    total_employees = payrolls.values('employee').distinct().count()

    # Calculate quarterly data
    quarterly_data = []
    quarters = [
        {'name': 'Q1', 'months': [1, 2, 3]},
        {'name': 'Q2', 'months': [4, 5, 6]},
        {'name': 'Q3', 'months': [7, 8, 9]},
        {'name': 'Q4', 'months': [10, 11, 12]},
    ]

    for quarter in quarters:
        quarter_gross = sum(month['total_gross'] for month in monthly_data if month['month'] in quarter['months'])
        quarter_net = sum(month['total_net'] for month in monthly_data if month['month'] in quarter['months'])
        quarter_employees = max((month['employee_count'] for month in monthly_data if month['month'] in quarter['months']), default=0)

        quarterly_data.append({
            'name': quarter['name'],
            'gross': quarter_gross,
            'net': quarter_net,
            'employees': quarter_employees,
        })

    context = {
        'year': year,
        'monthly_data': monthly_data,
        'quarterly_data': quarterly_data,
        'annual_gross': annual_gross,
        'annual_net': annual_net,
        'annual_deductions': annual_deductions,
        'annual_tax': annual_tax,
        'total_employees': total_employees,
        'periods': periods,
        'total_periods': periods.count(),
    }
    return render(request, 'salary/annual_payroll_report.html', context)

@login_required
def department_salary_report(request):
    """Department salary report."""
    # Group salary structures by department
    salary_structures = EmployeeSalaryStructure.objects.filter(
        is_active=True
    ).select_related('employee', 'salary_grade').order_by('employee__department', 'employee__full_name')

    # Calculate department statistics
    department_stats = {}
    total_payroll = Decimal('0.00')
    total_employees = 0

    for structure in salary_structures:
        dept = structure.employee.department or 'Unassigned'
        if dept not in department_stats:
            department_stats[dept] = {
                'employees': [],
                'total_salary': Decimal('0.00'),
                'count': 0,
                'avg_salary': Decimal('0.00'),
                'min_salary': structure.basic_salary,
                'max_salary': structure.basic_salary,
            }

        department_stats[dept]['employees'].append(structure)
        department_stats[dept]['total_salary'] += structure.basic_salary
        department_stats[dept]['count'] += 1

        # Track min and max salaries
        if structure.basic_salary < department_stats[dept]['min_salary']:
            department_stats[dept]['min_salary'] = structure.basic_salary
        if structure.basic_salary > department_stats[dept]['max_salary']:
            department_stats[dept]['max_salary'] = structure.basic_salary

        # Add to overall totals
        total_payroll += structure.basic_salary
        total_employees += 1

    # Calculate averages
    for dept_data in department_stats.values():
        if dept_data['count'] > 0:
            dept_data['avg_salary'] = dept_data['total_salary'] / dept_data['count']

    # Calculate overall average salary
    overall_avg_salary = total_payroll / total_employees if total_employees > 0 else Decimal('0.00')

    context = {
        'department_stats': department_stats,
        'salary_structures': salary_structures,
        'total_payroll': total_payroll,
        'total_employees': total_employees,
        'overall_avg_salary': overall_avg_salary,
    }
    return render(request, 'salary/department_salary_report.html', context)

@login_required
def grade_salary_report(request):
    """Salary grade report."""
    # Get all salary grades with employee counts
    grades = SalaryGrade.objects.filter(is_active=True).order_by('grade_type', 'minimum_salary')

    grade_stats = []
    for grade in grades:
        structures = EmployeeSalaryStructure.objects.filter(
            salary_grade=grade,
            is_active=True
        ).select_related('employee')

        total_salary = structures.aggregate(total=Sum('basic_salary'))['total'] or Decimal('0.00')
        employee_count = structures.count()
        avg_salary = total_salary / employee_count if employee_count > 0 else Decimal('0.00')

        grade_stats.append({
            'grade': grade,
            'employees': structures,
            'employee_count': employee_count,
            'total_salary': total_salary,
            'avg_salary': avg_salary,
            'utilization': (avg_salary - grade.minimum_salary) / (grade.maximum_salary - grade.minimum_salary) * 100 if grade.maximum_salary > grade.minimum_salary else 0,
        })

    context = {
        'grade_stats': grade_stats,
    }
    return render(request, 'salary/grade_salary_report.html', context)

@login_required
def custom_report(request):
    """Custom report generator."""
    if request.method == 'POST':
        report_type = request.POST.get('reportType')
        date_from = request.POST.get('dateFrom')
        date_to = request.POST.get('dateTo')
        format_type = request.POST.get('format', 'pdf')
        group_by = request.POST.get('groupBy')

        # Process the custom report request
        if report_type == 'payroll':
            return redirect('salary:payroll_summary')
        elif report_type == 'employee':
            return redirect('salary:employee_salary_report')
        elif report_type == 'department':
            return redirect('salary:department_salary_report')
        elif report_type == 'grade':
            return redirect('salary:grade_salary_report')

    # If GET request, show the form
    return render(request, 'salary/custom_report.html')

# API Views
@login_required
def calculate_salary_api(request):
    """API endpoint to calculate salary."""
    if request.method == 'POST':
        data = json.loads(request.body)
        basic_salary = Decimal(data.get('basic_salary', '0'))

        # Mock calculation
        result = {
            'basic_salary': float(basic_salary),
            'allowances': float(basic_salary * Decimal('0.2')),
            'deductions': float(basic_salary * Decimal('0.1')),
            'tax': float(basic_salary * Decimal('0.15')),
            'net_salary': float(basic_salary * Decimal('0.95'))
        }

        return JsonResponse(result)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

@login_required
def employee_components_api(request, employee_id):
    """API endpoint to get employee salary components."""
    try:
        salary_structure = EmployeeSalaryStructure.objects.get(
            employee_id=employee_id,
            is_active=True
        )

        components = EmployeeSalaryComponent.objects.filter(
            salary_structure=salary_structure,
            is_active=True
        ).select_related('component')

        result = []
        for comp in components:
            result.append({
                'id': comp.id,
                'name': comp.component.name,
                'type': comp.component.component_type,
                'amount': float(comp.get_calculated_amount(salary_structure.basic_salary))
            })

        return JsonResponse({'components': result})

    except EmployeeSalaryStructure.DoesNotExist:
        return JsonResponse({'error': 'Employee salary structure not found'}, status=404)

@login_required
def generate_bulk_payslips(request):
    """Generate payslips in bulk for a selected payroll period."""
    if request.method == 'POST':
        period_id = request.POST.get('period_id')
        if not period_id:
            messages.error(request, 'Please select a payroll period.')
            return redirect('salary:payslip_list')

        try:
            period = PayrollPeriod.objects.get(pk=period_id)

            # Get all approved payroll records for this period
            payrolls = EnhancedPayroll.objects.filter(
                payroll_period=period,
                status='approved'
            ).select_related('employee')

            if not payrolls.exists():
                messages.warning(request, f'No approved payroll records found for {period.period_name}.')
                return redirect('salary:payslip_list')

            # Generate payslips for each payroll record
            generated_count = 0
            for payroll in payrolls:
                # Check if payslip already exists
                existing_payslip = PaySlip.objects.filter(payroll=payroll).first()
                if not existing_payslip:
                    # Generate payslip number
                    payslip_number = f"PS-{period.start_date.strftime('%Y%m')}-{payroll.employee.employee_id}-{payroll.pk}"

                    # Create payslip
                    PaySlip.objects.create(
                        payroll=payroll,
                        payslip_number=payslip_number,
                        generated_by=request.user,
                        generated_at=timezone.now(),
                        is_final=True
                    )
                    generated_count += 1

            if generated_count > 0:
                messages.success(request, f'Successfully generated {generated_count} payslips for {period.period_name}.')
            else:
                messages.info(request, f'All payslips for {period.period_name} have already been generated.')

        except PayrollPeriod.DoesNotExist:
            messages.error(request, 'Selected payroll period not found.')
        except Exception as e:
            messages.error(request, f'Error generating payslips: {str(e)}')

        return redirect('salary:payslip_list')

    # GET request - show the form
    periods = PayrollPeriod.objects.filter(
        status__in=['approved', 'paid']
    ).order_by('-start_date')[:12]

    context = {
        'periods': periods,
    }
    return render(request, 'salary/generate_bulk_payslips.html', context)
