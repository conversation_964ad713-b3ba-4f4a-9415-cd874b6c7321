{% extends 'teachers/base.html' %}
{% load static %}

{% block title %}Result Entry - {{ teacher.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
    .result-entry-card {
        border-left: 4px solid #28a745;
    }
    .student-row {
        transition: all 0.3s ease;
    }
    .student-row:hover {
        background-color: #f8f9fa;
    }
    .score-input {
        width: 80px;
    }
    .grade-display {
        font-weight: bold;
        padding: 4px 8px;
        border-radius: 4px;
    }
    .grade-A { background-color: #d4edda; color: #155724; }
    .grade-B { background-color: #d1ecf1; color: #0c5460; }
    .grade-C { background-color: #fff3cd; color: #856404; }
    .grade-D { background-color: #f8d7da; color: #721c24; }
    .grade-E { background-color: #f5c6cb; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <div>
                    <h4 class="mb-sm-0">Result Entry</h4>
                    <p class="text-muted mt-1">Enter exam results manually or upload in bulk</p>
                </div>
                <div class="page-title-right d-flex align-items-center">
                    <a href="{% url 'teachers:bulk_result_upload' %}"
                       class="btn btn-success me-3">
                        <i class="fas fa-upload me-2"></i>Bulk Upload
                    </a>
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'teachers:dashboard' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'teachers:exam_dashboard' %}">Exam Dashboard</a></li>
                        <li class="breadcrumb-item active">Result Entry</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="exam" class="form-label">Exam</label>
                            <select class="form-select" id="exam" name="exam">
                                <option value="">Select Exam</option>
                                {% for exam in available_exams %}
                                <option value="{{ exam.id }}" {% if selected_exam.id == exam.id %}selected{% endif %}>{{ exam.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject" class="form-label">Subject</label>
                            <select class="form-select" id="subject" name="subject">
                                <option value="">Select Subject</option>
                                {% for subject in available_subjects %}
                                <option value="{{ subject.id }}" {% if selected_subject.id == subject.id %}selected{% endif %}>{{ subject.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="grade" class="form-label">Grade</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">Select Grade</option>
                                {% for grade in available_grades %}
                                <option value="{{ grade.id }}" {% if selected_grade.id == grade.id %}selected{% endif %}>{{ grade.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Load Students</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if selected_exam and selected_subject and selected_grade %}
    <!-- Exam Information -->
    <div class="row">
        <div class="col-12">
            <div class="card result-entry-card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-muted">Exam</h6>
                            <p class="mb-0">{{ selected_exam.name }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">Subject</h6>
                            <p class="mb-0">{{ selected_subject.name }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">Grade</h6>
                            <p class="mb-0">{{ selected_grade.name }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">Total Students</h6>
                            <p class="mb-0">{{ students.count }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Result Entry Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Student Results</h4>
                    <div>
                        <button type="button" class="btn btn-success" onclick="saveAllResults()">
                            <i class="ri-save-line me-1"></i>Save All Results
                        </button>
                        <button type="button" class="btn btn-info" onclick="calculateGrades()">
                            <i class="ri-calculator-line me-1"></i>Calculate Grades
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if students %}
                    <form id="resultForm" method="post">
                        {% csrf_token %}
                        <input type="hidden" name="exam_id" value="{{ selected_exam.id }}">
                        <input type="hidden" name="subject_id" value="{{ selected_subject.id }}">
                        <input type="hidden" name="grade_id" value="{{ selected_grade.id }}">
                        
                        <div class="table-responsive">
                            <table class="table table-nowrap align-middle">
                                <thead>
                                    <tr>
                                        <th>Admission No.</th>
                                        <th>Student Name</th>
                                        <th>Score ({{ selected_subject.max_score|default:"100" }})</th>
                                        <th>Grade</th>
                                        <th>Remarks</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in students %}
                                    <tr class="student-row" data-student-id="{{ student.id }}">
                                        <td>{{ student.admission_number }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-xs me-3">
                                                    <span class="avatar-title rounded-circle bg-primary">
                                                        {{ student.first_name|first }}{{ student.last_name|first }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ student.get_full_name }}</h6>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <input type="number" 
                                                   class="form-control score-input" 
                                                   name="score_{{ student.id }}" 
                                                   min="0" 
                                                   max="{{ selected_subject.max_score|default:'100' }}"
                                                   value="{{ student.existing_score|default:'' }}"
                                                   onchange="calculateGrade(this, {{ student.id }})"
                                                   placeholder="0">
                                        </td>
                                        <td>
                                            <span id="grade_{{ student.id }}" class="grade-display">
                                                {{ student.existing_grade|default:"-" }}
                                            </span>
                                        </td>
                                        <td>
                                            <input type="text" 
                                                   class="form-control" 
                                                   name="remarks_{{ student.id }}" 
                                                   value="{{ student.existing_remarks|default:'' }}"
                                                   placeholder="Optional remarks">
                                        </td>
                                        <td>
                                            <span class="badge bg-{% if student.existing_score %}success{% else %}warning{% endif %}">
                                                {% if student.existing_score %}Entered{% else %}Pending{% endif %}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Bulk Actions</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Set Score for All</label>
                                            <div class="input-group">
                                                <input type="number" id="bulkScore" class="form-control" placeholder="Score">
                                                <button class="btn btn-outline-secondary" onclick="setBulkScore()">Apply</button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Set Remarks for All</label>
                                            <div class="input-group">
                                                <input type="text" id="bulkRemarks" class="form-control" placeholder="Remarks">
                                                <button class="btn btn-outline-secondary" onclick="setBulkRemarks()">Apply</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Statistics</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h5 id="totalStudents">{{ students.count }}</h5>
                                            <p class="text-muted mb-0">Total</p>
                                        </div>
                                        <div class="col-4">
                                            <h5 id="enteredResults">0</h5>
                                            <p class="text-muted mb-0">Entered</p>
                                        </div>
                                        <div class="col-4">
                                            <h5 id="pendingResults">{{ students.count }}</h5>
                                            <p class="text-muted mb-0">Pending</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="ri-user-line font-size-48 text-muted"></i>
                        <p class="text-muted mt-2">No students found for the selected criteria</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-file-edit-line font-size-48 text-muted"></i>
                    <h5 class="mt-3">Select Exam, Subject, and Grade</h5>
                    <p class="text-muted">Please select an exam, subject, and grade to start entering results.</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Grading system configuration
const gradingSystem = {
    'A': { min: 80, max: 100, class: 'grade-A' },
    'B': { min: 70, max: 79, class: 'grade-B' },
    'C': { min: 60, max: 69, class: 'grade-C' },
    'D': { min: 50, max: 59, class: 'grade-D' },
    'E': { min: 0, max: 49, class: 'grade-E' }
};

function calculateGrade(input, studentId) {
    const score = parseFloat(input.value);
    const gradeElement = document.getElementById(`grade_${studentId}`);
    
    if (isNaN(score) || score < 0) {
        gradeElement.textContent = '-';
        gradeElement.className = 'grade-display';
        return;
    }
    
    let grade = 'E';
    for (const [gradeKey, gradeInfo] of Object.entries(gradingSystem)) {
        if (score >= gradeInfo.min && score <= gradeInfo.max) {
            grade = gradeKey;
            break;
        }
    }
    
    gradeElement.textContent = grade;
    gradeElement.className = `grade-display ${gradingSystem[grade].class}`;
    
    updateStatistics();
}

function calculateGrades() {
    document.querySelectorAll('.score-input').forEach(input => {
        if (input.value) {
            const studentId = input.closest('tr').dataset.studentId;
            calculateGrade(input, studentId);
        }
    });
}

function setBulkScore() {
    const bulkScore = document.getElementById('bulkScore').value;
    if (bulkScore) {
        document.querySelectorAll('.score-input').forEach(input => {
            input.value = bulkScore;
            const studentId = input.closest('tr').dataset.studentId;
            calculateGrade(input, studentId);
        });
    }
}

function setBulkRemarks() {
    const bulkRemarks = document.getElementById('bulkRemarks').value;
    if (bulkRemarks) {
        document.querySelectorAll('input[name^="remarks_"]').forEach(input => {
            input.value = bulkRemarks;
        });
    }
}

function updateStatistics() {
    const totalStudents = document.querySelectorAll('.student-row').length;
    const enteredResults = document.querySelectorAll('.score-input').length;
    let filledResults = 0;
    
    document.querySelectorAll('.score-input').forEach(input => {
        if (input.value && input.value.trim() !== '') {
            filledResults++;
        }
    });
    
    document.getElementById('enteredResults').textContent = filledResults;
    document.getElementById('pendingResults').textContent = totalStudents - filledResults;
}

function saveAllResults() {
    if (confirm('Are you sure you want to save all results? This action cannot be undone.')) {
        document.getElementById('resultForm').submit();
    }
}

// Initialize statistics on page load
document.addEventListener('DOMContentLoaded', function() {
    updateStatistics();
    calculateGrades();
});
</script>
{% endblock %}
