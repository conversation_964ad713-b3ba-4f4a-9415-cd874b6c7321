from django.urls import path
from . import views

app_name = 'timetable'

urlpatterns = [
    # Dashboard
    path('', views.timetable_dashboard, name='dashboard'),
    
    # Timetable Management
    path('view/', views.timetable_view, name='view'),
    path('create/', views.create_timetable, name='create'),
    path('edit/<int:timetable_id>/', views.edit_timetable, name='edit'),
    path('delete/<int:timetable_id>/', views.delete_timetable, name='delete'),
    
    # Grade-specific timetables
    path('grade/<int:grade_id>/', views.grade_timetable, name='grade_timetable'),
    
    # Teacher timetables
    path('teacher/<int:teacher_id>/', views.teacher_timetable, name='teacher_timetable'),
    
    # Classroom timetables
    path('classroom/<int:classroom_id>/', views.classroom_timetable, name='classroom_timetable'),
    
    # Reports and exports
    path('reports/', views.timetable_reports, name='reports'),
    path('export/', views.export_timetable, name='export'),
]
