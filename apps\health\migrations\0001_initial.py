# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("students", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="StudentHealthRecords",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "blood_group",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("A+", "A+"),
                            ("A-", "A-"),
                            ("B+", "B+"),
                            ("B-", "B-"),
                            ("AB+", "AB+"),
                            ("AB-", "AB-"),
                            ("O+", "O+"),
                            ("O-", "O-"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                ("allergies", models.TextField(blank=True, null=True)),
                ("chronic_conditions", models.TextField(blank=True, null=True)),
                ("medications", models.TextField(blank=True, null=True)),
                (
                    "emergency_contact_name",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                (
                    "emergency_contact_phone",
                    models.Char<PERSON>ield(blank=True, max_length=20, null=True),
                ),
                (
                    "emergency_contact_relationship",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "insurance_provider",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "insurance_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentHealthVisit",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("visit_date", models.DateTimeField()),
                ("complaint", models.TextField()),
                ("diagnosis", models.TextField(blank=True, null=True)),
                ("treatment", models.TextField(blank=True, null=True)),
                ("medication", models.TextField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "attended_by",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("referred_to_hospital", models.BooleanField(default=False)),
                (
                    "hospital_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("parent_notified", models.BooleanField(default=False)),
                ("notification_date", models.DateTimeField(blank=True, null=True)),
                ("follow_up_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[("open", "Open"), ("closed", "Closed")],
                        default="open",
                        max_length=50,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
            ],
        ),
    ]
