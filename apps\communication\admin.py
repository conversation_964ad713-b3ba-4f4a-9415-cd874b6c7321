from django.contrib import admin
from .models import (
    Chat, OnlineChat, GroupMessage, MyFriends, Feedback,
    Notification, NotificationPreference
)

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'notification_type', 'channel', 'status', 'scheduled_time', 'sent_at', 'read_at', 'created_at')
    search_fields = ('user__email', 'title', 'message')
    list_filter = ('notification_type', 'channel', 'status', 'created_at')
    ordering = ('-created_at',)

@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'in_app', 'email', 'sms')
    search_fields = ('user__email',)
    ordering = ('user',)

@admin.register(Chat)
class ChatAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation_id', 'grade', 'sender_index', 'sender_type', 'receiver_index', 'receiver_type', 'msg', 'date', 'time', '_isread')
    search_fields = ('conversation_id', 'grade', 'sender_index', 'receiver_index')
    list_filter = ('grade', 'sender_type', 'receiver_type', 'date', '_isread')
    ordering = ('-date', '-time', 'conversation_id')

@admin.register(OnlineChat)
class OnlineChatAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation_id', 'user_index', 'msg', 'user_type', '_isread', 'date', 'time', 'has_attachment', 'attachment_url')
    search_fields = ('conversation_id', 'user_index', 'msg')
    list_filter = ('user_type', 'date', 'has_attachment', '_isread')
    ordering = ('-date', '-time', 'conversation_id')

@admin.register(GroupMessage)
class GroupMessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation_id', 'message', 'sender_index', 'sender_type', 'group_id', 'grade', 'date', 'time', 'has_attachment', 'attachment_type', 'attachment_url')
    search_fields = ('conversation_id', 'group_id', 'sender_index', 'grade')
    list_filter = ('grade', 'sender_type', 'date', 'has_attachment')
    ordering = ('-date', '-time', 'conversation_id')

@admin.register(MyFriends)
class MyFriendsAdmin(admin.ModelAdmin):
    list_display = ('id', 'my_index', 'friend_index', '_status', 'conversation_id', 'my_type', 'friend_type', '_isread')
    search_fields = ('my_index', 'friend_index', 'conversation_id')
    list_filter = ('my_type', 'friend_type', '_status', '_isread')
    ordering = ('my_index', 'friend_index')

admin.site.register(Feedback)
