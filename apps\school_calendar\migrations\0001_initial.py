# Generated by Django 5.2.1 on 2025-05-21 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SchoolCalendar",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("term", "Term"),
                            ("holiday", "Holiday"),
                            ("exam", "Exam"),
                            ("event", "Event"),
                        ],
                        max_length=50,
                    ),
                ),
                ("is_holiday", models.BooleanField(default=False)),
                ("term", models.IntegerField(blank=True, null=True)),
                ("year", models.PositiveIntegerField()),
                ("color", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=7, null=True)),
                ("created_by", models.Inte<PERSON><PERSON>ield(blank=True, null=True)),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
    ]
