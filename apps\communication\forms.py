from django import forms
from .models import Feedback, Notification, NotificationPreference, Chat

class FeedbackForm(forms.ModelForm):
    class Meta:
        model = Feedback
        fields = ['message']
        widgets = {
            'message': forms.Textarea(attrs={'class': 'w-full px-3 py-2 border rounded', 'rows': 3, 'placeholder': 'Enter your feedback...'}),
        }

class NotificationForm(forms.ModelForm):
    class Meta:
        model = Notification
        fields = ['user', 'title', 'message', 'notification_type', 'channel', 'scheduled_time']
        widgets = {
            'scheduled_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        }

class NotificationPreferenceForm(forms.ModelForm):
    class Meta:
        model = NotificationPreference
        fields = ['in_app', 'email', 'sms']
        widgets = {
            'in_app': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'email': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'sms': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class MessageForm(forms.ModelForm):
    class Meta:
        model = Chat
        fields = ['receiver_index', 'receiver_type', 'msg']
        widgets = {
            'receiver_index': forms.NumberInput(attrs={'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm transition-colors'}),
            'receiver_type': forms.TextInput(attrs={'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm transition-colors'}),
            'msg': forms.Textarea(attrs={'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm transition-colors', 'rows': 4, 'placeholder': 'Type your message here...'})
        }
        labels = {
            'receiver_index': 'Recipient ID',
            'receiver_type': 'Recipient Type',
            'msg': 'Message',
        }