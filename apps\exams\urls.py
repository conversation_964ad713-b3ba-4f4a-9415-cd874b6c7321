from django.urls import path
from . import views

app_name = 'exams'

urlpatterns = [
    # Dashboard
    path('', views.exam_dashboard, name='exam_dashboard'),
    path('dashboard/', views.exam_dashboard, name='dashboard'),  # Alternative URL for compatibility

    # Exam Management
    path('exams/', views.exam_list, name='exam_list'),
    path('exams/add/', views.add_exam, name='add_exam'),
    path('exams/<int:exam_id>/', views.exam_detail, name='exam_detail'),
    path('exams/<int:exam_id>/edit/', views.edit_exam, name='edit_exam'),
    path('exams/<int:exam_id>/delete/', views.delete_exam, name='delete_exam'),

    # Exam Type Management
    path('types/', views.exam_type_list, name='exam_type_list'),
    path('types/add/', views.add_exam_type, name='add_exam_type'),
    path('types/<int:type_id>/edit/', views.edit_exam_type, name='edit_exam_type'),

    # Timetable Management
    path('timetables/', views.timetable_list, name='timetable_list'),
    path('timetables/add/', views.add_timetable, name='add_timetable'),
    path('timetables/<int:timetable_id>/', views.timetable_detail, name='timetable_detail'),
    path('timetables/<int:timetable_id>/edit/', views.edit_timetable, name='edit_timetable'),
    path('timetables/<int:timetable_id>/publish/', views.publish_timetable, name='publish_timetable'),
    path('exams/<int:exam_id>/generate-timetable/', views.generate_timetable, name='generate_timetable'),

    # Result Entry
    path('results/', views.result_entry_dashboard, name='result_entry_dashboard'),
    path('results/entry/<int:timetable_id>/', views.result_entry_form, name='result_entry_form'),
    path('exams/<int:exam_id>/bulk-upload/', views.bulk_result_upload, name='bulk_result_upload'),
    path('bulk-uploads/<int:upload_id>/', views.bulk_upload_detail, name='bulk_upload_detail'),
    path('exams/<int:exam_id>/download-template/', views.download_template, name='download_template'),

    # Performance Analysis
    path('performance/', views.performance_dashboard, name='performance_dashboard'),
    path('exams/<int:exam_id>/analysis/', views.exam_performance_analysis, name='exam_performance_analysis'),
    path('subjects/<int:subject_id>/analysis/', views.subject_performance_analysis, name='subject_performance_analysis'),

    # Reports
    path('reports/student/<int:student_id>/exam/<int:exam_id>/', views.generate_report_card, name='generate_report_card'),
    path('reports/class/<int:exam_id>/<int:grade_id>/', views.class_report, name='class_report'),

    # Rankings
    path('rankings/', views.ranking_dashboard, name='ranking_dashboard'),
    path('exams/<int:exam_id>/generate-rankings/', views.generate_rankings, name='generate_rankings'),
]
