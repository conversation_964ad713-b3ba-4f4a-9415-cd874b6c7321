# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("students", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TransportRoute",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "distance",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                ("estimated_time", models.IntegerField(blank=True, null=True)),
                (
                    "fee",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TransportVehicle",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("registration_number", models.CharField(max_length=50)),
                (
                    "vehicle_type",
                    models.CharField(
                        choices=[("bus", "Bus"), ("van", "Van"), ("car", "Car")],
                        max_length=50,
                    ),
                ),
                ("capacity", models.IntegerField()),
                (
                    "driver_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "driver_contact",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("insurance_expiry", models.DateField(blank=True, null=True)),
                ("service_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("maintenance", "Maintenance"),
                            ("inactive", "Inactive"),
                        ],
                        default="active",
                        max_length=50,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TransportStop",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("sequence", models.IntegerField()),
                ("arrival_time", models.TimeField(blank=True, null=True)),
                ("departure_time", models.TimeField(blank=True, null=True)),
                (
                    "coordinates",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "route",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="transport.transportroute",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TransportAssignment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("fee", models.DecimalField(decimal_places=2, max_digits=11)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("inactive", "Inactive")],
                        default="active",
                        max_length=50,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
                (
                    "route",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="transport.transportroute",
                    ),
                ),
                (
                    "stop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="transport.transportstop",
                    ),
                ),
                (
                    "vehicle",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="transport.transportvehicle",
                    ),
                ),
            ],
        ),
    ]
