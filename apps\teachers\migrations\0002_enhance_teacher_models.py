# Generated by Django 5.2.1 on 2025-06-10 07:51

import apps.teachers.models
import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0002_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="teacher",
            options={
                "ordering": ["last_name", "first_name"],
                "verbose_name": "Teacher",
                "verbose_name_plural": "Teachers",
            },
        ),
        migrations.RenameField(
            model_name="teacher",
            old_name="salary",
            new_name="basic_salary",
        ),
        migrations.RenameField(
            model_name="teacher",
            old_name="qualification",
            new_name="professional_qualification",
        ),
        migrations.AddField(
            model_name="teacher",
            name="alternative_email",
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="alternative_phone",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="city",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="confirmation_date",
            field=models.DateField(
                blank=True, help_text="Date of confirmation", null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="contract_end_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="contract_start_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="county",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="teachers_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="current_class",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="current_class_teacher",
                to="academics.classroom",
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="current_teaching_load",
            field=models.PositiveIntegerField(
                default=0, help_text="Number of lessons per week"
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="date_of_birth",
            field=models.DateField(default=datetime.date.today),
        ),
        migrations.AddField(
            model_name="teacher",
            name="emergency_contact_address",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="emergency_contact_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="emergency_contact_phone",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="emergency_contact_relationship",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="employee_id",
            field=models.CharField(
                blank=True,
                help_text="Unique employee identifier",
                max_length=20,
                null=True,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="employment_date",
            field=models.DateField(
                default=datetime.date.today, help_text="Date of first employment"
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="employment_status",
            field=models.CharField(
                choices=[
                    ("probation", "Probation"),
                    ("confirmed", "Confirmed"),
                    ("contract", "Contract"),
                    ("temporary", "Temporary"),
                ],
                default="probation",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="first_name",
            field=models.CharField(default="Unknown", max_length=100),
        ),
        migrations.AddField(
            model_name="teacher",
            name="house_allowance",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=11, null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="is_class_teacher",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="teacher",
            name="is_department_head",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="teacher",
            name="kra_pin",
            field=models.CharField(
                blank=True, help_text="KRA PIN number", max_length=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="last_evaluation_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="last_name",
            field=models.CharField(default="Teacher", max_length=100),
        ),
        migrations.AddField(
            model_name="teacher",
            name="marital_status",
            field=models.CharField(
                choices=[
                    ("single", "Single"),
                    ("married", "Married"),
                    ("divorced", "Divorced"),
                    ("widowed", "Widowed"),
                ],
                default="single",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="maximum_teaching_load",
            field=models.PositiveIntegerField(
                default=30, help_text="Maximum lessons per week"
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="middle_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="nhif_number",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="nssf_number",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="other_allowances",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=11, null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="passport_number",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="performance_rating",
            field=models.DecimalField(
                blank=True,
                decimal_places=1,
                help_text="Performance rating out of 5",
                max_digits=3,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="photo",
            field=models.ImageField(
                blank=True,
                null=True,
                upload_to=apps.teachers.models.teacher_photo_upload_path,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="place_of_birth",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="postal_address",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="postal_code",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="preferred_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="previous_schools",
            field=models.TextField(
                blank=True, help_text="Previous teaching experience", null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="probation_end_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="teacher_registration_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="teaching_experience_years",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="teacher",
            name="total_salary",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=11, null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="transport_allowance",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=11, null=True
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="tsc_number",
            field=models.CharField(
                blank=True,
                help_text="TSC registration number",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="teacher",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AddField(
            model_name="teacher",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="teachers_updated",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="address",
            field=models.CharField(default="Address not provided", max_length=255),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="email",
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="employment_type",
            field=models.CharField(
                choices=[
                    ("TSC", "TSC"),
                    ("BOM", "BOM"),
                    ("contract", "Contract"),
                    ("volunteer", "Volunteer"),
                    ("intern", "Intern"),
                ],
                default="TSC",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="full_name",
            field=models.CharField(editable=False, max_length=255),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="gender",
            field=models.CharField(
                choices=[("Male", "Male"), ("Female", "Female")],
                default="Male",
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="i_name",
            field=models.CharField(
                blank=True, help_text="Name abbreviation", max_length=255, null=True
            ),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="image_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="index_number",
            field=models.BigIntegerField(blank=True, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="national_id",
            field=models.CharField(blank=True, max_length=20, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="phone",
            field=models.CharField(default="0700000000", max_length=20),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="reg_date",
            field=models.DateField(default=datetime.date.today),
        ),
        migrations.AlterField(
            model_name="teacher",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("inactive", "Inactive"),
                    ("on_leave", "On Leave"),
                    ("suspended", "Suspended"),
                    ("terminated", "Terminated"),
                    ("retired", "Retired"),
                    ("transferred", "Transferred"),
                ],
                default="active",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="TeacherDepartmentTransfer",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "transfer_type",
                    models.CharField(
                        choices=[
                            ("promotion", "Promotion Transfer"),
                            ("lateral", "Lateral Transfer"),
                            ("disciplinary", "Disciplinary Transfer"),
                            ("request", "Request Transfer"),
                            ("administrative", "Administrative Transfer"),
                        ],
                        default="lateral",
                        max_length=20,
                    ),
                ),
                (
                    "transfer_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("request_date", models.DateField()),
                ("effective_date", models.DateField(blank=True, null=True)),
                ("reason", models.TextField()),
                ("approval_date", models.DateTimeField(blank=True, null=True)),
                ("rejection_reason", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_transfers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "from_department",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transfers_from",
                        to="academics.department",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="requested_transfers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="department_transfers",
                        to="teachers.teacher",
                    ),
                ),
                (
                    "to_department",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfers_to",
                        to="academics.department",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Department Transfer",
                "verbose_name_plural": "Teacher Department Transfers",
                "ordering": ["-request_date"],
            },
        ),
        migrations.CreateModel(
            name="TeacherDocument",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("cv", "Curriculum Vitae"),
                            ("certificate", "Educational Certificate"),
                            ("id_copy", "ID Copy"),
                            ("passport_copy", "Passport Copy"),
                            ("tsc_certificate", "TSC Certificate"),
                            ("kra_pin", "KRA PIN Certificate"),
                            ("nhif_card", "NHIF Card"),
                            ("nssf_card", "NSSF Card"),
                            ("contract", "Employment Contract"),
                            ("recommendation", "Recommendation Letter"),
                            ("medical_report", "Medical Report"),
                            ("police_clearance", "Police Clearance"),
                            ("other", "Other Document"),
                        ],
                        max_length=20,
                    ),
                ),
                ("document_name", models.CharField(max_length=255)),
                (
                    "document_file",
                    models.FileField(
                        upload_to=apps.teachers.models.teacher_document_upload_path
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                ("is_verified", models.BooleanField(default=False)),
                ("verification_date", models.DateTimeField(blank=True, null=True)),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="teachers.teacher",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_teacher_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Document",
                "verbose_name_plural": "Teacher Documents",
                "ordering": ["-upload_date"],
            },
        ),
        migrations.CreateModel(
            name="TeacherEmploymentHistory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "status_change",
                    models.CharField(
                        choices=[
                            ("hired", "Hired"),
                            ("confirmed", "Confirmed"),
                            ("promoted", "Promoted"),
                            ("transferred", "Transferred"),
                            ("suspended", "Suspended"),
                            ("terminated", "Terminated"),
                            ("resigned", "Resigned"),
                            ("retired", "Retired"),
                            ("leave_start", "Leave Started"),
                            ("leave_end", "Leave Ended"),
                        ],
                        max_length=20,
                    ),
                ),
                ("effective_date", models.DateField()),
                (
                    "previous_status",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("new_status", models.CharField(max_length=20)),
                ("reason", models.TextField(blank=True, null=True)),
                (
                    "reference_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="employment_history",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Employment History",
                "verbose_name_plural": "Teacher Employment History",
                "ordering": ["-effective_date"],
            },
        ),
        migrations.CreateModel(
            name="TeacherPerformanceEvaluation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "evaluation_type",
                    models.CharField(
                        choices=[
                            ("annual", "Annual Evaluation"),
                            ("mid_year", "Mid-Year Review"),
                            ("probation", "Probation Review"),
                            ("promotion", "Promotion Review"),
                            ("disciplinary", "Disciplinary Review"),
                        ],
                        default="annual",
                        max_length=20,
                    ),
                ),
                ("evaluation_period_start", models.DateField()),
                ("evaluation_period_end", models.DateField()),
                ("evaluation_date", models.DateField()),
                (
                    "teaching_effectiveness",
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (1, "Poor"),
                            (2, "Below Average"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "classroom_management",
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (1, "Poor"),
                            (2, "Below Average"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "student_engagement",
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (1, "Poor"),
                            (2, "Below Average"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "professional_development",
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (1, "Poor"),
                            (2, "Below Average"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "collaboration",
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (1, "Poor"),
                            (2, "Below Average"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "punctuality",
                    models.PositiveIntegerField(
                        blank=True,
                        choices=[
                            (1, "Poor"),
                            (2, "Below Average"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "overall_rating",
                    models.DecimalField(
                        blank=True, decimal_places=1, max_digits=3, null=True
                    ),
                ),
                ("strengths", models.TextField(blank=True, null=True)),
                ("areas_for_improvement", models.TextField(blank=True, null=True)),
                ("goals_for_next_period", models.TextField(blank=True, null=True)),
                ("evaluator_comments", models.TextField(blank=True, null=True)),
                ("teacher_comments", models.TextField(blank=True, null=True)),
                ("is_approved", models.BooleanField(default=False)),
                ("approval_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_evaluations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "evaluator",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_evaluations",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Performance Evaluation",
                "verbose_name_plural": "Teacher Performance Evaluations",
                "ordering": ["-evaluation_date"],
            },
        ),
        migrations.CreateModel(
            name="TeacherQualification",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "qualification_type",
                    models.CharField(
                        choices=[
                            ("certificate", "Certificate"),
                            ("diploma", "Diploma"),
                            ("degree", "Bachelor's Degree"),
                            ("masters", "Master's Degree"),
                            ("phd", "PhD"),
                            ("professional", "Professional Certification"),
                            ("training", "Training Course"),
                        ],
                        max_length=20,
                    ),
                ),
                ("qualification_name", models.CharField(max_length=255)),
                ("institution", models.CharField(max_length=255)),
                (
                    "field_of_study",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "grade_obtained",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("year_obtained", models.PositiveIntegerField()),
                (
                    "certificate_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "verification_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Verification"),
                            ("verified", "Verified"),
                            ("rejected", "Rejected"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("expiry_date", models.DateField(blank=True, null=True)),
                ("is_teaching_qualification", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="qualifications",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Qualification",
                "verbose_name_plural": "Teacher Qualifications",
                "ordering": ["-year_obtained"],
            },
        ),
        migrations.CreateModel(
            name="TeacherAssignment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("section", models.CharField(blank=True, max_length=10, null=True)),
                (
                    "assignment_type",
                    models.CharField(
                        choices=[
                            ("primary", "Primary Assignment"),
                            ("secondary", "Secondary Assignment"),
                            ("substitute", "Substitute Assignment"),
                            ("temporary", "Temporary Assignment"),
                        ],
                        default="primary",
                        max_length=20,
                    ),
                ),
                ("academic_year", models.PositiveIntegerField(default=2024)),
                ("term", models.PositiveIntegerField(default=1)),
                ("lessons_per_week", models.PositiveIntegerField(default=1)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assignments",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Assignment",
                "verbose_name_plural": "Teacher Assignments",
                "ordering": ["-academic_year", "-term", "subject__name"],
                "unique_together": {
                    ("teacher", "subject", "grade", "academic_year", "term")
                },
            },
        ),
        migrations.CreateModel(
            name="TeacherSubjectSpecialization",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "expertise_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        default="intermediate",
                        max_length=20,
                    ),
                ),
                (
                    "certification_status",
                    models.CharField(
                        choices=[
                            ("certified", "Certified"),
                            ("not_certified", "Not Certified"),
                            ("pending", "Pending Certification"),
                            ("expired", "Expired"),
                        ],
                        default="not_certified",
                        max_length=20,
                    ),
                ),
                ("years_of_experience", models.PositiveIntegerField(default=0)),
                ("certification_date", models.DateField(blank=True, null=True)),
                (
                    "certification_body",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "certification_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "can_teach_grade_levels",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated grade levels",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_specializations",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Subject Specialization",
                "verbose_name_plural": "Teacher Subject Specializations",
                "ordering": ["subject__name"],
                "unique_together": {("teacher", "subject")},
            },
        ),
        migrations.CreateModel(
            name="TeacherWorkload",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("academic_year", models.PositiveIntegerField(default=2024)),
                ("term", models.PositiveIntegerField(default=1)),
                ("total_lessons_per_week", models.PositiveIntegerField(default=0)),
                (
                    "total_contact_hours",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("number_of_subjects", models.PositiveIntegerField(default=0)),
                ("number_of_classes", models.PositiveIntegerField(default=0)),
                ("is_class_teacher", models.BooleanField(default=False)),
                ("is_department_head", models.BooleanField(default=False)),
                ("is_games_master", models.BooleanField(default=False)),
                ("is_club_patron", models.BooleanField(default=False)),
                ("other_responsibilities", models.TextField(blank=True, null=True)),
                (
                    "workload_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Percentage of maximum workload",
                        max_digits=5,
                    ),
                ),
                ("is_overloaded", models.BooleanField(default=False)),
                (
                    "overtime_hours",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("calculation_date", models.DateField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workloads",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Teacher Workload",
                "verbose_name_plural": "Teacher Workloads",
                "ordering": ["-academic_year", "-term"],
                "unique_together": {("teacher", "academic_year", "term")},
            },
        ),
    ]
