from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg, F
from django.db import models
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
import csv

from .models import (
    StudentHealthRecords, StudentHealthVisit, MedicalCondition,
    Medication, Immunization, EmergencyContact, HealthInsurance, HealthAlert
)
from .forms import (
    StudentHealthRecordsForm, StudentHealthVisitForm, MedicalConditionForm,
    MedicationForm, ImmunizationForm, EmergencyContactForm, HealthSearchForm
)
from apps.students.models import Student

@login_required
def health_dashboard(request):
    """Health management dashboard with statistics and alerts."""
    context = {
        'total_health_records': StudentHealthRecords.objects.count(),
        'total_health_visits': StudentHealthVisit.objects.count(),
        'total_medical_conditions': MedicalCondition.objects.filter(is_active=True).count(),
        'total_medications': Medication.objects.filter(is_active=True).count(),
        'total_immunizations': Immunization.objects.count(),
        'active_alerts': HealthAlert.objects.filter(status='active').count(),
        'urgent_alerts': HealthAlert.objects.filter(status='active', priority='urgent').count(),
        'overdue_immunizations': Immunization.objects.filter(
            next_due_date__lte=date.today(),
            dose_number__lt=F('total_doses_required')
        ).count(),
        'recent_visits': StudentHealthVisit.objects.select_related('student').order_by('-visit_date')[:10],
        'urgent_conditions': MedicalCondition.objects.filter(
            is_active=True, severity='critical'
        ).select_related('student')[:5],
        'emergency_medications': Medication.objects.filter(
            is_active=True, emergency_medication=True
        ).select_related('student')[:5],
        'recent_alerts': HealthAlert.objects.filter(status='active').order_by('-created_at')[:10],
    }
    return render(request, 'health/dashboard.html', context)

# Health Records Management Views
@login_required
def health_records_list(request):
    """List all student health records with search functionality."""
    form = HealthSearchForm(request.GET)
    records = StudentHealthRecords.objects.select_related('student').all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')

        if search_query:
            records = records.filter(
                Q(student__full_name__icontains=search_query) |
                Q(student__index_number__icontains=search_query) |
                Q(allergies__icontains=search_query) |
                Q(chronic_conditions__icontains=search_query) |
                Q(medications__icontains=search_query)
            )

    records = records.order_by('student__full_name')

    paginator = Paginator(records, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'records': page_obj,
    }
    return render(request, 'health/health_records_list.html', context)

@login_required
def health_record_detail(request, record_id):
    """Display detailed health information for a student."""
    record = get_object_or_404(StudentHealthRecords, id=record_id)
    student = record.student

    # Get related health information
    medical_conditions = MedicalCondition.objects.filter(student=student, is_active=True)
    medications = Medication.objects.filter(student=student, is_active=True)
    immunizations = Immunization.objects.filter(student=student).order_by('-date_administered')
    emergency_contacts = EmergencyContact.objects.filter(student=student).order_by('priority')
    health_visits = StudentHealthVisit.objects.filter(student=student).order_by('-visit_date')[:10]
    health_alerts = HealthAlert.objects.filter(student=student, status='active')

    context = {
        'record': record,
        'student': student,
        'medical_conditions': medical_conditions,
        'medications': medications,
        'immunizations': immunizations,
        'emergency_contacts': emergency_contacts,
        'health_visits': health_visits,
        'health_alerts': health_alerts,
    }
    return render(request, 'health/health_record_detail.html', context)

@login_required
def add_health_record(request):
    """Add a new student health record."""
    if request.method == 'POST':
        form = StudentHealthRecordsForm(request.POST)
        if form.is_valid():
            student = form.cleaned_data['student_index']

            # Check if record already exists
            existing_record = StudentHealthRecords.objects.filter(student=student).first()
            if existing_record:
                messages.warning(request, f'Health record already exists for {student.full_name}.')
                return redirect('health:health_record_detail', record_id=existing_record.id)

            record = form.save(commit=False)
            record.student = student
            record.save()

            messages.success(request, f'Health record created for {student.full_name}.')
            return redirect('health:health_record_detail', record_id=record.id)
    else:
        form = StudentHealthRecordsForm()

    context = {'form': form}
    return render(request, 'health/add_health_record.html', context)

@login_required
def edit_health_record(request, record_id):
    """Edit an existing health record."""
    record = get_object_or_404(StudentHealthRecords, id=record_id)

    if request.method == 'POST':
        form = StudentHealthRecordsForm(request.POST, instance=record)
        if form.is_valid():
            form.save()
            messages.success(request, f'Health record updated for {record.student.full_name}.')
            return redirect('health:health_record_detail', record_id=record.id)
    else:
        form = StudentHealthRecordsForm(instance=record)
        form.fields['student_index'].initial = record.student.index_number

    context = {'form': form, 'record': record}
    return render(request, 'health/edit_health_record.html', context)

# Health Visits Management
@login_required
def health_visits_list(request):
    """List all health visits."""
    visits = StudentHealthVisit.objects.select_related('student').order_by('-visit_date')

    paginator = Paginator(visits, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'visits': page_obj,
    }
    return render(request, 'health/health_visits_list.html', context)

@login_required
def health_visit_detail(request, visit_id):
    """Display detailed information about a health visit."""
    visit = get_object_or_404(StudentHealthVisit, id=visit_id)

    context = {'visit': visit}
    return render(request, 'health/health_visit_detail.html', context)

@login_required
def add_health_visit(request):
    """Record a new health visit."""
    if request.method == 'POST':
        form = StudentHealthVisitForm(request.POST)
        if form.is_valid():
            student = form.cleaned_data['student_index']
            visit = form.save(commit=False)
            visit.student = student
            visit.save()

            # Create alert if parent needs to be notified
            if visit.parent_notified:
                HealthAlert.objects.create(
                    student=student,
                    alert_type='emergency_alert',
                    title='Health Visit - Parent Notification',
                    message=f'Student visited health center on {visit.visit_date.date()}. Complaint: {visit.complaint}',
                    priority='medium'
                )

            messages.success(request, f'Health visit recorded for {student.full_name}.')
            return redirect('health:health_visit_detail', visit_id=visit.id)
    else:
        form = StudentHealthVisitForm()
        form.fields['visit_date'].initial = timezone.now()

    context = {'form': form}
    return render(request, 'health/add_health_visit.html', context)

@login_required
def health_reports(request):
    """Generate health reports and analytics."""
    from django.db.models import Count, Q
    from datetime import date, timedelta

    # Basic statistics
    total_health_records = StudentHealthRecords.objects.count()
    total_students_with_records = StudentHealthRecords.objects.values('student').distinct().count()
    total_health_visits = StudentHealthVisit.objects.count()
    total_active_conditions = MedicalCondition.objects.filter(is_active=True).count()
    total_active_medications = Medication.objects.filter(is_active=True).count()
    total_immunizations = Immunization.objects.count()

    # Additional statistics for template
    students_with_allergies = StudentHealthRecords.objects.filter(
        allergies__isnull=False
    ).exclude(allergies='').count()

    hospital_referrals = StudentHealthVisit.objects.filter(
        treatment_given__icontains='referral'
    ).count()

    # Recent activity (last 30 days)
    thirty_days_ago = date.today() - timedelta(days=30)
    recent_visits = StudentHealthVisit.objects.filter(visit_date__gte=thirty_days_ago).count()
    recent_conditions = MedicalCondition.objects.filter(diagnosed_date__gte=thirty_days_ago).count()

    # Condition statistics
    condition_stats = MedicalCondition.objects.filter(is_active=True).values('condition_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # Severity statistics
    severity_stats = MedicalCondition.objects.filter(is_active=True).values('severity').annotate(
        count=Count('id')
    ).order_by('-count')

    # Emergency medications
    emergency_meds = Medication.objects.filter(
        is_active=True,
        emergency_medication=True
    ).select_related('student').order_by('student__first_name')

    # Overdue immunizations
    overdue_immunizations = Immunization.objects.filter(
        next_due_date__lte=date.today(),
        dose_number__lt=models.F('total_doses_required')
    ).select_related('student').order_by('next_due_date')

    # Critical conditions
    critical_conditions = MedicalCondition.objects.filter(
        is_active=True,
        severity='critical'
    ).select_related('student').order_by('diagnosed_date')

    # Blood group statistics (if available)
    blood_group_stats = StudentHealthRecords.objects.exclude(
        blood_group__isnull=True
    ).exclude(blood_group='').values('blood_group').annotate(
        count=Count('id')
    ).order_by('-count')

    # Calculate percentages for blood groups
    total_with_blood_group = sum(stat['count'] for stat in blood_group_stats)
    for stat in blood_group_stats:
        if total_with_blood_group > 0:
            stat['percentage'] = (stat['count'] / total_with_blood_group) * 100
        else:
            stat['percentage'] = 0

    # Health alerts summary
    missing_emergency_contacts = StudentHealthRecords.objects.filter(
        Q(emergency_contact_name__isnull=True) | Q(emergency_contact_name='') |
        Q(emergency_contact_phone__isnull=True) | Q(emergency_contact_phone='')
    ).count()

    incomplete_records = StudentHealthRecords.objects.filter(
        Q(blood_group__isnull=True) | Q(blood_group='') |
        Q(medical_history__isnull=True) | Q(medical_history='')
    ).count()

    pending_followups = StudentHealthVisit.objects.filter(
        follow_up_required=True,
        follow_up_date__lte=date.today()
    ).count()

    context = {
        'total_health_records': total_health_records,
        'total_students_with_records': total_students_with_records,
        'total_health_visits': total_health_visits,
        'total_active_conditions': total_active_conditions,
        'total_active_medications': total_active_medications,
        'total_immunizations': total_immunizations,
        'students_with_allergies': students_with_allergies,
        'hospital_referrals': hospital_referrals,
        'recent_visits': recent_visits,
        'recent_conditions': recent_conditions,
        'condition_stats': condition_stats,
        'severity_stats': severity_stats,
        'emergency_meds': emergency_meds,
        'overdue_immunizations': overdue_immunizations,
        'critical_conditions': critical_conditions,
        'blood_group_stats': blood_group_stats,
        'missing_emergency_contacts': missing_emergency_contacts,
        'incomplete_records': incomplete_records,
        'pending_followups': pending_followups,
    }
    return render(request, 'health/health_reports.html', context)
