from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from .models import (
    Student, Guardian, StudentGuardian, StudentDocument, 
    StudentGrade, StudentSubject, StudentStatusHistory,
    StudentPromotion, StudentGraduation
)
from apps.academics.models import Grade, Subject

User = get_user_model()

class GuardianForm(forms.ModelForm):
    """Form for guardian registration and management"""
    
    class Meta:
        model = Guardian
        fields = [
            'full_name', 'gender', 'relationship', 'phone', 'alt_phone', 
            'email', 'address', 'occupation', 'employer', 'national_id',
            'is_primary_contact', 'is_emergency_contact'
        ]
        widgets = {
            'full_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Full Name'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'relationship': forms.Select(attrs={'class': 'form-select'}),
            'phone': forms.TextInput(attrs={'class': 'form-input', 'placeholder': '+254...'}),
            'alt_phone': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Alternative Phone'}),
            'email': forms.EmailInput(attrs={'class': 'form-input', 'placeholder': '<EMAIL>'}),
            'address': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3, 'placeholder': 'Physical Address'}),
            'occupation': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Occupation'}),
            'employer': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Employer'}),
            'national_id': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'National ID Number'}),
            'is_primary_contact': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'is_emergency_contact': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }

class StudentRegistrationForm(forms.ModelForm):
    """Comprehensive student registration form"""
    
    # Guardian fields
    guardian_full_name = forms.CharField(max_length=255, label="Guardian Full Name")
    guardian_relationship = forms.ChoiceField(choices=Guardian.RELATIONSHIP_CHOICES, label="Relationship")
    guardian_phone = forms.CharField(max_length=20, label="Guardian Phone")
    guardian_email = forms.EmailField(required=False, label="Guardian Email")
    guardian_address = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), label="Guardian Address")
    
    class Meta:
        model = Student
        fields = [
            'first_name', 'middle_name', 'last_name', 'preferred_name',
            'gender', 'date_of_birth', 'place_of_birth', 'nationality', 'religion',
            'phone', 'email', 'address', 'postal_address', 'postal_code', 'city', 'county',
            'kcpe_index', 'kcpe_score', 'kcpe_year', 'primary_school', 'previous_school', 'previous_class',
            'transfer_status', 'transfer_reason', 'transfer_documents',
            'health_conditions', 'allergies', 'current_medications', 'blood_group', 'special_needs',
            'boarding_status', 'current_grade', 'current_section',
            'admission_date', 'photo'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'First Name'}),
            'middle_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Middle Name (Optional)'}),
            'last_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Last Name'}),
            'preferred_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Preferred Name (Optional)'}),
            'gender': forms.Select(attrs={'class': 'form-select'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'place_of_birth': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Place of Birth'}),
            'nationality': forms.Select(attrs={'class': 'form-select'}),
            'religion': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Religion'}),
            'phone': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Student Phone (Optional)'}),
            'email': forms.EmailInput(attrs={'class': 'form-input', 'placeholder': 'Student Email (Optional)'}),
            'address': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3, 'placeholder': 'Physical Address'}),
            'postal_address': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'P.O. Box'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Postal Code'}),
            'city': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'City/Town'}),
            'county': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'County'}),
            'kcpe_index': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'KCPE Index Number'}),
            'kcpe_score': forms.NumberInput(attrs={'class': 'form-input', 'min': 0, 'max': 500}),
            'kcpe_year': forms.NumberInput(attrs={'class': 'form-input', 'min': 2000, 'max': date.today().year}),
            'primary_school': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Primary School Name'}),
            'previous_school': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Previous School (if any)'}),
            'previous_class': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Previous Class/Grade'}),
            'transfer_status': forms.Select(attrs={'class': 'form-select'}),
            'transfer_reason': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2}),
            'transfer_documents': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2}),
            'health_conditions': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2, 'placeholder': 'Known medical conditions'}),
            'allergies': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2, 'placeholder': 'Known allergies'}),
            'current_medications': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2, 'placeholder': 'Current medications'}),
            'blood_group': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Blood Group (e.g., A+)'}),
            'special_needs': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2, 'placeholder': 'Special needs or accommodations'}),
            'boarding_status': forms.Select(attrs={'class': 'form-select'}),
            'current_grade': forms.Select(attrs={'class': 'form-select'}),
            'current_section': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Section (e.g., A, B, C)'}),
            'admission_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'photo': forms.FileInput(attrs={'class': 'form-input', 'accept': 'image/*'}),
        }
    
    def clean_date_of_birth(self):
        dob = self.cleaned_data['date_of_birth']
        if dob > date.today():
            raise ValidationError("Date of birth cannot be in the future.")
        
        # Check if student is at least 3 years old
        min_age_date = date.today() - timedelta(days=3*365)
        if dob > min_age_date:
            raise ValidationError("Student must be at least 3 years old.")
        
        return dob
    
    def clean_kcpe_score(self):
        score = self.cleaned_data.get('kcpe_score')
        if score is not None and (score < 0 or score > 500):
            raise ValidationError("KCPE score must be between 0 and 500.")
        return score
    
    def clean_admission_date(self):
        admission_date = self.cleaned_data['admission_date']
        if admission_date > date.today():
            raise ValidationError("Admission date cannot be in the future.")
        return admission_date

class StudentProfileForm(forms.ModelForm):
    """Form for editing student profile information"""
    
    class Meta:
        model = Student
        fields = [
            'first_name', 'middle_name', 'last_name', 'preferred_name',
            'phone', 'email', 'address', 'postal_address', 'postal_code', 'city', 'county',
            'religion', 'boarding_status', 'current_section', 'photo'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-input'}),
            'middle_name': forms.TextInput(attrs={'class': 'form-input'}),
            'last_name': forms.TextInput(attrs={'class': 'form-input'}),
            'preferred_name': forms.TextInput(attrs={'class': 'form-input'}),
            'phone': forms.TextInput(attrs={'class': 'form-input'}),
            'email': forms.EmailInput(attrs={'class': 'form-input'}),
            'address': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
            'postal_address': forms.TextInput(attrs={'class': 'form-input'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-input'}),
            'city': forms.TextInput(attrs={'class': 'form-input'}),
            'county': forms.TextInput(attrs={'class': 'form-input'}),
            'religion': forms.TextInput(attrs={'class': 'form-input'}),
            'boarding_status': forms.Select(attrs={'class': 'form-select'}),
            'current_section': forms.TextInput(attrs={'class': 'form-input'}),
            'photo': forms.FileInput(attrs={'class': 'form-input', 'accept': 'image/*'}),
        }

class StudentDocumentForm(forms.ModelForm):
    """Form for uploading student documents"""
    
    class Meta:
        model = StudentDocument
        fields = ['document_type', 'document_name', 'document_file', 'description']
        widgets = {
            'document_type': forms.Select(attrs={'class': 'form-select'}),
            'document_name': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Document Name'}),
            'document_file': forms.FileInput(attrs={'class': 'form-input'}),
            'description': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3, 'placeholder': 'Description (Optional)'}),
        }

class StudentGradeAssignmentForm(forms.ModelForm):
    """Form for assigning students to grades/classes"""
    
    class Meta:
        model = StudentGrade
        fields = ['grade', 'section', 'academic_year', 'term', 'assignment_type', 'assignment_date']
        widgets = {
            'grade': forms.Select(attrs={'class': 'form-select'}),
            'section': forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Section (e.g., A, B, C)'}),
            'academic_year': forms.NumberInput(attrs={'class': 'form-input', 'min': 2020, 'max': 2030}),
            'term': forms.NumberInput(attrs={'class': 'form-input', 'min': 1, 'max': 3}),
            'assignment_type': forms.Select(attrs={'class': 'form-select'}),
            'assignment_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
        }

class StudentSubjectAssignmentForm(forms.ModelForm):
    """Form for assigning subjects to students"""
    
    class Meta:
        model = StudentSubject
        fields = ['subject', 'grade', 'academic_year', 'term', 'subject_type', 'registration_date', 'teacher']
        widgets = {
            'subject': forms.Select(attrs={'class': 'form-select'}),
            'grade': forms.Select(attrs={'class': 'form-select'}),
            'academic_year': forms.NumberInput(attrs={'class': 'form-input', 'min': 2020, 'max': 2030}),
            'term': forms.NumberInput(attrs={'class': 'form-input', 'min': 1, 'max': 3}),
            'subject_type': forms.Select(attrs={'class': 'form-select'}),
            'registration_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'teacher': forms.Select(attrs={'class': 'form-select'}),
        }

class StudentStatusChangeForm(forms.ModelForm):
    """Form for changing student status"""
    
    class Meta:
        model = StudentStatusHistory
        fields = ['new_status', 'change_date', 'reason', 'supporting_documents']
        widgets = {
            'new_status': forms.Select(attrs={'class': 'form-select'}),
            'change_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'reason': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 4, 'placeholder': 'Reason for status change'}),
            'supporting_documents': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 2, 'placeholder': 'List of supporting documents'}),
        }

class BulkStudentAssignmentForm(forms.Form):
    """Form for bulk student assignments"""
    
    students = forms.ModelMultipleChoiceField(
        queryset=Student.objects.filter(status='active'),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-checkbox'}),
        label="Select Students"
    )
    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Grade/Class"
    )
    section = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Section (e.g., A, B, C)'}),
        required=False
    )
    academic_year = forms.IntegerField(
        widget=forms.NumberInput(attrs={'class': 'form-input', 'min': 2020, 'max': 2030}),
        initial=date.today().year
    )
    term = forms.IntegerField(
        widget=forms.NumberInput(attrs={'class': 'form-input', 'min': 1, 'max': 3}),
        initial=1
    )
    assignment_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
        initial=date.today
    )

class StudentSearchForm(forms.Form):
    """Form for searching students"""
    
    search_query = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search by name, student ID, or index number...'
        }),
        required=False
    )
    grade = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        required=False,
        empty_label="All Grades"
    )
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + Student.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'}),
        required=False
    )
    boarding_status = forms.ChoiceField(
        choices=[('', 'All')] + Student.BOARDING_STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'}),
        required=False
    )
    admission_year = forms.IntegerField(
        widget=forms.NumberInput(attrs={'class': 'form-input', 'min': 2000, 'max': date.today().year}),
        required=False
    )
