from django.db import models
from django.conf import settings
from django.core.validators import Min<PERSON><PERSON>ueValida<PERSON>, MaxValueValidator
from django.utils import timezone
from django.db.models import Count, Q, Avg
from datetime import date, timedelta
from apps.students.models import Student
from apps.teachers.models import Teacher

# Create your models here.

class AttendancePeriod(models.Model):
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

class StudentAttendance(models.Model):
    STATUS_CHOICES = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('excused', 'Excused'),
    ]
    id = models.AutoField(primary_key=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='attendances')
    date = models.DateField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    reason = models.CharField(max_length=255, null=True, blank=True)
    notified = models.BooleanField(default=False)
    recorded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    period = models.ForeignKey(AttendancePeriod, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        unique_together = ('student', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.student} - {self.date} - {self.status}"

    @classmethod
    def get_attendance_rate(cls, student, start_date=None, end_date=None):
        """Calculate attendance rate for a student in a given period"""
        queryset = cls.objects.filter(student=student)
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        total_days = queryset.count()
        if total_days == 0:
            return 0.0

        present_days = queryset.filter(status__in=['present', 'late']).count()
        return (present_days / total_days) * 100

    @classmethod
    def get_consecutive_absences(cls, student, as_of_date=None):
        """Get count of consecutive absences for a student"""
        if not as_of_date:
            as_of_date = date.today()

        consecutive_count = 0
        current_date = as_of_date

        while True:
            try:
                attendance = cls.objects.get(student=student, date=current_date)
                if attendance.status == 'absent':
                    consecutive_count += 1
                    current_date -= timedelta(days=1)
                else:
                    break
            except cls.DoesNotExist:
                break

        return consecutive_count

    @classmethod
    def get_monthly_statistics(cls, student, year, month):
        """Get monthly attendance statistics for a student"""
        from calendar import monthrange
        start_date = date(year, month, 1)
        end_date = date(year, month, monthrange(year, month)[1])

        attendances = cls.objects.filter(
            student=student,
            date__range=[start_date, end_date]
        )

        stats = {
            'total_days': attendances.count(),
            'present_days': attendances.filter(status='present').count(),
            'absent_days': attendances.filter(status='absent').count(),
            'late_days': attendances.filter(status='late').count(),
            'excused_days': attendances.filter(status='excused').count(),
        }

        if stats['total_days'] > 0:
            stats['attendance_rate'] = (stats['present_days'] + stats['late_days']) / stats['total_days'] * 100
        else:
            stats['attendance_rate'] = 0.0

        return stats

class TeacherAttendance(models.Model):
    STATUS_CHOICES = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('excused', 'Excused'),
    ]
    id = models.AutoField(primary_key=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='attendances')
    date = models.DateField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    reason = models.CharField(max_length=255, null=True, blank=True)
    notified = models.BooleanField(default=False)
    recorded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    period = models.ForeignKey(AttendancePeriod, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        unique_together = ('teacher', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.teacher} - {self.date} - {self.status}"

    @classmethod
    def get_attendance_rate(cls, teacher, start_date=None, end_date=None):
        """Calculate attendance rate for a teacher in a given period"""
        queryset = cls.objects.filter(teacher=teacher)
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        total_days = queryset.count()
        if total_days == 0:
            return 0.0

        present_days = queryset.filter(status__in=['present', 'late']).count()
        return (present_days / total_days) * 100

    @classmethod
    def get_monthly_statistics(cls, teacher, year, month):
        """Get monthly attendance statistics for a teacher"""
        from calendar import monthrange
        start_date = date(year, month, 1)
        end_date = date(year, month, monthrange(year, month)[1])

        attendances = cls.objects.filter(
            teacher=teacher,
            date__range=[start_date, end_date]
        )

        stats = {
            'total_days': attendances.count(),
            'present_days': attendances.filter(status='present').count(),
            'absent_days': attendances.filter(status='absent').count(),
            'late_days': attendances.filter(status='late').count(),
            'excused_days': attendances.filter(status='excused').count(),
        }

        if stats['total_days'] > 0:
            stats['attendance_rate'] = (stats['present_days'] + stats['late_days']) / stats['total_days'] * 100
        else:
            stats['attendance_rate'] = 0.0

        return stats


class AttendanceStatistics(models.Model):
    """
    Model to store calculated attendance statistics for performance optimization
    """
    ENTITY_TYPE_CHOICES = [
        ('student', 'Student'),
        ('teacher', 'Teacher'),
        ('class', 'Class'),
        ('grade', 'Grade'),
        ('school', 'School'),
    ]

    PERIOD_TYPE_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('term', 'Term'),
        ('annual', 'Annual'),
    ]

    id = models.AutoField(primary_key=True)
    entity_type = models.CharField(max_length=20, choices=ENTITY_TYPE_CHOICES)
    entity_id = models.PositiveIntegerField(null=True, blank=True)  # ID of student, teacher, class, etc.
    period_type = models.CharField(max_length=20, choices=PERIOD_TYPE_CHOICES)
    period_start = models.DateField()
    period_end = models.DateField()

    # Statistics
    total_days = models.PositiveIntegerField(default=0)
    present_days = models.PositiveIntegerField(default=0)
    absent_days = models.PositiveIntegerField(default=0)
    late_days = models.PositiveIntegerField(default=0)
    excused_days = models.PositiveIntegerField(default=0)

    # Calculated fields
    attendance_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)  # Percentage
    absence_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    punctuality_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)

    # Metadata
    calculated_at = models.DateTimeField(auto_now=True)
    academic_year = models.PositiveIntegerField(default=2024)
    term = models.PositiveIntegerField(default=1)

    class Meta:
        verbose_name = "Attendance Statistics"
        verbose_name_plural = "Attendance Statistics"
        unique_together = ['entity_type', 'entity_id', 'period_type', 'period_start', 'period_end']
        ordering = ['-period_start']

    def __str__(self):
        return f"{self.entity_type} {self.entity_id} - {self.period_type} ({self.period_start} to {self.period_end})"


class AttendanceAlert(models.Model):
    """
    Model for attendance-based alerts and notifications
    """
    ALERT_TYPE_CHOICES = [
        ('consecutive_absence', 'Consecutive Absence'),
        ('low_attendance', 'Low Attendance Rate'),
        ('frequent_lateness', 'Frequent Lateness'),
        ('pattern_change', 'Attendance Pattern Change'),
        ('risk_identification', 'At-Risk Student'),
    ]

    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]

    id = models.AutoField(primary_key=True)
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPE_CHOICES)
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    # Related entities
    student = models.ForeignKey(Student, on_delete=models.CASCADE, null=True, blank=True, related_name='attendance_alerts')
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, null=True, blank=True, related_name='attendance_alerts')

    # Alert details
    title = models.CharField(max_length=255)
    description = models.TextField()
    threshold_value = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    current_value = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    # Dates and tracking
    triggered_date = models.DateTimeField(auto_now_add=True)
    acknowledged_date = models.DateTimeField(null=True, blank=True)
    resolved_date = models.DateTimeField(null=True, blank=True)
    acknowledged_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_alerts')
    resolved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')

    # Actions taken
    action_taken = models.TextField(null=True, blank=True)
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name = "Attendance Alert"
        verbose_name_plural = "Attendance Alerts"
        ordering = ['-triggered_date']

    def __str__(self):
        entity = self.student or self.teacher
        return f"{self.alert_type} - {entity} ({self.severity})"


class BulkAttendanceSession(models.Model):
    """
    Model to track bulk attendance entry sessions
    """
    SESSION_TYPE_CHOICES = [
        ('class_marking', 'Class Marking'),
        ('csv_import', 'CSV Import'),
        ('api_import', 'API Import'),
        ('manual_bulk', 'Manual Bulk Entry'),
    ]

    STATUS_CHOICES = [
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    session_type = models.CharField(max_length=20, choices=SESSION_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='in_progress')

    # Session details
    date = models.DateField()
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, null=True, blank=True)
    section = models.CharField(max_length=10, null=True, blank=True)
    period = models.ForeignKey(AttendancePeriod, on_delete=models.SET_NULL, null=True, blank=True)

    # Processing details
    total_records = models.PositiveIntegerField(default=0)
    processed_records = models.PositiveIntegerField(default=0)
    successful_records = models.PositiveIntegerField(default=0)
    failed_records = models.PositiveIntegerField(default=0)

    # File information (for imports)
    uploaded_file = models.FileField(upload_to='attendance/bulk_imports/', null=True, blank=True)
    error_log = models.TextField(null=True, blank=True)

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Bulk Attendance Session"
        verbose_name_plural = "Bulk Attendance Sessions"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.session_type} - {self.date} ({self.status})"


class AttendanceReport(models.Model):
    """
    Model for generated attendance reports
    """
    REPORT_TYPE_CHOICES = [
        ('daily_summary', 'Daily Summary'),
        ('weekly_summary', 'Weekly Summary'),
        ('monthly_summary', 'Monthly Summary'),
        ('term_summary', 'Term Summary'),
        ('student_individual', 'Individual Student Report'),
        ('class_summary', 'Class Summary'),
        ('grade_summary', 'Grade Summary'),
        ('teacher_summary', 'Teacher Summary'),
        ('attendance_trends', 'Attendance Trends'),
        ('absence_analysis', 'Absence Analysis'),
    ]

    FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
        ('html', 'HTML'),
    ]

    STATUS_CHOICES = [
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    id = models.AutoField(primary_key=True)
    report_type = models.CharField(max_length=30, choices=REPORT_TYPE_CHOICES)
    format = models.CharField(max_length=10, choices=FORMAT_CHOICES, default='pdf')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='generating')

    # Report parameters
    start_date = models.DateField()
    end_date = models.DateField()
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE, null=True, blank=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE, null=True, blank=True)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, null=True, blank=True)

    # Report content
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    file_path = models.FileField(upload_to='attendance/reports/', null=True, blank=True)

    # Statistics included in report
    total_students = models.PositiveIntegerField(default=0)
    total_days = models.PositiveIntegerField(default=0)
    average_attendance_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)

    # Metadata
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    generated_at = models.DateTimeField(auto_now_add=True)
    accessed_count = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Attendance Report"
        verbose_name_plural = "Attendance Reports"
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.report_type} - {self.start_date} to {self.end_date}"
