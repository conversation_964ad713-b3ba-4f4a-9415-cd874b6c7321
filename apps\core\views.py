from django.shortcuts import render
from django.http import JsonResponse
from django.contrib import messages
from django.views.generic import TemplateView
from django.core.mail import send_mail
from django.conf import settings
from .models import SchoolInfo, ContactMessage, Testimonial, FAQ
from apps.students.models import Student
from apps.teachers.models import Teacher
from apps.academics.models import Subject
from apps.events.models import Event

class LandingPageView(TemplateView):
    template_name = 'core/home.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get school information
        try:
            context['school_info'] = SchoolInfo.objects.first()
        except SchoolInfo.DoesNotExist:
            context['school_info'] = None
        
        # Get basic statistics
        context['stats'] = {
            'total_students': Student.objects.count(),
            'total_teachers': Teacher.objects.count(),
            'total_subjects': Subject.objects.count(),
            'success_rate': 95,  # Can be calculated from exam results
        }
        
        # Get recent announcements/events
        context['recent_announcements'] = Event.objects.filter(
            category__name='Announcement'
        ).order_by('-start_date_time')[:3]
        
        # Get testimonials
        context['testimonials'] = Testimonial.objects.filter(
            is_active=True
        ).order_by('-created_at')[:6]
        
        # Get FAQs
        context['faqs'] = FAQ.objects.filter(
            is_active=True
        ).order_by('order', 'category')[:10]
        
        return context

class FAQView(TemplateView):
    template_name = 'core/faq.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['faqs'] = FAQ.objects.filter(is_active=True).order_by('order', 'category')
        return context

class TestimonialsView(TemplateView):
    template_name = 'core/testimonials.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['testimonials'] = Testimonial.objects.filter(is_active=True).order_by('-created_at')
        return context

class BlogView(TemplateView):
    template_name = 'core/blog.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This is a placeholder. We'll implement the actual blog functionality later
        context['under_construction'] = True
        return context

def contact_view(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        
        # Save contact message to database
        contact_message = ContactMessage.objects.create(
            name=name,
            email=email,
            subject=subject,
            message=message
        )
        
        # Send email notification
        try:
            school_info = SchoolInfo.objects.first()
            recipient_email = school_info.email if school_info else settings.DEFAULT_FROM_EMAIL
            
            email_subject = f"Contact Form: {subject}"
            email_message = f"""
            Name: {name}
            Email: {email}
            Subject: {subject}
            
            Message:
            {message}
            
            ---
            Message ID: {contact_message.id}
            Received: {contact_message.created_at}
            """
            
            send_mail(
                email_subject,
                email_message,
                settings.DEFAULT_FROM_EMAIL,
                [recipient_email],
                fail_silently=False,
            )
            
            messages.success(request, 'Your message has been sent successfully!')
            return JsonResponse({'status': 'success', 'message': 'Message sent successfully!'})
            
        except Exception as e:
            messages.error(request, 'Failed to send message. Please try again.')
            return JsonResponse({'status': 'error', 'message': 'Failed to send message.'})
    
    return JsonResponse({'status': 'error', 'message': 'Invalid request method.'})
