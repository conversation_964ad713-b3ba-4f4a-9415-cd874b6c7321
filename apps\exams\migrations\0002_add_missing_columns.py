# Generated manually to fix missing columns

from django.db import migrations


def add_missing_columns(apps, schema_editor):
    """Add missing columns to exams_exam table"""
    from django.db import connection
    
    cursor = connection.cursor()
    
    # Check and add exam_code column
    cursor.execute("PRAGMA table_info(exams_exam);")
    columns = [column[1] for column in cursor.fetchall()]
    
    if 'exam_code' not in columns:
        cursor.execute("ALTER TABLE exams_exam ADD COLUMN exam_code VARCHAR(50) DEFAULT '';")
        print("Added exam_code column to exams_exam table")
    
    # Add other potentially missing columns
    missing_columns = [
        ('exam_type_id', 'INTEGER NULL'),  # Foreign key to ExamType
        ('term', 'INTEGER DEFAULT 1'),
        ('description', 'TEXT DEFAULT ""'),
        ('instructions', 'TEXT DEFAULT ""'),
        ('total_marks', 'INTEGER DEFAULT 100'),
        ('pass_marks', 'INTEGER DEFAULT 40'),
        ('duration_minutes', 'INTEGER DEFAULT 120'),
        ('marking_scheme', 'VARCHAR(20) DEFAULT "percentage"'),
        ('registration_start', 'DATE NULL'),
        ('registration_end', 'DATE NULL'),
        ('results_release_date', 'DATE NULL'),
        ('status', 'VARCHAR(20) DEFAULT "draft"'),
        ('is_published', 'BOOLEAN DEFAULT 0'),
        ('results_published', 'BOOLEAN DEFAULT 0'),
        ('allow_calculator', 'BOOLEAN DEFAULT 0'),
        ('allow_reference_materials', 'BOOLEAN DEFAULT 0'),
        ('requires_signature', 'BOOLEAN DEFAULT 1'),
        ('created_by_id', 'INTEGER NULL'),
        ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
        ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
    ]
    
    for column_name, column_def in missing_columns:
        if column_name not in columns:
            try:
                cursor.execute(f"ALTER TABLE exams_exam ADD COLUMN {column_name} {column_def};")
                print(f"Added {column_name} column to exams_exam table")
            except Exception as e:
                print(f"Failed to add {column_name}: {e}")
    
    # Update exam_code for existing records
    cursor.execute("UPDATE exams_exam SET exam_code = 'EX' || id WHERE exam_code = '' OR exam_code IS NULL;")
    print("Updated exam_code for existing records")


def reverse_missing_columns(apps, schema_editor):
    """Reverse operation - no-op since we don't want to remove columns"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("exams", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(add_missing_columns, reverse_missing_columns),
    ]
