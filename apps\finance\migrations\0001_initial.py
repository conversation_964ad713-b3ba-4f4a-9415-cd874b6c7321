# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("academics", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="StudentPayment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("year", models.PositiveIntegerField()),
                ("term", models.IntegerField(default=1)),
                ("month", models.CharField(max_length=255)),
                ("date", models.DateField()),
                ("paid", models.DecimalField(decimal_places=2, max_digits=11)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Cash", "Cash"),
                            ("M-Pesa", "M-Pesa"),
                            ("Bank", "Bank"),
                        ],
                        default="Cash",
                        max_length=50,
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "receipt_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("received_by", models.BigIntegerField(blank=True, null=True)),
                ("_status", models.CharField(max_length=255)),
                ("student_status", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="StudentPaymentHistory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("grade_id", models.IntegerField()),
                ("subject_id", models.IntegerField()),
                ("teacher_id", models.IntegerField()),
                ("subject_fee", models.DecimalField(decimal_places=2, max_digits=11)),
                ("subtotal", models.DecimalField(decimal_places=2, max_digits=11)),
                ("_status", models.CharField(max_length=255)),
                ("month", models.CharField(max_length=255)),
                ("year", models.PositiveIntegerField()),
                ("date", models.DateField()),
                ("invoice_number", models.IntegerField()),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Cash", "Cash"),
                            ("M-Pesa", "M-Pesa"),
                            ("Bank", "Bank"),
                        ],
                        default="Cash",
                        max_length=50,
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FeeStructure",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("fee_category", models.CharField(max_length=100)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=11)),
                ("term", models.IntegerField(default=0)),
                ("year", models.PositiveIntegerField()),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_mandatory", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Order",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="orders",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentFeeBalance",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("index_number", models.BigIntegerField()),
                ("year", models.PositiveIntegerField()),
                ("term", models.IntegerField()),
                ("total_amount", models.DecimalField(decimal_places=2, max_digits=11)),
                (
                    "amount_paid",
                    models.DecimalField(decimal_places=2, default=0, max_digits=11),
                ),
                ("balance", models.DecimalField(decimal_places=2, max_digits=11)),
                ("last_payment_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("unpaid", "Unpaid"),
                            ("partially_paid", "Partially Paid"),
                            ("paid", "Paid"),
                        ],
                        default="unpaid",
                        max_length=50,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
            ],
        ),
    ]
