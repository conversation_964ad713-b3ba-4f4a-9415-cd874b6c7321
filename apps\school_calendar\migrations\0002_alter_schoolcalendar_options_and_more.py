# Generated by Django 5.2.1 on 2025-06-09 10:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0002_initial"),
        ("school_calendar", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="schoolcalendar",
            options={
                "ordering": ["start_date", "start_time", "title"],
                "verbose_name": "School Calendar Event",
                "verbose_name_plural": "School Calendar Events",
            },
        ),
        migrations.RemoveField(
            model_name="schoolcalendar",
            name="type",
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="affects_attendance",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="end_time",
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="event_type",
            field=models.CharField(
                choices=[
                    ("academic", "Academic Event"),
                    ("holiday", "Holiday"),
                    ("exam", "Exam"),
                    ("event", "School Event"),
                    ("meeting", "Meeting"),
                    ("deadline", "Deadline"),
                    ("reminder", "Reminder"),
                    ("maintenance", "Maintenance"),
                ],
                default="event",
                max_length=50,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="grades",
            field=models.ManyToManyField(
                blank=True, related_name="calendar_events", to="academics.grade"
            ),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="icon",
            field=models.CharField(
                blank=True, help_text="Icon class name", max_length=50
            ),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="is_all_day",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="is_recurring",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="location",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="preparation_days",
            field=models.IntegerField(
                default=0, help_text="Days needed for preparation"
            ),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="priority",
            field=models.CharField(
                choices=[
                    ("low", "Low"),
                    ("medium", "Medium"),
                    ("high", "High"),
                    ("urgent", "Urgent"),
                ],
                default="medium",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="recurrence_rule",
            field=models.TextField(blank=True, help_text="RRULE for recurring events"),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="requires_preparation",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="start_time",
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="subjects",
            field=models.ManyToManyField(
                blank=True, related_name="calendar_events", to="academics.subject"
            ),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="visibility",
            field=models.CharField(
                choices=[
                    ("public", "Public"),
                    ("students", "Students Only"),
                    ("teachers", "Teachers Only"),
                    ("staff", "Staff Only"),
                    ("admin", "Admin Only"),
                    ("parents", "Parents Only"),
                    ("custom", "Custom Groups"),
                ],
                default="public",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="schoolcalendar",
            name="color",
            field=models.CharField(
                default="#3B82F6", help_text="Hex color code", max_length=7
            ),
        ),
        migrations.AlterField(
            model_name="schoolcalendar",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="schoolcalendar",
            name="year",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="AcademicYearConfiguration",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "year_name",
                    models.CharField(
                        help_text="e.g., 2024-2025", max_length=50, unique=True
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("upcoming", "Upcoming"),
                            ("current", "Current"),
                            ("completed", "Completed"),
                            ("archived", "Archived"),
                        ],
                        default="upcoming",
                        max_length=20,
                    ),
                ),
                ("total_weeks", models.IntegerField(help_text="Total academic weeks")),
                ("total_days", models.IntegerField(help_text="Total academic days")),
                (
                    "description",
                    models.TextField(blank=True, help_text="Academic year description"),
                ),
                (
                    "is_current",
                    models.BooleanField(
                        default=False, help_text="Mark as current academic year"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Academic Year Configuration",
                "verbose_name_plural": "Academic Year Configurations",
                "ordering": ["-start_date"],
            },
        ),
        migrations.AddField(
            model_name="schoolcalendar",
            name="academic_year",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="school_calendar.academicyearconfiguration",
            ),
        ),
        migrations.CreateModel(
            name="CalendarExportImport",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "operation_type",
                    models.CharField(
                        choices=[("export", "Export"), ("import", "Import")],
                        max_length=10,
                    ),
                ),
                (
                    "format_type",
                    models.CharField(
                        choices=[
                            ("ical", "iCal (.ics)"),
                            ("csv", "CSV (.csv)"),
                            ("excel", "Excel (.xlsx)"),
                            ("pdf", "PDF (.pdf)"),
                            ("json", "JSON (.json)"),
                        ],
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "event_types",
                    models.JSONField(
                        default=list, help_text="List of event types to include"
                    ),
                ),
                ("include_holidays", models.BooleanField(default=True)),
                ("include_exams", models.BooleanField(default=True)),
                ("include_events", models.BooleanField(default=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "file_path",
                    models.FileField(
                        blank=True, null=True, upload_to="calendar_exports/"
                    ),
                ),
                (
                    "file_size",
                    models.BigIntegerField(
                        blank=True, help_text="File size in bytes", null=True
                    ),
                ),
                (
                    "records_count",
                    models.IntegerField(
                        default=0, help_text="Number of records processed"
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
                ("processing_time", models.DurationField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, help_text="When the export file expires", null=True
                    ),
                ),
                (
                    "grades",
                    models.ManyToManyField(
                        blank=True,
                        related_name="calendar_exports",
                        to="academics.grade",
                    ),
                ),
                (
                    "subjects",
                    models.ManyToManyField(
                        blank=True,
                        related_name="calendar_exports",
                        to="academics.subject",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_operations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Export/Import",
                "verbose_name_plural": "Calendar Exports/Imports",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HolidaySchedule",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "holiday_type",
                    models.CharField(
                        choices=[
                            ("national", "National Holiday"),
                            ("religious", "Religious Holiday"),
                            ("school", "School Holiday"),
                            ("break", "School Break"),
                            ("weekend", "Weekend"),
                            ("custom", "Custom Holiday"),
                        ],
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("is_recurring", models.BooleanField(default=False)),
                (
                    "recurrence_type",
                    models.CharField(
                        choices=[
                            ("none", "No Recurrence"),
                            ("yearly", "Yearly"),
                            ("monthly", "Monthly"),
                            ("weekly", "Weekly"),
                        ],
                        default="none",
                        max_length=20,
                    ),
                ),
                (
                    "affects_attendance",
                    models.BooleanField(
                        default=True,
                        help_text="Does this holiday affect attendance tracking?",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "color_code",
                    models.CharField(
                        default="#FF6B6B",
                        help_text="Hex color for calendar display",
                        max_length=7,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="holidays",
                        to="school_calendar.academicyearconfiguration",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Holiday Schedule",
                "verbose_name_plural": "Holiday Schedules",
                "ordering": ["start_date", "name"],
            },
        ),
        migrations.CreateModel(
            name="TermConfiguration",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "term_name",
                    models.CharField(
                        choices=[
                            ("term_1", "Term 1"),
                            ("term_2", "Term 2"),
                            ("term_3", "Term 3"),
                            ("semester_1", "Semester 1"),
                            ("semester_2", "Semester 2"),
                            ("quarter_1", "Quarter 1"),
                            ("quarter_2", "Quarter 2"),
                            ("quarter_3", "Quarter 3"),
                            ("quarter_4", "Quarter 4"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "display_name",
                    models.CharField(help_text="Custom display name", max_length=100),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("upcoming", "Upcoming"),
                            ("current", "Current"),
                            ("completed", "Completed"),
                            ("break", "Break Period"),
                        ],
                        default="upcoming",
                        max_length=20,
                    ),
                ),
                ("total_weeks", models.IntegerField(help_text="Total term weeks")),
                ("total_days", models.IntegerField(help_text="Total term days")),
                (
                    "break_start",
                    models.DateField(
                        blank=True, help_text="Term break start date", null=True
                    ),
                ),
                (
                    "break_end",
                    models.DateField(
                        blank=True, help_text="Term break end date", null=True
                    ),
                ),
                (
                    "is_current",
                    models.BooleanField(
                        default=False, help_text="Mark as current term"
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="terms",
                        to="school_calendar.academicyearconfiguration",
                    ),
                ),
            ],
            options={
                "verbose_name": "Term Configuration",
                "verbose_name_plural": "Term Configurations",
                "ordering": ["academic_year", "start_date"],
                "unique_together": {("academic_year", "term_name")},
            },
        ),
        migrations.CreateModel(
            name="ExamPeriodSchedule",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("exam_name", models.CharField(max_length=200)),
                (
                    "exam_type",
                    models.CharField(
                        choices=[
                            ("continuous_assessment", "Continuous Assessment"),
                            ("mid_term", "Mid-Term Exam"),
                            ("end_term", "End of Term Exam"),
                            ("final", "Final Exam"),
                            ("mock", "Mock Exam"),
                            ("national", "National Exam"),
                            ("entrance", "Entrance Exam"),
                            ("placement", "Placement Test"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("registration_start", models.DateField(blank=True, null=True)),
                ("registration_end", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("ongoing", "Ongoing"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("postponed", "Postponed"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                (
                    "instructions",
                    models.TextField(
                        blank=True,
                        help_text="Special instructions for this exam period",
                    ),
                ),
                (
                    "color_code",
                    models.CharField(
                        default="#4ECDC4",
                        help_text="Hex color for calendar display",
                        max_length=7,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_periods",
                        to="school_calendar.academicyearconfiguration",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grades",
                    models.ManyToManyField(
                        related_name="exam_periods", to="academics.grade"
                    ),
                ),
                (
                    "subjects",
                    models.ManyToManyField(
                        blank=True, related_name="exam_periods", to="academics.subject"
                    ),
                ),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_periods",
                        to="school_calendar.termconfiguration",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Period Schedule",
                "verbose_name_plural": "Exam Period Schedules",
                "ordering": ["start_date", "exam_name"],
            },
        ),
        migrations.AlterField(
            model_name="schoolcalendar",
            name="term",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="school_calendar.termconfiguration",
            ),
        ),
        migrations.CreateModel(
            name="UserCalendarPreferences",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "default_view",
                    models.CharField(
                        choices=[
                            ("month", "Month View"),
                            ("week", "Week View"),
                            ("day", "Day View"),
                            ("agenda", "Agenda View"),
                            ("year", "Year View"),
                        ],
                        default="month",
                        max_length=20,
                    ),
                ),
                (
                    "time_format",
                    models.CharField(
                        choices=[("12", "12-hour (AM/PM)"), ("24", "24-hour")],
                        default="12",
                        max_length=2,
                    ),
                ),
                (
                    "week_starts_on",
                    models.CharField(
                        choices=[("sunday", "Sunday"), ("monday", "Monday")],
                        default="monday",
                        max_length=10,
                    ),
                ),
                ("show_weekends", models.BooleanField(default=True)),
                ("show_holidays", models.BooleanField(default=True)),
                ("show_exams", models.BooleanField(default=True)),
                ("show_events", models.BooleanField(default=True)),
                ("email_notifications", models.BooleanField(default=True)),
                ("sms_notifications", models.BooleanField(default=False)),
                ("push_notifications", models.BooleanField(default=True)),
                (
                    "default_reminder_time",
                    models.CharField(
                        choices=[
                            ("15_minutes", "15 Minutes Before"),
                            ("30_minutes", "30 Minutes Before"),
                            ("1_hour", "1 Hour Before"),
                            ("2_hours", "2 Hours Before"),
                            ("1_day", "1 Day Before"),
                            ("2_days", "2 Days Before"),
                            ("1_week", "1 Week Before"),
                            ("2_weeks", "2 Weeks Before"),
                        ],
                        default="1_day",
                        max_length=20,
                    ),
                ),
                (
                    "visible_event_types",
                    models.JSONField(
                        default=list,
                        help_text="List of event types visible to this user",
                    ),
                ),
                ("theme_color", models.CharField(default="#3B82F6", max_length=7)),
                ("compact_view", models.BooleanField(default=False)),
                ("show_event_details", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "visible_grades",
                    models.ManyToManyField(
                        blank=True,
                        related_name="calendar_viewers",
                        to="academics.grade",
                    ),
                ),
                (
                    "visible_subjects",
                    models.ManyToManyField(
                        blank=True,
                        related_name="calendar_viewers",
                        to="academics.subject",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Calendar Preferences",
                "verbose_name_plural": "User Calendar Preferences",
            },
        ),
        migrations.CreateModel(
            name="CalendarNotification",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("push", "Push Notification"),
                            ("in_app", "In-App Notification"),
                            ("dashboard", "Dashboard Alert"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "reminder_interval",
                    models.CharField(
                        choices=[
                            ("15_minutes", "15 Minutes Before"),
                            ("30_minutes", "30 Minutes Before"),
                            ("1_hour", "1 Hour Before"),
                            ("2_hours", "2 Hours Before"),
                            ("1_day", "1 Day Before"),
                            ("2_days", "2 Days Before"),
                            ("1_week", "1 Week Before"),
                            ("2_weeks", "2 Weeks Before"),
                        ],
                        max_length=20,
                    ),
                ),
                ("scheduled_time", models.DateTimeField()),
                ("sent_time", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("message_title", models.CharField(max_length=200)),
                ("message_body", models.TextField()),
                ("error_message", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "calendar_event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="school_calendar.schoolcalendar",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Notification",
                "verbose_name_plural": "Calendar Notifications",
                "ordering": ["scheduled_time"],
                "unique_together": {
                    ("calendar_event", "recipient", "reminder_interval")
                },
            },
        ),
        migrations.CreateModel(
            name="CalendarSyncConfiguration",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("google", "Google Calendar"),
                            ("outlook", "Microsoft Outlook"),
                            ("apple", "Apple iCloud"),
                            ("ical", "iCal/CalDAV"),
                            ("exchange", "Microsoft Exchange"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "sync_direction",
                    models.CharField(
                        choices=[
                            ("import", "Import Only"),
                            ("export", "Export Only"),
                            ("bidirectional", "Bidirectional"),
                        ],
                        default="export",
                        max_length=20,
                    ),
                ),
                ("calendar_name", models.CharField(max_length=200)),
                ("external_calendar_id", models.CharField(max_length=500)),
                ("access_token", models.TextField(blank=True)),
                ("refresh_token", models.TextField(blank=True)),
                (
                    "sync_frequency",
                    models.IntegerField(
                        default=60, help_text="Sync frequency in minutes"
                    ),
                ),
                ("last_sync", models.DateTimeField(blank=True, null=True)),
                ("next_sync", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("paused", "Paused"),
                            ("error", "Error"),
                            ("disabled", "Disabled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
                (
                    "sync_filters",
                    models.JSONField(
                        blank=True, default=dict, help_text="Filters for what to sync"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_syncs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Calendar Sync Configuration",
                "verbose_name_plural": "Calendar Sync Configurations",
                "unique_together": {("user", "provider", "external_calendar_id")},
            },
        ),
    ]
