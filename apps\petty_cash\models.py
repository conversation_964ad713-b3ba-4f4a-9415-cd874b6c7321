from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta
import uuid

# Enhanced Petty Cash Management System

class PettyCash(models.Model):
    id = models.AutoField(primary_key=True)
    received_by = models.BigIntegerField()
    approved_by = models.BigIntegerField()
    year = models.PositiveIntegerField()
    month = models.CharField(max_length=255)
    date = models.DateField()
    time = models.TimeField()
    paid = models.DecimalField(max_digits=11, decimal_places=2)
    received_type = models.CharField(max_length=255)
    purpose = models.TextField(null=True, blank=True)
    receipt_number = models.CharField(max_length=50, null=True, blank=True)
    _status = models.CharField(max_length=255)

    def __str__(self):
        return f"Petty Cash - {self.id}"

class PettyCashHistory(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('Bank', 'Bank'),
        ('Cash', 'Cash'),
        ('M-Pesa', 'M-Pesa'),
    ]
    id = models.AutoField(primary_key=True)
    _desc = models.CharField(max_length=255)
    received_by = models.BigIntegerField()
    approved_by = models.BigIntegerField()
    year = models.PositiveIntegerField()
    month = models.CharField(max_length=255)
    date = models.DateField()
    time = models.TimeField()
    amount = models.DecimalField(max_digits=11, decimal_places=2)
    total_paid = models.DecimalField(max_digits=11, decimal_places=2)
    invoice_number = models.IntegerField()
    received_type = models.CharField(max_length=255)
    payment_method = models.CharField(max_length=50, null=True, blank=True, choices=PAYMENT_METHOD_CHOICES)
    _status = models.CharField(max_length=255)

    def __str__(self):
        return f"Petty Cash History - {self.id}"

# Enhanced Petty Cash Management Models

class PettyCashCategory(models.Model):
    """Categories for petty cash expenses"""
    CATEGORY_TYPES = [
        ('office_supplies', 'Office Supplies'),
        ('maintenance', 'Maintenance & Repairs'),
        ('utilities', 'Utilities'),
        ('transport', 'Transport & Travel'),
        ('communication', 'Communication'),
        ('refreshments', 'Refreshments'),
        ('emergency', 'Emergency Expenses'),
        ('miscellaneous', 'Miscellaneous'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, unique=True)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField(blank=True)
    budget_limit = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    requires_approval = models.BooleanField(default=False)
    approval_threshold = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Petty Cash Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

class PettyCashFund(models.Model):
    """Petty cash fund management"""
    FUND_STATUSES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('closed', 'Closed'),
    ]

    id = models.AutoField(primary_key=True)
    fund_name = models.CharField(max_length=200)
    custodian = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='managed_funds')
    initial_amount = models.DecimalField(max_digits=12, decimal_places=2)
    current_balance = models.DecimalField(max_digits=12, decimal_places=2)
    minimum_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    maximum_limit = models.DecimalField(max_digits=12, decimal_places=2)

    # Fund details
    establishment_date = models.DateField()
    last_replenishment_date = models.DateField(null=True, blank=True)
    last_reconciliation_date = models.DateField(null=True, blank=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=FUND_STATUSES, default='active')
    total_disbursed = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_replenished = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Metadata
    description = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def update_balance(self):
        """Update current balance based on transactions"""
        from django.db.models import Sum

        total_expenses = self.expenses.filter(
            status='approved'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        total_replenishments = self.replenishments.filter(
            status='approved'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        self.current_balance = self.initial_amount + total_replenishments - total_expenses
        self.total_disbursed = total_expenses
        self.total_replenished = total_replenishments
        self.save()

    @property
    def is_low_balance(self):
        """Check if fund balance is below minimum"""
        return self.current_balance <= self.minimum_balance

    def __str__(self):
        return f"{self.fund_name} - {self.custodian.get_full_name()}"

class EnhancedPettyCashRequest(models.Model):
    """Enhanced petty cash request system"""
    REQUEST_TYPES = [
        ('expense', 'Expense Request'),
        ('advance', 'Cash Advance'),
        ('reimbursement', 'Reimbursement'),
        ('replenishment', 'Fund Replenishment'),
    ]

    REQUEST_STATUSES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('disbursed', 'Disbursed'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    id = models.AutoField(primary_key=True)
    request_number = models.CharField(max_length=100, unique=True)
    fund = models.ForeignKey(PettyCashFund, on_delete=models.CASCADE, related_name='requests')
    category = models.ForeignKey(PettyCashCategory, on_delete=models.CASCADE)

    # Request details
    request_type = models.CharField(max_length=20, choices=REQUEST_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    amount_requested = models.DecimalField(max_digits=10, decimal_places=2)
    amount_approved = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Dates
    requested_date = models.DateField(auto_now_add=True)
    required_date = models.DateField()
    approved_date = models.DateField(null=True, blank=True)
    disbursed_date = models.DateField(null=True, blank=True)

    # People involved
    requested_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='petty_cash_requests')
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_petty_cash_requests')
    disbursed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='disbursed_petty_cash_requests')

    # Status and tracking
    status = models.CharField(max_length=20, choices=REQUEST_STATUSES, default='draft')
    rejection_reason = models.TextField(blank=True)
    approval_notes = models.TextField(blank=True)

    # Supporting documents
    supporting_documents = models.FileField(upload_to='petty_cash/documents/', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.request_number:
            self.request_number = self.generate_request_number()
        super().save(*args, **kwargs)

    def generate_request_number(self):
        """Generate unique request number"""
        year = date.today().year
        count = EnhancedPettyCashRequest.objects.filter(created_at__year=year).count() + 1
        return f"PCR-{year}-{count:06d}"

    def __str__(self):
        return f"{self.request_number} - {self.title}"

class PettyCashExpense(models.Model):
    """Actual petty cash expenses"""
    EXPENSE_STATUSES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('reimbursed', 'Reimbursed'),
    ]

    id = models.AutoField(primary_key=True)
    expense_number = models.CharField(max_length=100, unique=True)
    request = models.ForeignKey(EnhancedPettyCashRequest, on_delete=models.CASCADE, related_name='expenses', null=True, blank=True)
    fund = models.ForeignKey(PettyCashFund, on_delete=models.CASCADE, related_name='expenses')
    category = models.ForeignKey(PettyCashCategory, on_delete=models.CASCADE)

    # Expense details
    description = models.TextField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    expense_date = models.DateField()
    vendor_name = models.CharField(max_length=200, blank=True)
    receipt_number = models.CharField(max_length=100, blank=True)

    # People involved
    incurred_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='incurred_expenses')
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_expenses')

    # Status and tracking
    status = models.CharField(max_length=20, choices=EXPENSE_STATUSES, default='pending')
    approved_date = models.DateField(null=True, blank=True)

    # Supporting documents
    receipt_image = models.ImageField(upload_to='petty_cash/receipts/', blank=True)
    supporting_documents = models.FileField(upload_to='petty_cash/expense_docs/', blank=True)

    # Metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-expense_date']

    def save(self, *args, **kwargs):
        if not self.expense_number:
            self.expense_number = self.generate_expense_number()
        super().save(*args, **kwargs)

    def generate_expense_number(self):
        """Generate unique expense number"""
        year = date.today().year
        count = PettyCashExpense.objects.filter(created_at__year=year).count() + 1
        return f"PCE-{year}-{count:06d}"

    def __str__(self):
        return f"{self.expense_number} - {self.description[:50]}"

class PettyCashReplenishment(models.Model):
    """Fund replenishment records"""
    REPLENISHMENT_STATUSES = [
        ('requested', 'Requested'),
        ('approved', 'Approved'),
        ('disbursed', 'Disbursed'),
        ('rejected', 'Rejected'),
    ]

    id = models.AutoField(primary_key=True)
    replenishment_number = models.CharField(max_length=100, unique=True)
    fund = models.ForeignKey(PettyCashFund, on_delete=models.CASCADE, related_name='replenishments')

    # Replenishment details
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    reason = models.TextField()
    requested_date = models.DateField(auto_now_add=True)
    approved_date = models.DateField(null=True, blank=True)
    disbursed_date = models.DateField(null=True, blank=True)

    # People involved
    requested_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='requested_replenishments')
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_replenishments')
    disbursed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='disbursed_replenishments')

    # Status and tracking
    status = models.CharField(max_length=20, choices=REPLENISHMENT_STATUSES, default='requested')

    # Supporting documents
    supporting_documents = models.FileField(upload_to='petty_cash/replenishment_docs/', blank=True)

    # Metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.replenishment_number:
            self.replenishment_number = self.generate_replenishment_number()
        super().save(*args, **kwargs)

    def generate_replenishment_number(self):
        """Generate unique replenishment number"""
        year = date.today().year
        count = PettyCashReplenishment.objects.filter(created_at__year=year).count() + 1
        return f"PCR-{year}-{count:06d}"

    def __str__(self):
        return f"{self.replenishment_number} - {self.amount}"

class PettyCashReconciliation(models.Model):
    """Petty cash reconciliation records"""
    RECONCILIATION_STATUSES = [
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('discrepancy', 'Discrepancy Found'),
        ('resolved', 'Resolved'),
    ]

    id = models.AutoField(primary_key=True)
    reconciliation_number = models.CharField(max_length=100, unique=True)
    fund = models.ForeignKey(PettyCashFund, on_delete=models.CASCADE, related_name='reconciliations')

    # Reconciliation period
    start_date = models.DateField()
    end_date = models.DateField()
    reconciliation_date = models.DateField(auto_now_add=True)

    # Balances
    opening_balance = models.DecimalField(max_digits=12, decimal_places=2)
    closing_balance = models.DecimalField(max_digits=12, decimal_places=2)
    calculated_balance = models.DecimalField(max_digits=12, decimal_places=2)
    physical_cash_count = models.DecimalField(max_digits=12, decimal_places=2)
    variance = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Transaction summaries
    total_expenses = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_replenishments = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    number_of_transactions = models.IntegerField(default=0)

    # People involved
    reconciled_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reconciliations')
    reviewed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_reconciliations')

    # Status and tracking
    status = models.CharField(max_length=20, choices=RECONCILIATION_STATUSES, default='in_progress')
    discrepancy_explanation = models.TextField(blank=True)
    resolution_notes = models.TextField(blank=True)

    # Supporting documents
    reconciliation_report = models.FileField(upload_to='petty_cash/reconciliation_reports/', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.reconciliation_number:
            self.reconciliation_number = self.generate_reconciliation_number()

        # Calculate variance
        self.variance = self.physical_cash_count - self.calculated_balance

        # Update status based on variance
        if abs(self.variance) > Decimal('0.01'):  # Allow for minor rounding differences
            if self.status == 'in_progress':
                self.status = 'discrepancy'
        elif self.status == 'discrepancy' and not self.resolution_notes:
            self.status = 'completed'

        super().save(*args, **kwargs)

    def generate_reconciliation_number(self):
        """Generate unique reconciliation number"""
        year = date.today().year
        count = PettyCashReconciliation.objects.filter(created_at__year=year).count() + 1
        return f"REC-{year}-{count:06d}"

    def __str__(self):
        return f"{self.reconciliation_number} - {self.fund.fund_name}"
