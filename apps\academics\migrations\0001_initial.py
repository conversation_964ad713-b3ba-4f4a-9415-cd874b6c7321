# Generated by Django 5.2.1 on 2025-05-21 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ClassRoom",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("student_count", models.IntegerField(default=0)),
                ("capacity", models.IntegerField(default=40)),
                ("section", models.CharField(blank=True, max_length=50, null=True)),
                ("building", models.CharField(blank=True, max_length=100, null=True)),
                ("floor", models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ("room_number", models.CharField(blank=True, max_length=20, null=True)),
                ("has_projector", models.BooleanField(default=False)),
                ("has_ac", models.Boolean<PERSON>ield(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Department",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Grade",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("admission_fee", models.DecimalField(decimal_places=2, max_digits=11)),
                ("hall_charge", models.IntegerField()),
                (
                    "section",
                    models.CharField(
                        blank=True,
                        choices=[("A", "A"), ("B", "B"), ("C", "C")],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("minimum_subjects", models.IntegerField(default=8)),
                (
                    "level",
                    models.CharField(
                        blank=True,
                        choices=[("Primary", "Primary"), ("Secondary", "Secondary")],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "boarding_fee",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                ("is_boarding", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Subject",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("code", models.CharField(blank=True, max_length=20, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("is_optional", models.BooleanField(default=False)),
                ("lessons_per_week", models.IntegerField(default=5)),
            ],
        ),
        migrations.CreateModel(
            name="SubjectRouting",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("fee", models.DecimalField(decimal_places=2, max_digits=11)),
                ("is_mandatory", models.BooleanField(default=True)),
            ],
        ),
    ]
