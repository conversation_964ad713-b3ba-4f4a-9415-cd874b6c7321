from django.urls import path
from django.shortcuts import redirect
from . import views

app_name = 'teachers'

def redirect_to_register(request):
    """Redirect create/ to register/ for better UX"""
    return redirect('teachers:register')

urlpatterns = [
    # Dashboard and main views
    path('', views.teacher_dashboard, name='dashboard'),
    path('list/', views.teacher_list, name='list'),
    path('register/', views.teacher_register, name='register'),
    path('create/', redirect_to_register, name='create'),  # Redirect create to register
    
    # Teacher detail and management
    path('<int:pk>/', views.teacher_detail, name='detail'),
    path('<int:pk>/edit/', views.teacher_edit, name='edit'),
    
    # Teacher qualifications
    path('<int:pk>/qualifications/', views.teacher_qualifications, name='qualifications'),
    
    # Teacher documents
    path('<int:pk>/documents/', views.teacher_documents, name='documents'),
    
    # Teacher subject specializations
    path('<int:pk>/specializations/', views.teacher_specializations, name='specializations'),
    
    # Teacher assignments
    path('<int:pk>/assignments/', views.teacher_assignments, name='assignments'),
    path('bulk-assignment/', views.bulk_teacher_assignment, name='bulk_assignment'),
    
    # Teacher status and workload
    path('<int:pk>/status-change/', views.teacher_status_change, name='status_change'),
    path('workload-report/', views.teacher_workload_report, name='workload_report'),
    
    # Teacher deletion
    path('<int:pk>/delete/', views.teacher_delete, name='delete'),

    # Exam-related URLs
    path('exams/', views.teacher_exam_dashboard, name='exam_dashboard'),
    path('exams/assignments/', views.teacher_exam_assignments, name='exam_assignments'),
    path('exams/results/', views.teacher_result_entry, name='result_entry'),
    path('exams/results/bulk-upload/', views.bulk_result_upload, name='bulk_result_upload'),
    path('exams/results/template-download/', views.download_result_template, name='download_result_template'),

    # Student Management URLs
    path('students/', views.teacher_student_management, name='student_management'),
    path('students/<int:student_id>/', views.teacher_student_detail, name='student_detail'),
    path('class-progress/<int:grade_id>/', views.teacher_class_progress, name='class_progress'),
]
