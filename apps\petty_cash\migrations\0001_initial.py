# Generated by Django 5.2.1 on 2025-05-21 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PettyCash",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("received_by", models.BigIntegerField()),
                ("approved_by", models.BigIntegerField()),
                ("year", models.PositiveIntegerField()),
                ("month", models.CharField(max_length=255)),
                ("date", models.DateField()),
                ("time", models.TimeField()),
                ("paid", models.DecimalField(decimal_places=2, max_digits=11)),
                ("received_type", models.CharField(max_length=255)),
                ("purpose", models.TextField(blank=True, null=True)),
                (
                    "receipt_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("_status", models.Char<PERSON>ield(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="PettyCashHistory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("_desc", models.CharField(max_length=255)),
                ("received_by", models.BigIntegerField()),
                ("approved_by", models.BigIntegerField()),
                ("year", models.PositiveIntegerField()),
                ("month", models.CharField(max_length=255)),
                ("date", models.DateField()),
                ("time", models.TimeField()),
                ("amount", models.DecimalField(decimal_places=2, max_digits=11)),
                ("total_paid", models.DecimalField(decimal_places=2, max_digits=11)),
                ("invoice_number", models.IntegerField()),
                ("received_type", models.CharField(max_length=255)),
                (
                    "payment_method",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Bank", "Bank"),
                            ("Cash", "Cash"),
                            ("M-Pesa", "M-Pesa"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("_status", models.CharField(max_length=255)),
            ],
        ),
    ]
