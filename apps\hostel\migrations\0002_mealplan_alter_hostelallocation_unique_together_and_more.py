# Generated by Django 5.2.1 on 2025-06-09 08:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hostel", "0001_initial"),
        ("students", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="MealPlan",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100)),
                (
                    "meal_type",
                    models.CharField(
                        choices=[
                            ("breakfast", "Breakfast Only"),
                            ("lunch", "Lunch Only"),
                            ("dinner", "Dinner Only"),
                            ("breakfast_lunch", "Breakfast & Lunch"),
                            ("lunch_dinner", "Lunch & Dinner"),
                            ("full_board", "Full Board (All Meals)"),
                            ("custom", "Custom Plan"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("monthly_fee", models.DecimalField(decimal_places=2, max_digits=10)),
                ("includes_breakfast", models.BooleanField(default=False)),
                ("includes_lunch", models.BooleanField(default=False)),
                ("includes_dinner", models.BooleanField(default=False)),
                ("includes_snacks", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterUniqueTogether(
            name="hostelallocation",
            unique_together={("student", "status")},
        ),
        migrations.CreateModel(
            name="BoardingFeeDiscount",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "discount_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage"),
                            ("fixed_amount", "Fixed Amount"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "discount_value",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("scholarship", "Scholarship"),
                            ("financial_hardship", "Financial Hardship"),
                            ("sibling_discount", "Sibling Discount"),
                            ("staff_child", "Staff Child"),
                            ("academic_excellence", "Academic Excellence"),
                            ("other", "Other"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "allocation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="discounts",
                        to="hostel.hostelallocation",
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BoardingStudentStatus",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("checked_in", "Checked In"),
                            ("checked_out", "Checked Out"),
                            ("on_leave", "On Leave"),
                            ("suspended", "Suspended"),
                            ("expelled", "Expelled"),
                            ("transferred", "Transferred"),
                        ],
                        max_length=20,
                    ),
                ),
                ("status_date", models.DateTimeField(auto_now_add=True)),
                ("reason", models.TextField(blank=True)),
                ("expected_return", models.DateTimeField(blank=True, null=True)),
                ("actual_return", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "allocation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="status_history",
                        to="hostel.hostelallocation",
                    ),
                ),
                (
                    "recorded_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-status_date"],
            },
        ),
        migrations.CreateModel(
            name="HostelFacility",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100)),
                (
                    "facility_type",
                    models.CharField(
                        choices=[
                            ("common_room", "Common Room"),
                            ("study_hall", "Study Hall"),
                            ("dining_hall", "Dining Hall"),
                            ("kitchen", "Kitchen"),
                            ("laundry", "Laundry"),
                            ("bathroom", "Bathroom"),
                            ("recreation", "Recreation Area"),
                            ("medical_room", "Medical Room"),
                            ("storage", "Storage"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("location", models.CharField(blank=True, max_length=100)),
                ("capacity", models.IntegerField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("operational", "Operational"),
                            ("maintenance", "Under Maintenance"),
                            ("closed", "Closed"),
                            ("renovation", "Under Renovation"),
                        ],
                        default="operational",
                        max_length=20,
                    ),
                ),
                ("last_maintenance", models.DateField(blank=True, null=True)),
                ("next_maintenance", models.DateField(blank=True, null=True)),
                ("maintenance_notes", models.TextField(blank=True)),
                (
                    "hostel",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="facilities",
                        to="hostel.hostel",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HostelWarden",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("employee_id", models.CharField(max_length=20, unique=True)),
                (
                    "warden_type",
                    models.CharField(
                        choices=[
                            ("head_warden", "Head Warden"),
                            ("assistant_warden", "Assistant Warden"),
                            ("night_warden", "Night Warden"),
                            ("duty_warden", "Duty Warden"),
                        ],
                        max_length=20,
                    ),
                ),
                ("phone_number", models.CharField(max_length=15)),
                ("emergency_contact", models.CharField(blank=True, max_length=15)),
                ("address", models.TextField(blank=True)),
                ("hire_date", models.DateField()),
                ("is_active", models.BooleanField(default=True)),
                ("shift_start", models.TimeField(blank=True, null=True)),
                ("shift_end", models.TimeField(blank=True, null=True)),
                (
                    "assigned_hostels",
                    models.ManyToManyField(related_name="wardens", to="hostel.hostel"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warden_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BoardingFeeStructure",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100)),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("accommodation", "Accommodation Only"),
                            ("accommodation_meals", "Accommodation + Meals"),
                            ("full_boarding", "Full Boarding Package"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "room_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("single", "Single"),
                            ("double", "Double"),
                            ("dormitory", "Dormitory"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "accommodation_fee",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "security_deposit",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "maintenance_fee",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "total_monthly_fee",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("academic_year", models.CharField(max_length=20)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "hostel",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fee_structures",
                        to="hostel.hostel",
                    ),
                ),
                (
                    "meal_plan",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="hostel.mealplan",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BoardingFeePayment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("cash", "Cash"),
                            ("mpesa", "M-Pesa"),
                            ("bank_transfer", "Bank Transfer"),
                            ("cheque", "Cheque"),
                            ("card", "Credit/Debit Card"),
                        ],
                        max_length=20,
                    ),
                ),
                ("payment_date", models.DateTimeField(auto_now_add=True)),
                ("payment_for_month", models.DateField()),
                ("reference_number", models.CharField(blank=True, max_length=100)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                        ],
                        default="completed",
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "allocation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="hostel.hostelallocation",
                    ),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("allocation", "payment_for_month")},
            },
        ),
        migrations.CreateModel(
            name="HostelBed",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("bed_number", models.CharField(max_length=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("occupied", "Occupied"),
                            ("maintenance", "Under Maintenance"),
                            ("reserved", "Reserved"),
                        ],
                        default="available",
                        max_length=20,
                    ),
                ),
                ("last_maintenance", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "current_occupant",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="current_bed",
                        to="students.student",
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="beds",
                        to="hostel.hostelroom",
                    ),
                ),
            ],
            options={
                "unique_together": {("room", "bed_number")},
            },
        ),
    ]
