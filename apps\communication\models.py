from django.db import models
from django.conf import settings
from django.utils import timezone
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

class Notification(models.Model):
    NOTIFICATION_TYPES = [
        ('system', 'System Generated'),
        ('custom', 'Custom Created'),
    ]

    CHANNEL_TYPES = [
        ('in_app', 'In-App'),
        ('email', 'Email'),
        ('sms', 'SMS'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    ]

    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    channel = models.Char<PERSON><PERSON>(max_length=20, choices=CHANNEL_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    scheduled_time = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # For generic relationships
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object = GenericForeignKey('content_type', 'object_id')

    def __str__(self):
        return f"Notification for {self.user.email}: {self.title}"

    def mark_as_sent(self):
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.save()

    def mark_as_read(self):
        self.read_at = timezone.now()
        self.save()

class NotificationPreference(models.Model):
    PREFERENCES = [
        ('in_app', 'In-App'),
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('none', 'None'),
    ]

    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notification_preferences')
    in_app = models.BooleanField(default=True)
    email = models.BooleanField(default=True)
    sms = models.BooleanField(default=False)

    def __str__(self):
        return f"Preferences for {self.user.email}"

class Chat(models.Model):
    id = models.AutoField(primary_key=True)
    conversation_id = models.IntegerField()
    grade = models.CharField(max_length=255)
    sender_index = models.BigIntegerField()
    sender_type = models.CharField(max_length=255)
    receiver_index = models.BigIntegerField()
    receiver_type = models.CharField(max_length=255)
    msg = models.TextField()
    date = models.DateField()
    time = models.TimeField()
    _isread = models.IntegerField()

    def __str__(self):
        return f"Chat - {self.conversation_id}"

class OnlineChat(models.Model):
    id = models.AutoField(primary_key=True)
    conversation_id = models.IntegerField()
    user_index = models.BigIntegerField()
    msg = models.TextField()
    user_type = models.CharField(max_length=255)
    _isread = models.IntegerField()
    date = models.DateField()
    time = models.TimeField()
    has_attachment = models.BooleanField(default=False)
    attachment_url = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"Online Chat - {self.conversation_id}"

class GroupMessage(models.Model):
    id = models.AutoField(primary_key=True)
    conversation_id = models.IntegerField()
    message = models.TextField()
    sender_index = models.BigIntegerField()
    sender_type = models.CharField(max_length=255)
    group_id = models.IntegerField()
    grade = models.CharField(max_length=255)
    date = models.DateField()
    time = models.TimeField()
    has_attachment = models.BooleanField(default=False)
    attachment_type = models.CharField(max_length=50, null=True, blank=True)
    attachment_url = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"Group Message - {self.conversation_id}"

class MyFriends(models.Model):
    id = models.AutoField(primary_key=True)
    my_index = models.BigIntegerField()
    friend_index = models.BigIntegerField()
    _status = models.CharField(max_length=255)
    conversation_id = models.IntegerField()
    my_type = models.CharField(max_length=255)
    friend_type = models.CharField(max_length=255)
    _isread = models.IntegerField()

    def __str__(self):
        return f"Friendship - {self.my_index}"

class Feedback(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='feedbacks')
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Feedback from {self.user.email} at {self.created_at}"
