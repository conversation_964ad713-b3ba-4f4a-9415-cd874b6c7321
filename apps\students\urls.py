from django.urls import path
from django.shortcuts import redirect
from . import views

# For attendance redirects
from django.urls import reverse

app_name = 'students'

def redirect_to_register(request):
    """Redirect student_register/ to register/ for better UX"""
    return redirect('students:student_register')

urlpatterns = [
    # Dashboard
    path('', views.students_dashboard, name='dashboard'),

    # Student Management
    path('list/', views.students_list, name='students_list'),
    path('<int:student_id>/', views.student_detail, name='student_detail'),
    path('register/', views.student_register, name='student_register'),
    path('student_register/', redirect_to_register, name='student_register_redirect'),  # Redirect for compatibility
    path('<int:student_id>/edit/', views.student_edit, name='student_edit'),
    
    # Student Status Management
    path('<int:student_id>/status-change/', views.student_status_change, name='student_status_change'),
    
    # Grade and Subject Assignment
    path('<int:student_id>/grade-assignment/', views.student_grade_assignment, name='student_grade_assignment'),
    path('<int:student_id>/subject-assignment/', views.student_subject_assignment, name='student_subject_assignment'),
    path('bulk-assignment/', views.bulk_student_assignment, name='bulk_student_assignment'),
    
    # Document Management
    path('<int:student_id>/document-upload/', views.student_document_upload, name='student_document_upload'),
    
    # Guardian Management
    path('<int:student_id>/guardian-add/', views.guardian_add, name='guardian_add'),
    
    # Attendance Management (redirects to attendance app)
    path('attendance/', lambda request: redirect('attendance:student_attendance_entry'), name='attendance_redirect'),
    path('attendance/<int:student_id>/', lambda request, student_id: redirect('attendance:student_attendance_history_by_id', student_id=student_id), name='attendance_history_redirect'),
    
    # Grade Management (redirects to academics app)
    path('grades/', lambda request: redirect('academics:grades_list'), name='grades_redirect'),

    # Exam-related URLs
    path('<int:student_id>/exams/', views.student_exam_dashboard, name='exam_dashboard'),
    path('<int:student_id>/exam-results/', views.student_exam_results, name='exam_results'),
    path('<int:student_id>/report-cards/', views.student_report_cards, name='report_cards'),
]
