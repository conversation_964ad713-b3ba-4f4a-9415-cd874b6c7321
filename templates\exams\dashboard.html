{% extends 'exams/base.html' %}

{% block exam_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clipboard-list text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Exams</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_exams|default:0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-play-circle text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Exams</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ active_exams|default:0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Upcoming Exams</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ upcoming_exams|default:0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed Exams</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ completed_exams|default:0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="lg:col-span-2 bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Performance Overview</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ total_results|default:0 }}</div>
                    <div class="text-sm text-blue-600">Total Results</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ average_performance|floatformat:1|default:0 }}%</div>
                    <div class="text-sm text-green-600">Average Performance</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">{{ published_timetables|default:0 }}</div>
                    <div class="text-sm text-purple-600">Published Timetables</div>
                </div>
            </div>

            <!-- Performance Chart Placeholder -->
            <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-line text-4xl mb-4"></i>
                    <p>Performance Analytics Chart</p>
                    <p class="text-sm">Chart visualization would be displayed here</p>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            
            <div class="space-y-3">
                <a href="{% url 'exams:add_exam' %}" 
                   class="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Create New Exam
                </a>
                <button onclick="scheduleExam()" 
                        class="block w-full bg-green-600 text-white text-center py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
                    <i class="fas fa-calendar-plus mr-2"></i>Schedule Exam
                </button>
                <button onclick="generateTimetable()" 
                        class="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded-md hover:bg-purple-700 transition-colors">
                    <i class="fas fa-table mr-2"></i>Generate Timetable
                </button>
                <button onclick="viewReports()" 
                        class="block w-full bg-orange-600 text-white text-center py-2 px-4 rounded-md hover:bg-orange-700 transition-colors">
                    <i class="fas fa-chart-bar mr-2"></i>View Reports
                </button>
            </div>

            <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="text-sm font-medium text-gray-900 mb-3">System Status</h4>
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Exam System</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-circle text-green-400 mr-1"></i>Online
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Timetable Engine</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-circle text-green-400 mr-1"></i>Active
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Results Processing</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-circle text-yellow-400 mr-1"></i>Processing
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities and Upcoming Exams -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Exams -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Recent Exams</h3>
            </div>
            <div class="p-6">
                {% if recent_exams %}
                <div class="space-y-4">
                    {% for exam in recent_exams %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">{{ exam.name }}</h4>
                            <p class="text-xs text-gray-500">General Exam • {{ exam.start_date|date:"M d, Y" }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="exam-status-badge exam-status-{{ exam.status }}">
                                {{ exam.get_status_display }}
                            </span>
                            <a href="#" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-clipboard-list text-2xl mb-2"></i>
                    <p class="text-sm">No recent exams</p>
                </div>
                {% endif %}
                
                <div class="mt-4 text-center">
                    <a href="{% url 'exams:exam_list' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View All Exams <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Upcoming Exams -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Upcoming Exams</h3>
            </div>
            <div class="p-6">
                {% if upcoming_exam_list %}
                <div class="space-y-4">
                    {% for exam in upcoming_exam_list %}
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">{{ exam.name }}</h4>
                            <p class="text-xs text-gray-500">General Exam</p>
                            <p class="text-xs text-yellow-700 font-medium">
                                <i class="fas fa-calendar mr-1"></i>{{ exam.start_date|date:"M d, Y" }}
                            </p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="exam-type-badge exam-type-general">
                                General
                            </span>
                            <a href="#" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-cog"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-calendar-plus text-2xl mb-2"></i>
                    <p class="text-sm">No upcoming exams scheduled</p>
                    <a href="{% url 'exams:add_exam' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">
                        Schedule an Exam
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Exam Alerts -->
    {% if exam_alerts %}
    <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Exam Alerts</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <ul class="list-disc list-inside space-y-1">
                        {% for alert in exam_alerts %}
                        <li>{{ alert.message }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function scheduleExam() {
    window.location.href = "{% url 'exams:add_exam' %}";
}

function generateTimetable() {
    // Implementation for timetable generation
    alert('Timetable generation feature coming soon!');
}

function viewReports() {
    // Implementation for viewing reports
    alert('Reports feature coming soon!');
}

// Real-time updates for dashboard metrics
function updateDashboardMetrics() {
    fetch('/exams/api/dashboard-metrics/')
        .then(response => response.json())
        .then(data => {
            // Update metrics without page reload
            console.log('Dashboard metrics updated');
        })
        .catch(error => console.error('Failed to update metrics:', error));
}

// Update metrics every 2 minutes
setInterval(updateDashboardMetrics, 120000);

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Any initialization code
    console.log('Exam dashboard loaded');
});
</script>
{% endblock %}
