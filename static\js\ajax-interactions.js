// AJAX-powered Interactions System
class AjaxManager {
    constructor() {
        this.loadingStates = new Map();
        this.requestQueue = [];
        this.maxConcurrentRequests = 3;
        this.activeRequests = 0;
        this.init();
    }

    init() {
        this.setupGlobalErrorHandling();
        this.setupFormInterceptors();
        this.setupLinkInterceptors();
        this.setupCSRFToken();
    }

    // CSRF Token Management
    setupCSRFToken() {
        this.csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        
        // Update CSRF token periodically
        setInterval(() => {
            this.refreshCSRFToken();
        }, 300000); // 5 minutes
    }

    async refreshCSRFToken() {
        try {
            const response = await fetch('/api/csrf-token/');
            const data = await response.json();
            this.csrfToken = data.token;
        } catch (error) {
            console.warn('Failed to refresh CSRF token:', error);
        }
    }

    // Generic AJAX Request Handler
    async request(url, options = {}) {
        const requestId = this.generateRequestId();
        
        // Default options
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': this.csrfToken
            },
            credentials: 'same-origin'
        };

        // Merge options
        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        // Queue request if too many active
        if (this.activeRequests >= this.maxConcurrentRequests) {
            return new Promise((resolve, reject) => {
                this.requestQueue.push({ url, options: finalOptions, resolve, reject, requestId });
            });
        }

        return this.executeRequest(url, finalOptions, requestId);
    }

    async executeRequest(url, options, requestId) {
        this.activeRequests++;
        this.showLoading(requestId);

        try {
            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            let data;

            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }

            this.hideLoading(requestId);
            this.processNextRequest();

            return { data, response };

        } catch (error) {
            this.hideLoading(requestId);
            this.handleError(error, url, options);
            this.processNextRequest();
            throw error;
        }
    }

    processNextRequest() {
        this.activeRequests--;
        
        if (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
            const { url, options, resolve, reject, requestId } = this.requestQueue.shift();
            this.executeRequest(url, options, requestId)
                .then(resolve)
                .catch(reject);
        }
    }

    // Form Handling
    setupFormInterceptors() {
        document.addEventListener('submit', (event) => {
            const form = event.target;
            
            if (form.hasAttribute('data-ajax') || form.classList.contains('ajax-form')) {
                event.preventDefault();
                this.handleFormSubmission(form);
            }
        });
    }

    async handleFormSubmission(form) {
        const formData = new FormData(form);
        const url = form.action || window.location.href;
        const method = form.method || 'POST';

        // Show form loading state
        this.setFormLoading(form, true);

        try {
            const { data } = await this.request(url, {
                method: method.toUpperCase(),
                body: formData,
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });

            this.handleFormSuccess(form, data);

        } catch (error) {
            this.handleFormError(form, error);
        } finally {
            this.setFormLoading(form, false);
        }
    }

    handleFormSuccess(form, data) {
        // Clear form errors
        this.clearFormErrors(form);

        // Handle different response types
        if (data.redirect) {
            window.location.href = data.redirect;
        } else if (data.reload) {
            window.location.reload();
        } else if (data.html) {
            this.updateContent(data.target || form.getAttribute('data-target'), data.html);
        } else if (data.message) {
            this.showMessage(data.message, 'success');
        }

        // Trigger custom event
        form.dispatchEvent(new CustomEvent('ajax:success', { detail: data }));
    }

    handleFormError(form, error) {
        if (error.response && error.response.status === 400) {
            // Handle validation errors
            error.response.json().then(data => {
                this.displayFormErrors(form, data.errors || {});
            });
        } else {
            this.showMessage('An error occurred. Please try again.', 'error');
        }

        // Trigger custom event
        form.dispatchEvent(new CustomEvent('ajax:error', { detail: error }));
    }

    // Link Handling
    setupLinkInterceptors() {
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a[data-ajax], a.ajax-link');
            
            if (link && !event.ctrlKey && !event.metaKey) {
                event.preventDefault();
                this.handleLinkClick(link);
            }
        });
    }

    async handleLinkClick(link) {
        const url = link.href;
        const target = link.getAttribute('data-target');
        const method = link.getAttribute('data-method') || 'GET';

        try {
            const { data } = await this.request(url, { method });

            if (typeof data === 'string') {
                // HTML response
                if (target) {
                    this.updateContent(target, data);
                } else {
                    document.body.innerHTML = data;
                }
            } else {
                // JSON response
                this.handleLinkSuccess(link, data);
            }

        } catch (error) {
            this.handleError(error, url);
        }
    }

    handleLinkSuccess(link, data) {
        if (data.redirect) {
            window.location.href = data.redirect;
        } else if (data.html && data.target) {
            this.updateContent(data.target, data.html);
        } else if (data.message) {
            this.showMessage(data.message, data.type || 'info');
        }

        // Trigger custom event
        link.dispatchEvent(new CustomEvent('ajax:success', { detail: data }));
    }

    // Content Updates
    updateContent(selector, html) {
        const elements = document.querySelectorAll(selector);
        
        elements.forEach(element => {
            // Fade out
            element.style.opacity = '0.5';
            element.style.transition = 'opacity 0.3s ease';
            
            setTimeout(() => {
                element.innerHTML = html;
                
                // Fade in
                element.style.opacity = '1';
                
                // Reinitialize any JavaScript components
                this.reinitializeComponents(element);
                
                // Trigger update event
                element.dispatchEvent(new CustomEvent('content:updated'));
            }, 150);
        });
    }

    reinitializeComponents(container) {
        // Reinitialize Alpine.js components
        if (window.Alpine) {
            window.Alpine.initTree(container);
        }

        // Reinitialize any other components
        container.dispatchEvent(new CustomEvent('components:reinitialize'));
    }

    // Loading States
    showLoading(requestId, element = null) {
        if (element) {
            element.classList.add('loading');
            element.style.pointerEvents = 'none';
        }
        
        this.loadingStates.set(requestId, { element, startTime: Date.now() });
    }

    hideLoading(requestId) {
        const loadingState = this.loadingStates.get(requestId);
        
        if (loadingState && loadingState.element) {
            loadingState.element.classList.remove('loading');
            loadingState.element.style.pointerEvents = '';
        }
        
        this.loadingStates.delete(requestId);
    }

    setFormLoading(form, loading) {
        const submitButton = form.querySelector('[type="submit"]');
        const inputs = form.querySelectorAll('input, select, textarea');

        if (loading) {
            form.classList.add('loading');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            }
            inputs.forEach(input => input.disabled = true);
        } else {
            form.classList.remove('loading');
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.innerHTML = submitButton.getAttribute('data-original-text') || 'Submit';
            }
            inputs.forEach(input => input.disabled = false);
        }
    }

    // Error Handling
    setupGlobalErrorHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showMessage('An unexpected error occurred.', 'error');
        });
    }

    handleError(error, url, options = {}) {
        console.error('AJAX Error:', error, 'URL:', url);

        // Handle specific error types
        if (error.message.includes('403')) {
            this.showMessage('Access denied. Please check your permissions.', 'error');
        } else if (error.message.includes('404')) {
            this.showMessage('The requested resource was not found.', 'error');
        } else if (error.message.includes('500')) {
            this.showMessage('Server error. Please try again later.', 'error');
        } else if (error.name === 'NetworkError' || error.message.includes('Failed to fetch')) {
            this.showMessage('Network error. Please check your connection.', 'error');
        } else {
            this.showMessage('An error occurred. Please try again.', 'error');
        }
    }

    // Form Error Display
    displayFormErrors(form, errors) {
        this.clearFormErrors(form);

        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'text-red-500 text-sm mt-1 field-error';
                errorDiv.textContent = errors[fieldName][0]; // First error message
                
                field.classList.add('border-red-500');
                field.parentNode.appendChild(errorDiv);
            }
        });
    }

    clearFormErrors(form) {
        // Remove error styling
        form.querySelectorAll('.border-red-500').forEach(field => {
            field.classList.remove('border-red-500');
        });

        // Remove error messages
        form.querySelectorAll('.field-error').forEach(error => {
            error.remove();
        });
    }

    // Message Display
    showMessage(message, type = 'info') {
        // Dispatch event for notification system
        document.dispatchEvent(new CustomEvent('add-notification', {
            detail: {
                id: this.generateRequestId(),
                title: type.charAt(0).toUpperCase() + type.slice(1),
                message: message,
                type: type,
                timestamp: new Date().toISOString()
            }
        }));
    }

    // Utility Methods
    generateRequestId() {
        return 'req_' + Math.random().toString(36).substr(2, 9);
    }

    // Public API Methods
    get(url, options = {}) {
        return this.request(url, { ...options, method: 'GET' });
    }

    post(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    put(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }
}

// Initialize AJAX manager
const ajaxManager = new AjaxManager();

// Global API
window.ajax = ajaxManager;

// jQuery-like shortcuts for backward compatibility
window.$.ajax = (options) => {
    return ajaxManager.request(options.url, options);
};

window.$.get = (url, data) => ajaxManager.get(url, { params: data });
window.$.post = (url, data) => ajaxManager.post(url, data);

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AjaxManager;
}
