from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg, Max, Min
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal
import json

from .models import (
    ExamType, Exam, GradingSystem, GradeScale, ExamTimetable,
    EnhancedStudentExamResult, StudentOverallPerformance, SubjectPerformanceAnalysis,
    ReportCard, PerformanceComparison, ClassRanking, StudentRanking,
    ExamRangeGrade, StudentExam, StudentOverallExamResult,
    BulkResultUpload, ExamNotification, ExamAuditLog
)
from .forms import ExamTypeForm, ExamForm, ExamTimetableForm
from apps.students.models import Student
from apps.academics.models import Grade, Subject, ClassRoom
from apps.teachers.models import Teacher
import pandas as pd
import openpyxl
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import csv
import io

@login_required
def exam_dashboard(request):
    """Exam management dashboard with statistics and overview."""
    today = date.today()
    current_year = today.year

    # Calculate key exam metrics
    total_exams = Exam.objects.filter(academic_year=current_year).count()
    active_exams = Exam.objects.filter(
        status='active',
        start_date__lte=today,
        end_date__gte=today
    ).count()
    upcoming_exams = Exam.objects.filter(
        start_date__gt=today,
        academic_year=current_year
    ).count()
    completed_exams = Exam.objects.filter(
        status='completed',
        academic_year=current_year
    ).count()

    # Recent activities (avoiding ExamType table completely)
    try:
        recent_exams = Exam.objects.filter(
            academic_year=current_year
        ).order_by('-created_at')[:10]
    except Exception:
        recent_exams = []

    try:
        upcoming_exam_list = Exam.objects.filter(
            start_date__gt=today,
            academic_year=current_year
        ).order_by('start_date')[:5]
    except Exception:
        upcoming_exam_list = []

    # Performance statistics (with error handling for missing tables)
    try:
        total_results = EnhancedStudentExamResult.objects.filter(
            exam__academic_year=current_year,
            status='verified'
        ).count()

        average_performance = EnhancedStudentExamResult.objects.filter(
            exam__academic_year=current_year,
            status='verified'
        ).aggregate(avg_percentage=Avg('percentage'))['avg_percentage'] or 0
    except Exception as e:
        # Handle case where EnhancedStudentExamResult table doesn't exist
        total_results = 0
        average_performance = 0

    # Timetable statistics (with error handling for missing columns)
    try:
        total_timetable_entries = ExamTimetable.objects.filter(
            exam__academic_year=current_year
        ).count()

        published_timetables = ExamTimetable.objects.filter(
            exam__academic_year=current_year,
            is_published=True
        ).count()
    except Exception as e:
        # Handle case where ExamTimetable table or columns don't exist
        total_timetable_entries = 0
        published_timetables = 0

    context = {
        'total_exams': total_exams,
        'active_exams': active_exams,
        'upcoming_exams': upcoming_exams,
        'completed_exams': completed_exams,
        'recent_exams': recent_exams,
        'upcoming_exam_list': upcoming_exam_list,
        'total_results': total_results,
        'average_performance': round(average_performance, 2),
        'total_timetable_entries': total_timetable_entries,
        'published_timetables': published_timetables,
        'exam_types': 0,  # Will be updated below with error handling
        'grading_systems': 0,  # Will be updated below with error handling
    }

    # Add additional statistics with error handling
    try:
        context['exam_types'] = ExamType.objects.filter(is_active=True).count()
    except Exception:
        context['exam_types'] = 0

    try:
        context['grading_systems'] = GradingSystem.objects.filter(is_active=True).count()
    except Exception:
        context['grading_systems'] = 0

    return render(request, 'exams/dashboard.html', context)

@login_required
def exam_list(request):
    """List all exams with search and filter functionality."""
    # Try to get exams with exam_type, fallback to basic query if ExamType table doesn't exist
    try:
        exams = Exam.objects.select_related('exam_type').order_by('-academic_year', '-start_date')
        use_exam_type_search = True
    except Exception:
        exams = Exam.objects.order_by('-academic_year', '-start_date')
        use_exam_type_search = False

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        if use_exam_type_search:
            try:
                exams = exams.filter(
                    Q(name__icontains=search_query) |
                    Q(exam_code__icontains=search_query) |
                    Q(exam_type__name__icontains=search_query)
                )
            except Exception:
                # Fallback to basic search without exam_type
                exams = exams.filter(
                    Q(name__icontains=search_query) |
                    Q(exam_code__icontains=search_query)
                )
        else:
            exams = exams.filter(
                Q(name__icontains=search_query) |
                Q(exam_code__icontains=search_query)
            )

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        exams = exams.filter(status=status_filter)

    # Filter by academic year
    year_filter = request.GET.get('year')
    if year_filter:
        exams = exams.filter(academic_year=year_filter)

    # Filter by exam type (only if ExamType table exists)
    type_filter = request.GET.get('type')
    if type_filter and use_exam_type_search:
        try:
            exams = exams.filter(exam_type_id=type_filter)
        except Exception:
            # Ignore exam type filter if table doesn't exist
            pass

    paginator = Paginator(exams, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get exam types with error handling
    try:
        exam_types = ExamType.objects.filter(is_active=True)
    except Exception:
        exam_types = []

    context = {
        'page_obj': page_obj,
        'exams': page_obj,
        'exam_types': exam_types,
        'years': range(date.today().year - 2, date.today().year + 3),
        'status_choices': Exam.STATUS_CHOICES,
        'search_query': search_query,
        'status_filter': status_filter,
        'year_filter': year_filter,
        'type_filter': type_filter,
    }
    return render(request, 'exams/exam_list.html', context)

@login_required
def add_exam(request):
    """Add a new exam."""
    # Check if ExamType table exists
    try:
        from .models import ExamType
        ExamType.objects.exists()
        exam_type_available = True
    except Exception:
        exam_type_available = False

    if not exam_type_available:
        # If ExamType table doesn't exist, show a message and redirect
        messages.error(request, 'Exam types are not available yet. Please contact the administrator to set up exam types first.')
        return redirect('exams:exam_list')

    if request.method == 'POST':
        try:
            form = ExamForm(request.POST)
            if form.is_valid():
                exam = form.save(commit=False)
                exam.created_by = request.user
                exam.save()
                form.save_m2m()  # Save many-to-many relationships
                messages.success(request, f'Exam "{exam.name}" has been created successfully.')
                return redirect('exams:exam_detail', exam_id=exam.id)
        except Exception as e:
            messages.error(request, f'Error creating exam: {str(e)}')
            form = ExamForm()
    else:
        try:
            form = ExamForm()
        except Exception as e:
            messages.error(request, f'Error loading form: {str(e)}')
            return redirect('exams:exam_list')

    context = {'form': form}
    return render(request, 'exams/add_exam.html', context)

@login_required
def exam_detail(request, exam_id):
    """View detailed information about a specific exam."""
    exam = get_object_or_404(Exam, id=exam_id)

    # Get exam statistics
    total_students = EnhancedStudentExamResult.objects.filter(exam=exam).count()
    submitted_results = EnhancedStudentExamResult.objects.filter(exam=exam, status='submitted').count()
    verified_results = EnhancedStudentExamResult.objects.filter(exam=exam, status='verified').count()

    # Get timetable entries
    timetable_entries = ExamTimetable.objects.filter(exam=exam).select_related('grade', 'subject', 'classroom')

    # Get recent activities
    recent_activities = ExamAuditLog.objects.filter(exam=exam).order_by('-timestamp')[:10]

    context = {
        'exam': exam,
        'total_students': total_students,
        'submitted_results': submitted_results,
        'verified_results': verified_results,
        'timetable_entries': timetable_entries,
        'recent_activities': recent_activities,
    }
    return render(request, 'exams/exam_detail.html', context)

@login_required
def edit_exam(request, exam_id):
    """Edit an existing exam."""
    exam = get_object_or_404(Exam, id=exam_id)

    if request.method == 'POST':
        form = ExamForm(request.POST, instance=exam)
        if form.is_valid():
            # Log the changes
            old_values = {
                'name': exam.name,
                'start_date': str(exam.start_date),
                'end_date': str(exam.end_date),
                'status': exam.status,
            }

            exam = form.save()

            # Create audit log
            ExamAuditLog.objects.create(
                exam=exam,
                action_type='update',
                description=f'Exam "{exam.name}" updated',
                old_values=old_values,
                new_values={
                    'name': exam.name,
                    'start_date': str(exam.start_date),
                    'end_date': str(exam.end_date),
                    'status': exam.status,
                },
                user=request.user,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, f'Exam "{exam.name}" has been updated successfully.')
            return redirect('exams:exam_detail', exam_id=exam.id)
    else:
        form = ExamForm(instance=exam)

    context = {'form': form, 'exam': exam}
    return render(request, 'exams/edit_exam.html', context)

@login_required
def delete_exam(request, exam_id):
    """Delete an exam."""
    exam = get_object_or_404(Exam, id=exam_id)

    if request.method == 'POST':
        exam_name = exam.name

        # Create audit log before deletion
        ExamAuditLog.objects.create(
            action_type='delete',
            description=f'Exam "{exam_name}" deleted',
            old_values={'name': exam_name, 'id': exam.id},
            user=request.user,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        exam.delete()
        messages.success(request, f'Exam "{exam_name}" has been deleted successfully.')
        return redirect('exams:exam_list')

    context = {'exam': exam}
    return render(request, 'exams/delete_exam.html', context)

# Exam Type Management Views

@login_required
def exam_type_list(request):
    """List all exam types."""
    exam_types = ExamType.objects.filter(is_active=True).order_by('category', 'name')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        exam_types = exam_types.filter(
            Q(name__icontains=search_query) |
            Q(category__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by category
    category_filter = request.GET.get('category')
    if category_filter:
        exam_types = exam_types.filter(category=category_filter)

    # Filter by education level
    level_filter = request.GET.get('level')
    if level_filter:
        exam_types = exam_types.filter(education_level=level_filter)

    paginator = Paginator(exam_types, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'exam_types': page_obj,
        'categories': ExamType.TYPE_CATEGORIES,
        'education_levels': ExamType.EDUCATION_LEVELS,
        'search_query': search_query,
        'category_filter': category_filter,
        'level_filter': level_filter,
    }
    return render(request, 'exams/exam_type_list.html', context)

@login_required
def add_exam_type(request):
    """Add a new exam type."""
    if request.method == 'POST':
        form = ExamTypeForm(request.POST)
        if form.is_valid():
            exam_type = form.save()
            messages.success(request, f'Exam type "{exam_type.name}" has been created successfully.')
            return redirect('exams:exam_type_list')
    else:
        form = ExamTypeForm()

    context = {'form': form}
    return render(request, 'exams/add_exam_type.html', context)

@login_required
def edit_exam_type(request, type_id):
    """Edit an existing exam type."""
    exam_type = get_object_or_404(ExamType, id=type_id)

    if request.method == 'POST':
        form = ExamTypeForm(request.POST, instance=exam_type)
        if form.is_valid():
            exam_type = form.save()
            messages.success(request, f'Exam type "{exam_type.name}" has been updated successfully.')
            return redirect('exams:exam_type_list')
    else:
        form = ExamTypeForm(instance=exam_type)

    context = {'form': form, 'exam_type': exam_type}
    return render(request, 'exams/edit_exam_type.html', context)

# Timetable Management Views

@login_required
def timetable_list(request):
    """List all exam timetables."""
    timetables = ExamTimetable.objects.select_related('exam', 'grade', 'subject', 'classroom').order_by('-exam__start_date', 'exam_date', 'start_time')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        timetables = timetables.filter(
            Q(exam__name__icontains=search_query) |
            Q(subject__name__icontains=search_query) |
            Q(grade__name__icontains=search_query)
        )

    # Filter by exam
    exam_filter = request.GET.get('exam')
    if exam_filter:
        timetables = timetables.filter(exam_id=exam_filter)

    # Filter by grade
    grade_filter = request.GET.get('grade')
    if grade_filter:
        timetables = timetables.filter(grade_id=grade_filter)

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        timetables = timetables.filter(status=status_filter)

    paginator = Paginator(timetables, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    exams = Exam.objects.filter(status__in=['scheduled', 'active']).order_by('-start_date')
    grades = Grade.objects.all().order_by('name')

    context = {
        'page_obj': page_obj,
        'timetables': page_obj,
        'exams': exams,
        'grades': grades,
        'status_choices': ExamTimetable.TIMETABLE_STATUS,
        'search_query': search_query,
        'exam_filter': exam_filter,
        'grade_filter': grade_filter,
        'status_filter': status_filter,
    }
    return render(request, 'exams/timetable_list.html', context)

@login_required
def add_timetable(request):
    """Add a new timetable entry."""
    if request.method == 'POST':
        form = ExamTimetableForm(request.POST)
        if form.is_valid():
            timetable = form.save(commit=False)
            timetable.created_by = request.user
            timetable.save()
            form.save_m2m()
            messages.success(request, 'Timetable entry has been created successfully.')
            return redirect('exams:timetable_list')
    else:
        form = ExamTimetableForm()

    context = {'form': form}
    return render(request, 'exams/add_timetable.html', context)

@login_required
def edit_timetable(request, timetable_id):
    """Edit an existing timetable entry."""
    timetable = get_object_or_404(ExamTimetable, id=timetable_id)

    if request.method == 'POST':
        form = ExamTimetableForm(request.POST, instance=timetable)
        if form.is_valid():
            timetable = form.save()
            messages.success(request, 'Timetable entry has been updated successfully.')
            return redirect('exams:timetable_list')
    else:
        form = ExamTimetableForm(instance=timetable)

    context = {'form': form, 'timetable': timetable}
    return render(request, 'exams/edit_timetable.html', context)

@login_required
def timetable_detail(request, timetable_id):
    """View detailed information about a timetable entry."""
    timetable = get_object_or_404(ExamTimetable, id=timetable_id)

    # Get student results for this timetable entry
    results = EnhancedStudentExamResult.objects.filter(timetable_entry=timetable).select_related('student')

    # Calculate statistics
    total_expected = timetable.expected_candidates
    total_present = results.filter(attendance_status='present').count()
    total_absent = results.filter(attendance_status='absent').count()

    context = {
        'timetable': timetable,
        'results': results,
        'total_expected': total_expected,
        'total_present': total_present,
        'total_absent': total_absent,
    }
    return render(request, 'exams/timetable_detail.html', context)

@login_required
def publish_timetable(request, timetable_id):
    """Publish a timetable entry."""
    timetable = get_object_or_404(ExamTimetable, id=timetable_id)

    if request.method == 'POST':
        timetable.is_published = True
        timetable.published_at = timezone.now()
        timetable.status = 'published'
        timetable.save()

        # Create notification
        ExamNotification.objects.create(
            exam=timetable.exam,
            notification_type='timetable_published',
            recipient_type='all',
            title=f'Timetable Published: {timetable.exam.name}',
            message=f'The timetable for {timetable.subject.name} - {timetable.grade.name} has been published.',
            created_by=request.user
        )

        messages.success(request, 'Timetable has been published successfully.')
        return redirect('exams:timetable_detail', timetable_id=timetable.id)

    context = {'timetable': timetable}
    return render(request, 'exams/publish_timetable.html', context)

@login_required
def generate_timetable(request, exam_id):
    """Generate timetable for an entire exam."""
    exam = get_object_or_404(Exam, id=exam_id)

    if request.method == 'POST':
        # Get selected grades and subjects
        selected_grades = request.POST.getlist('grades')
        selected_subjects = request.POST.getlist('subjects')

        # Generate timetable entries
        created_count = 0
        for grade_id in selected_grades:
            grade = Grade.objects.get(id=grade_id)
            for subject_id in selected_subjects:
                subject = Subject.objects.get(id=subject_id)

                # Check if timetable entry already exists
                if not ExamTimetable.objects.filter(exam=exam, grade=grade, subject=subject).exists():
                    # Create timetable entry
                    ExamTimetable.objects.create(
                        exam=exam,
                        grade=grade,
                        subject=subject,
                        exam_date=exam.start_date,  # Default to exam start date
                        start_time='08:00',  # Default start time
                        end_time='10:00',   # Default end time
                        duration_minutes=120,
                        total_marks=exam.total_marks,
                        created_by=request.user
                    )
                    created_count += 1

        messages.success(request, f'{created_count} timetable entries have been generated successfully.')
        return redirect('exams:timetable_list')

    # Get available grades and subjects for the exam
    grades = exam.grades.all()
    subjects = exam.subjects.all()

    context = {
        'exam': exam,
        'grades': grades,
        'subjects': subjects,
    }
    return render(request, 'exams/generate_timetable.html', context)

# Result Entry and Management Views

@login_required
def result_entry_dashboard(request):
    """Dashboard for result entry activities."""
    # Get current user's assigned subjects (if teacher)
    user_subjects = []
    if hasattr(request.user, 'teacher'):
        # Get subjects assigned to this teacher
        from apps.academics.models import SubjectRouting
        user_subjects = SubjectRouting.objects.filter(teacher=request.user.teacher).select_related('subject', 'grade')

    # Get pending result entries
    pending_exams = Exam.objects.filter(
        status__in=['active', 'completed'],
        results_published=False
    ).order_by('-start_date')

    # Get recent result entries
    recent_results = EnhancedStudentExamResult.objects.filter(
        marked_by=request.user
    ).order_by('-created_at')[:10]

    # Get bulk upload history
    recent_uploads = BulkResultUpload.objects.filter(
        uploaded_by=request.user
    ).order_by('-created_at')[:5]

    context = {
        'user_subjects': user_subjects,
        'pending_exams': pending_exams,
        'recent_results': recent_results,
        'recent_uploads': recent_uploads,
    }
    return render(request, 'exams/result_entry_dashboard.html', context)

@login_required
def result_entry_form(request, timetable_id):
    """Manual result entry form for a specific timetable entry."""
    timetable = get_object_or_404(ExamTimetable, id=timetable_id)

    # Get students for this grade
    students = Student.objects.filter(
        current_grade=timetable.grade,
        status='active'
    ).order_by('full_name')

    # Get existing results
    existing_results = {}
    for result in EnhancedStudentExamResult.objects.filter(timetable_entry=timetable):
        existing_results[result.student.id] = result

    if request.method == 'POST':
        results_saved = 0
        errors = []

        for student in students:
            marks_key = f'marks_{student.id}'
            attendance_key = f'attendance_{student.id}'
            remarks_key = f'remarks_{student.id}'

            if marks_key in request.POST:
                try:
                    marks = float(request.POST[marks_key]) if request.POST[marks_key] else None
                    attendance = request.POST.get(attendance_key, 'present')
                    remarks = request.POST.get(remarks_key, '')

                    if marks is not None:
                        # Create or update result
                        result, created = EnhancedStudentExamResult.objects.get_or_create(
                            student=student,
                            exam=timetable.exam,
                            timetable_entry=timetable,
                            defaults={
                                'marks_obtained': marks,
                                'total_marks': timetable.total_marks,
                                'attendance_status': attendance,
                                'teacher_remarks': remarks,
                                'marked_by': request.user,
                                'marked_date': timezone.now(),
                                'status': 'submitted'
                            }
                        )

                        if not created:
                            # Update existing result
                            result.marks_obtained = marks
                            result.attendance_status = attendance
                            result.teacher_remarks = remarks
                            result.marked_by = request.user
                            result.marked_date = timezone.now()
                            result.save()

                        results_saved += 1

                except ValueError as e:
                    errors.append(f'Invalid marks for {student.full_name}: {str(e)}')

        if results_saved > 0:
            messages.success(request, f'{results_saved} results have been saved successfully.')

            # Create audit log
            ExamAuditLog.objects.create(
                exam=timetable.exam,
                action_type='result_entry',
                description=f'Manual result entry for {timetable.subject.name} - {timetable.grade.name}',
                new_values={'results_count': results_saved},
                user=request.user,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

        if errors:
            for error in errors:
                messages.error(request, error)

        return redirect('exams:result_entry_form', timetable_id=timetable.id)

    context = {
        'timetable': timetable,
        'students': students,
        'existing_results': existing_results,
    }
    return render(request, 'exams/result_entry_form.html', context)

@login_required
def bulk_result_upload(request, exam_id):
    """Bulk result upload via Excel/CSV file."""
    exam = get_object_or_404(Exam, id=exam_id)

    if request.method == 'POST':
        if 'file' not in request.FILES:
            messages.error(request, 'Please select a file to upload.')
            return redirect('exams:bulk_result_upload', exam_id=exam.id)

        uploaded_file = request.FILES['file']

        # Validate file type
        if not uploaded_file.name.endswith(('.xlsx', '.xls', '.csv')):
            messages.error(request, 'Please upload an Excel (.xlsx, .xls) or CSV (.csv) file.')
            return redirect('exams:bulk_result_upload', exam_id=exam.id)

        # Create bulk upload record
        bulk_upload = BulkResultUpload.objects.create(
            exam=exam,
            uploaded_file=uploaded_file,
            original_filename=uploaded_file.name,
            uploaded_by=request.user,
            status='processing'
        )

        try:
            # Process the file
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            else:
                df = pd.read_excel(uploaded_file)

            # Validate required columns
            required_columns = ['student_id', 'subject', 'marks', 'grade']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                bulk_upload.status = 'failed'
                bulk_upload.error_log = f'Missing required columns: {", ".join(missing_columns)}'
                bulk_upload.save()
                messages.error(request, f'Missing required columns: {", ".join(missing_columns)}')
                return redirect('exams:bulk_result_upload', exam_id=exam.id)

            # Process each row
            successful_count = 0
            failed_count = 0
            error_messages = []

            for index, row in df.iterrows():
                try:
                    # Find student
                    student = Student.objects.get(
                        Q(student_id=row['student_id']) | Q(index_number=row['student_id'])
                    )

                    # Find subject
                    subject = Subject.objects.get(name__iexact=row['subject'])

                    # Find grade
                    grade = Grade.objects.get(name__iexact=row['grade'])

                    # Find timetable entry
                    timetable = ExamTimetable.objects.get(
                        exam=exam,
                        subject=subject,
                        grade=grade
                    )

                    # Create or update result
                    result, created = EnhancedStudentExamResult.objects.get_or_create(
                        student=student,
                        exam=exam,
                        timetable_entry=timetable,
                        defaults={
                            'marks_obtained': float(row['marks']),
                            'total_marks': timetable.total_marks,
                            'attendance_status': row.get('attendance', 'present'),
                            'teacher_remarks': row.get('remarks', ''),
                            'marked_by': request.user,
                            'marked_date': timezone.now(),
                            'status': 'submitted'
                        }
                    )

                    if not created:
                        result.marks_obtained = float(row['marks'])
                        result.attendance_status = row.get('attendance', 'present')
                        result.teacher_remarks = row.get('remarks', '')
                        result.marked_by = request.user
                        result.marked_date = timezone.now()
                        result.save()

                    successful_count += 1

                except Exception as e:
                    failed_count += 1
                    error_messages.append(f'Row {index + 2}: {str(e)}')

            # Update bulk upload record
            bulk_upload.total_records = len(df)
            bulk_upload.successful_records = successful_count
            bulk_upload.failed_records = failed_count
            bulk_upload.status = 'completed' if failed_count == 0 else 'partially_completed'
            bulk_upload.error_log = '\n'.join(error_messages) if error_messages else ''
            bulk_upload.processed_at = timezone.now()
            bulk_upload.processed_by = request.user
            bulk_upload.save()

            # Create audit log
            ExamAuditLog.objects.create(
                exam=exam,
                action_type='bulk_upload',
                description=f'Bulk result upload: {successful_count} successful, {failed_count} failed',
                new_values={
                    'total_records': len(df),
                    'successful_records': successful_count,
                    'failed_records': failed_count
                },
                user=request.user,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            if successful_count > 0:
                messages.success(request, f'{successful_count} results uploaded successfully.')

            if failed_count > 0:
                messages.warning(request, f'{failed_count} results failed to upload. Check the error log for details.')

        except Exception as e:
            bulk_upload.status = 'failed'
            bulk_upload.error_log = str(e)
            bulk_upload.save()
            messages.error(request, f'Error processing file: {str(e)}')

        return redirect('exams:bulk_upload_detail', upload_id=bulk_upload.id)

    # Get sample template data
    sample_data = [
        {'student_id': '12345', 'subject': 'Mathematics', 'grade': 'Form 1A', 'marks': 85, 'attendance': 'present', 'remarks': 'Good performance'},
        {'student_id': '12346', 'subject': 'Mathematics', 'grade': 'Form 1A', 'marks': 72, 'attendance': 'present', 'remarks': 'Needs improvement'},
    ]

    context = {
        'exam': exam,
        'sample_data': sample_data,
    }
    return render(request, 'exams/bulk_result_upload.html', context)

@login_required
def bulk_upload_detail(request, upload_id):
    """View details of a bulk upload operation."""
    upload = get_object_or_404(BulkResultUpload, id=upload_id)

    context = {
        'upload': upload,
    }
    return render(request, 'exams/bulk_upload_detail.html', context)

@login_required
def download_template(request, exam_id):
    """Download Excel template for bulk result upload."""
    exam = get_object_or_404(Exam, id=exam_id)

    # Create sample data
    data = []
    for timetable in ExamTimetable.objects.filter(exam=exam).select_related('subject', 'grade'):
        # Get a few sample students for this grade
        students = Student.objects.filter(current_grade=timetable.grade, status='active')[:3]
        for student in students:
            data.append({
                'student_id': student.student_id or student.index_number,
                'student_name': student.full_name,
                'subject': timetable.subject.name,
                'grade': timetable.grade.name,
                'marks': '',  # Empty for user to fill
                'attendance': 'present',
                'remarks': ''
            })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create Excel response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename="{exam.name}_results_template.xlsx"'

    # Write to Excel
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Results', index=False)

        # Add instructions sheet
        instructions = pd.DataFrame({
            'Instructions': [
                '1. Fill in the marks for each student',
                '2. Attendance should be "present" or "absent"',
                '3. Remarks are optional',
                '4. Do not modify student_id, student_name, subject, or grade columns',
                '5. Save the file and upload it back to the system'
            ]
        })
        instructions.to_excel(writer, sheet_name='Instructions', index=False)

    return response

# Performance Analysis Views

@login_required
def performance_dashboard(request):
    """Main performance analysis dashboard."""
    # Get recent exams
    recent_exams = Exam.objects.filter(
        status__in=['completed', 'results_published']
    ).order_by('-end_date')[:10]

    # Get overall statistics
    total_students = Student.objects.filter(status='active').count()
    total_results = EnhancedStudentExamResult.objects.filter(status='verified').count()

    # Get performance trends (last 6 months)
    six_months_ago = timezone.now() - timedelta(days=180)
    performance_data = EnhancedStudentExamResult.objects.filter(
        exam__end_date__gte=six_months_ago,
        status='verified'
    ).values('exam__name', 'exam__end_date').annotate(
        avg_marks=Avg('percentage_score'),
        total_students=Count('id')
    ).order_by('exam__end_date')

    context = {
        'recent_exams': recent_exams,
        'total_students': total_students,
        'total_results': total_results,
        'performance_data': performance_data,
    }
    return render(request, 'exams/performance_dashboard.html', context)

@login_required
def exam_performance_analysis(request, exam_id):
    """Detailed performance analysis for a specific exam."""
    exam = get_object_or_404(Exam, id=exam_id)

    # Get all results for this exam
    results = EnhancedStudentExamResult.objects.filter(
        exam=exam,
        status='verified'
    ).select_related('student', 'timetable_entry__subject', 'timetable_entry__grade')

    # Calculate overall statistics
    total_students = results.count()
    if total_students > 0:
        avg_score = results.aggregate(avg=Avg('percentage_score'))['avg'] or 0
        highest_score = results.aggregate(max=Max('percentage_score'))['max'] or 0
        lowest_score = results.aggregate(min=Min('percentage_score'))['min'] or 0

        # Grade distribution
        grade_distribution = {}
        for result in results:
            grade = result.letter_grade or 'N/A'
            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1

        # Subject performance
        subject_performance = results.values('timetable_entry__subject__name').annotate(
            avg_score=Avg('percentage_score'),
            student_count=Count('id'),
            highest=Max('percentage_score'),
            lowest=Min('percentage_score')
        ).order_by('-avg_score')

        # Grade performance
        grade_performance = results.values('timetable_entry__grade__name').annotate(
            avg_score=Avg('percentage_score'),
            student_count=Count('id'),
            highest=Max('percentage_score'),
            lowest=Min('percentage_score')
        ).order_by('-avg_score')

        # Top performers
        top_performers = results.order_by('-percentage_score')[:10]

        # Students needing support
        struggling_students = results.filter(percentage_score__lt=40).order_by('percentage_score')[:10]

    else:
        avg_score = highest_score = lowest_score = 0
        grade_distribution = {}
        subject_performance = []
        grade_performance = []
        top_performers = []
        struggling_students = []

    context = {
        'exam': exam,
        'total_students': total_students,
        'avg_score': round(avg_score, 2),
        'highest_score': highest_score,
        'lowest_score': lowest_score,
        'grade_distribution': grade_distribution,
        'subject_performance': subject_performance,
        'grade_performance': grade_performance,
        'top_performers': top_performers,
        'struggling_students': struggling_students,
    }
    return render(request, 'exams/exam_performance_analysis.html', context)

@login_required
def subject_performance_analysis(request, subject_id):
    """Performance analysis for a specific subject across all exams."""
    subject = get_object_or_404(Subject, id=subject_id)

    # Get all results for this subject
    results = EnhancedStudentExamResult.objects.filter(
        timetable_entry__subject=subject,
        status='verified'
    ).select_related('exam', 'student', 'timetable_entry__grade').order_by('-exam__end_date')

    # Performance trends over time
    performance_trends = results.values('exam__name', 'exam__end_date').annotate(
        avg_score=Avg('percentage_score'),
        student_count=Count('id')
    ).order_by('exam__end_date')

    # Grade-wise performance
    grade_performance = results.values('timetable_entry__grade__name').annotate(
        avg_score=Avg('percentage_score'),
        student_count=Count('id'),
        exam_count=Count('exam', distinct=True)
    ).order_by('-avg_score')

    # Recent performance (last 3 exams)
    recent_exams = Exam.objects.filter(
        timetables__subject=subject,
        status__in=['completed', 'results_published']
    ).distinct().order_by('-end_date')[:3]

    recent_performance = []
    for exam in recent_exams:
        exam_results = results.filter(exam=exam)
        if exam_results.exists():
            recent_performance.append({
                'exam': exam,
                'avg_score': exam_results.aggregate(avg=Avg('percentage_score'))['avg'],
                'student_count': exam_results.count(),
                'highest': exam_results.aggregate(max=Max('percentage_score'))['max'],
                'lowest': exam_results.aggregate(min=Min('percentage_score'))['min']
            })

    context = {
        'subject': subject,
        'total_results': results.count(),
        'performance_trends': performance_trends,
        'grade_performance': grade_performance,
        'recent_performance': recent_performance,
    }
    return render(request, 'exams/subject_performance_analysis.html', context)

# Report Generation Views

@login_required
def generate_report_card(request, student_id, exam_id):
    """Generate individual student report card."""
    student = get_object_or_404(Student, id=student_id)
    exam = get_object_or_404(Exam, id=exam_id)

    # Get student results for this exam
    results = EnhancedStudentExamResult.objects.filter(
        student=student,
        exam=exam,
        status='verified'
    ).select_related('timetable_entry__subject').order_by('timetable_entry__subject__name')

    if not results.exists():
        messages.error(request, f'No verified results found for {student.full_name} in {exam.name}.')
        return redirect('exams:exam_detail', exam_id=exam.id)

    # Calculate overall performance
    total_marks = sum(result.marks_obtained for result in results)
    total_possible = sum(result.total_marks for result in results)
    overall_percentage = (total_marks / total_possible * 100) if total_possible > 0 else 0

    # Get or create report card
    report_card, created = ReportCard.objects.get_or_create(
        student=student,
        exam=exam,
        defaults={
            'total_marks': total_marks,
            'total_possible_marks': total_possible,
            'overall_percentage': overall_percentage,
            'overall_grade': _calculate_letter_grade(overall_percentage),
            'generated_by': request.user,
            'generated_at': timezone.now()
        }
    )

    if not created:
        # Update existing report card
        report_card.total_marks = total_marks
        report_card.total_possible_marks = total_possible
        report_card.overall_percentage = overall_percentage
        report_card.overall_grade = _calculate_letter_grade(overall_percentage)
        report_card.generated_by = request.user
        report_card.generated_at = timezone.now()
        report_card.save()

    # Get class ranking
    class_ranking = ClassRanking.objects.filter(
        exam=exam,
        grade=student.current_grade
    ).first()

    student_rank = None
    if class_ranking:
        student_ranking = StudentRanking.objects.filter(
            class_ranking=class_ranking,
            student=student
        ).first()
        if student_ranking:
            student_rank = student_ranking.position

    context = {
        'student': student,
        'exam': exam,
        'results': results,
        'report_card': report_card,
        'student_rank': student_rank,
        'class_ranking': class_ranking,
    }

    if request.GET.get('format') == 'pdf':
        # Generate PDF report card
        from django.template.loader import render_to_string
        from weasyprint import HTML

        html_string = render_to_string('exams/report_card_pdf.html', context)
        html = HTML(string=html_string)
        pdf = html.write_pdf()

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{student.full_name}_{exam.name}_report_card.pdf"'
        return response

    return render(request, 'exams/report_card.html', context)

@login_required
def class_report(request, exam_id, grade_id):
    """Generate class performance report."""
    exam = get_object_or_404(Exam, id=exam_id)
    grade = get_object_or_404(Grade, id=grade_id)

    # Get all results for this class
    results = EnhancedStudentExamResult.objects.filter(
        exam=exam,
        timetable_entry__grade=grade,
        status='verified'
    ).select_related('student', 'timetable_entry__subject').order_by('student__full_name')

    # Group results by student
    student_results = {}
    for result in results:
        if result.student.id not in student_results:
            student_results[result.student.id] = {
                'student': result.student,
                'subjects': [],
                'total_marks': 0,
                'total_possible': 0
            }

        student_results[result.student.id]['subjects'].append(result)
        student_results[result.student.id]['total_marks'] += result.marks_obtained
        student_results[result.student.id]['total_possible'] += result.total_marks

    # Calculate overall percentages and sort by performance
    for student_data in student_results.values():
        if student_data['total_possible'] > 0:
            student_data['percentage'] = (student_data['total_marks'] / student_data['total_possible']) * 100
        else:
            student_data['percentage'] = 0
        student_data['grade'] = _calculate_letter_grade(student_data['percentage'])

    # Sort by percentage (descending)
    sorted_students = sorted(student_results.values(), key=lambda x: x['percentage'], reverse=True)

    # Add rankings
    for i, student_data in enumerate(sorted_students):
        student_data['rank'] = i + 1

    # Calculate class statistics
    if sorted_students:
        class_avg = sum(s['percentage'] for s in sorted_students) / len(sorted_students)
        highest_score = sorted_students[0]['percentage']
        lowest_score = sorted_students[-1]['percentage']
    else:
        class_avg = highest_score = lowest_score = 0

    # Subject-wise analysis
    subjects = Subject.objects.filter(
        timetables__exam=exam,
        timetables__grade=grade
    ).distinct()

    subject_analysis = []
    for subject in subjects:
        subject_results = results.filter(timetable_entry__subject=subject)
        if subject_results.exists():
            subject_analysis.append({
                'subject': subject,
                'avg_score': subject_results.aggregate(avg=Avg('percentage_score'))['avg'],
                'highest': subject_results.aggregate(max=Max('percentage_score'))['max'],
                'lowest': subject_results.aggregate(min=Min('percentage_score'))['min'],
                'student_count': subject_results.count()
            })

    context = {
        'exam': exam,
        'grade': grade,
        'sorted_students': sorted_students,
        'class_avg': round(class_avg, 2),
        'highest_score': highest_score,
        'lowest_score': lowest_score,
        'total_students': len(sorted_students),
        'subject_analysis': subject_analysis,
    }

    if request.GET.get('format') == 'pdf':
        # Generate PDF class report
        from django.template.loader import render_to_string
        from weasyprint import HTML

        html_string = render_to_string('exams/class_report_pdf.html', context)
        html = HTML(string=html_string)
        pdf = html.write_pdf()

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{grade.name}_{exam.name}_class_report.pdf"'
        return response

    return render(request, 'exams/class_report.html', context)

def _calculate_letter_grade(percentage):
    """Helper function to calculate letter grade from percentage."""
    if percentage >= 80:
        return 'A'
    elif percentage >= 70:
        return 'B'
    elif percentage >= 60:
        return 'C'
    elif percentage >= 50:
        return 'D'
    else:
        return 'E'

@login_required
def ranking_dashboard(request):
    """Dashboard for class and student rankings."""
    # Get recent exams with rankings
    recent_exams = Exam.objects.filter(
        status__in=['completed', 'results_published']
    ).order_by('-end_date')[:10]

    # Get class rankings for recent exams
    class_rankings = ClassRanking.objects.filter(
        exam__in=recent_exams
    ).select_related('exam', 'grade').order_by('-exam__end_date', 'grade__name')

    context = {
        'recent_exams': recent_exams,
        'class_rankings': class_rankings,
    }
    return render(request, 'exams/ranking_dashboard.html', context)

@login_required
def generate_rankings(request, exam_id):
    """Generate rankings for an exam."""
    exam = get_object_or_404(Exam, id=exam_id)

    if request.method == 'POST':
        # Get all grades that have results for this exam
        grades_with_results = Grade.objects.filter(
            timetables__exam=exam,
            timetables__results__status='verified'
        ).distinct()

        rankings_created = 0

        for grade in grades_with_results:
            # Get all students with results for this grade and exam
            student_results = {}
            results = EnhancedStudentExamResult.objects.filter(
                exam=exam,
                timetable_entry__grade=grade,
                status='verified'
            ).select_related('student')

            # Calculate total marks for each student
            for result in results:
                if result.student.id not in student_results:
                    student_results[result.student.id] = {
                        'student': result.student,
                        'total_marks': 0,
                        'total_possible': 0,
                        'subject_count': 0
                    }

                student_results[result.student.id]['total_marks'] += result.marks_obtained
                student_results[result.student.id]['total_possible'] += result.total_marks
                student_results[result.student.id]['subject_count'] += 1

            # Calculate percentages and sort
            student_list = []
            for student_data in student_results.values():
                if student_data['total_possible'] > 0:
                    percentage = (student_data['total_marks'] / student_data['total_possible']) * 100
                else:
                    percentage = 0

                student_list.append({
                    'student': student_data['student'],
                    'total_marks': student_data['total_marks'],
                    'total_possible': student_data['total_possible'],
                    'percentage': percentage,
                    'subject_count': student_data['subject_count']
                })

            # Sort by percentage (descending)
            student_list.sort(key=lambda x: x['percentage'], reverse=True)

            if student_list:
                # Create or update class ranking
                class_ranking, created = ClassRanking.objects.get_or_create(
                    exam=exam,
                    grade=grade,
                    defaults={
                        'total_students': len(student_list),
                        'highest_score': student_list[0]['percentage'],
                        'lowest_score': student_list[-1]['percentage'],
                        'average_score': sum(s['percentage'] for s in student_list) / len(student_list),
                        'generated_by': request.user,
                        'generated_at': timezone.now()
                    }
                )

                if not created:
                    # Update existing ranking
                    class_ranking.total_students = len(student_list)
                    class_ranking.highest_score = student_list[0]['percentage']
                    class_ranking.lowest_score = student_list[-1]['percentage']
                    class_ranking.average_score = sum(s['percentage'] for s in student_list) / len(student_list)
                    class_ranking.generated_by = request.user
                    class_ranking.generated_at = timezone.now()
                    class_ranking.save()

                # Delete existing student rankings
                StudentRanking.objects.filter(class_ranking=class_ranking).delete()

                # Create student rankings
                for i, student_data in enumerate(student_list):
                    StudentRanking.objects.create(
                        class_ranking=class_ranking,
                        student=student_data['student'],
                        position=i + 1,
                        total_marks=student_data['total_marks'],
                        total_possible_marks=student_data['total_possible'],
                        percentage_score=student_data['percentage'],
                        subject_count=student_data['subject_count']
                    )

                rankings_created += 1

        # Create audit log
        ExamAuditLog.objects.create(
            exam=exam,
            action_type='ranking_generation',
            description=f'Rankings generated for {rankings_created} classes',
            new_values={'classes_ranked': rankings_created},
            user=request.user,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        messages.success(request, f'Rankings generated successfully for {rankings_created} classes.')
        return redirect('exams:ranking_dashboard')

    # Get grades that have results for this exam
    grades_with_results = Grade.objects.filter(
        timetables__exam=exam,
        timetables__results__status='verified'
    ).distinct()

    context = {
        'exam': exam,
        'grades_with_results': grades_with_results,
    }
    return render(request, 'exams/generate_rankings.html', context)
