from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg, Max, Min
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal
import json

from .models import (
    ExamType, Exam, GradingSystem, GradeScale, ExamTimetable,
    EnhancedStudentExamResult, StudentOverallPerformance, SubjectPerformanceAnalysis,
    ReportCard, PerformanceComparison, ClassRanking, StudentRanking,
    ExamRangeGrade, StudentExam, StudentOverallExamResult
)
from .forms import ExamTypeForm, ExamForm, ExamTimetableForm
from apps.students.models import Student
from apps.academics.models import Grade, Subject
from apps.teachers.models import Teacher

@login_required
def exam_dashboard(request):
    """Exam management dashboard with statistics and overview."""
    today = date.today()
    current_year = today.year

    # Calculate key exam metrics
    total_exams = Exam.objects.filter(academic_year=current_year).count()
    active_exams = Exam.objects.filter(
        status='active',
        start_date__lte=today,
        end_date__gte=today
    ).count()
    upcoming_exams = Exam.objects.filter(
        start_date__gt=today,
        academic_year=current_year
    ).count()
    completed_exams = Exam.objects.filter(
        status='completed',
        academic_year=current_year
    ).count()

    # Recent activities (avoiding ExamType table completely)
    try:
        recent_exams = Exam.objects.filter(
            academic_year=current_year
        ).order_by('-created_at')[:10]
    except Exception:
        recent_exams = []

    try:
        upcoming_exam_list = Exam.objects.filter(
            start_date__gt=today,
            academic_year=current_year
        ).order_by('start_date')[:5]
    except Exception:
        upcoming_exam_list = []

    # Performance statistics (with error handling for missing tables)
    try:
        total_results = EnhancedStudentExamResult.objects.filter(
            exam__academic_year=current_year,
            status='verified'
        ).count()

        average_performance = EnhancedStudentExamResult.objects.filter(
            exam__academic_year=current_year,
            status='verified'
        ).aggregate(avg_percentage=Avg('percentage'))['avg_percentage'] or 0
    except Exception as e:
        # Handle case where EnhancedStudentExamResult table doesn't exist
        total_results = 0
        average_performance = 0

    # Timetable statistics (with error handling for missing columns)
    try:
        total_timetable_entries = ExamTimetable.objects.filter(
            exam__academic_year=current_year
        ).count()

        published_timetables = ExamTimetable.objects.filter(
            exam__academic_year=current_year,
            is_published=True
        ).count()
    except Exception as e:
        # Handle case where ExamTimetable table or columns don't exist
        total_timetable_entries = 0
        published_timetables = 0

    context = {
        'total_exams': total_exams,
        'active_exams': active_exams,
        'upcoming_exams': upcoming_exams,
        'completed_exams': completed_exams,
        'recent_exams': recent_exams,
        'upcoming_exam_list': upcoming_exam_list,
        'total_results': total_results,
        'average_performance': round(average_performance, 2),
        'total_timetable_entries': total_timetable_entries,
        'published_timetables': published_timetables,
        'exam_types': 0,  # Will be updated below with error handling
        'grading_systems': 0,  # Will be updated below with error handling
    }

    # Add additional statistics with error handling
    try:
        context['exam_types'] = ExamType.objects.filter(is_active=True).count()
    except Exception:
        context['exam_types'] = 0

    try:
        context['grading_systems'] = GradingSystem.objects.filter(is_active=True).count()
    except Exception:
        context['grading_systems'] = 0

    return render(request, 'exams/dashboard.html', context)

@login_required
def exam_list(request):
    """List all exams with search and filter functionality."""
    # Try to get exams with exam_type, fallback to basic query if ExamType table doesn't exist
    try:
        exams = Exam.objects.select_related('exam_type').order_by('-academic_year', '-start_date')
        use_exam_type_search = True
    except Exception:
        exams = Exam.objects.order_by('-academic_year', '-start_date')
        use_exam_type_search = False

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        if use_exam_type_search:
            try:
                exams = exams.filter(
                    Q(name__icontains=search_query) |
                    Q(exam_code__icontains=search_query) |
                    Q(exam_type__name__icontains=search_query)
                )
            except Exception:
                # Fallback to basic search without exam_type
                exams = exams.filter(
                    Q(name__icontains=search_query) |
                    Q(exam_code__icontains=search_query)
                )
        else:
            exams = exams.filter(
                Q(name__icontains=search_query) |
                Q(exam_code__icontains=search_query)
            )

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        exams = exams.filter(status=status_filter)

    # Filter by academic year
    year_filter = request.GET.get('year')
    if year_filter:
        exams = exams.filter(academic_year=year_filter)

    # Filter by exam type (only if ExamType table exists)
    type_filter = request.GET.get('type')
    if type_filter and use_exam_type_search:
        try:
            exams = exams.filter(exam_type_id=type_filter)
        except Exception:
            # Ignore exam type filter if table doesn't exist
            pass

    paginator = Paginator(exams, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get exam types with error handling
    try:
        exam_types = ExamType.objects.filter(is_active=True)
    except Exception:
        exam_types = []

    context = {
        'page_obj': page_obj,
        'exams': page_obj,
        'exam_types': exam_types,
        'years': range(date.today().year - 2, date.today().year + 3),
        'status_choices': Exam.STATUS_CHOICES,
        'search_query': search_query,
        'status_filter': status_filter,
        'year_filter': year_filter,
        'type_filter': type_filter,
    }
    return render(request, 'exams/exam_list.html', context)

@login_required
def add_exam(request):
    """Add a new exam."""
    # Check if ExamType table exists
    try:
        from .models import ExamType
        ExamType.objects.exists()
        exam_type_available = True
    except Exception:
        exam_type_available = False

    if not exam_type_available:
        # If ExamType table doesn't exist, show a message and redirect
        messages.error(request, 'Exam types are not available yet. Please contact the administrator to set up exam types first.')
        return redirect('exams:exam_list')

    if request.method == 'POST':
        try:
            form = ExamForm(request.POST)
            if form.is_valid():
                exam = form.save(commit=False)
                exam.created_by = request.user
                exam.save()
                form.save_m2m()  # Save many-to-many relationships
                messages.success(request, f'Exam "{exam.name}" has been created successfully.')
                return redirect('exams:exam_detail', exam_id=exam.id)
        except Exception as e:
            messages.error(request, f'Error creating exam: {str(e)}')
            form = ExamForm()
    else:
        try:
            form = ExamForm()
        except Exception as e:
            messages.error(request, f'Error loading form: {str(e)}')
            return redirect('exams:exam_list')

    context = {'form': form}
    return render(request, 'exams/add_exam.html', context)
