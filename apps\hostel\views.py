from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg, F
from django.db import models
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
import csv

from .models import (
    Hostel, HostelRoom, HostelAllocation, HostelBed, MealPlan,
    HostelWarden, BoardingFeeStructure, BoardingFeePayment,
    BoardingFeeDiscount, HostelFacility, BoardingStudentStatus
)
from .forms import (
    HostelForm, HostelRoomForm, HostelAllocationForm, HostelBedForm,
    MealPlanForm, BoardingFeePaymentForm, BoardingFeeDiscountForm,
    HostelSearchForm
)
from apps.students.models import Student

@login_required
def hostel_dashboard(request):
    """Hostel management dashboard with statistics and recent activities."""
    context = {
        'total_hostels': Hostel.objects.count(),
        'total_rooms': HostelRoom.objects.count(),
        'total_beds': HostelBed.objects.count(),
        'occupied_beds': HostelAllocation.objects.filter(status='active').count(),
        'available_beds': HostelBed.objects.filter(status='available').count(),
        'total_boarding_students': HostelAllocation.objects.filter(status='active').count(),
        'total_revenue': HostelAllocation.objects.filter(status='active').aggregate(
            total=Sum('fee')
        )['total'] or 0,
        'recent_allocations': HostelAllocation.objects.select_related(
            'student', 'hostel', 'room'
        ).order_by('-allocation_date')[:10],
        'hostel_occupancy': Hostel.objects.annotate(
            current_occupancy=Count('hostelallocation', filter=Q(hostelallocation__status='active')),
            occupancy_rate=Count('hostelallocation', filter=Q(hostelallocation__status='active')) * 100.0 / F('capacity')
        ).order_by('-occupancy_rate')[:5],
        'meal_plans': MealPlan.objects.filter(is_active=True).order_by('name')[:5],
    }
    return render(request, 'hostel/dashboard.html', context)

# Hostel Management Views
@login_required
def hostel_list(request):
    """List all hostels with occupancy information."""
    hostels = Hostel.objects.annotate(
        current_occupancy=Count('hostelallocation', filter=Q(hostelallocation__status='active')),
        room_count=Count('hostelroom'),
        available_capacity=F('capacity') - Count('hostelallocation', filter=Q(hostelallocation__status='active'))
    ).order_by('name')

    # Calculate occupancy percentage for each hostel
    for hostel in hostels:
        if hostel.capacity > 0:
            hostel.occupancy_percentage = (hostel.current_occupancy / hostel.capacity) * 100
        else:
            hostel.occupancy_percentage = 0

    paginator = Paginator(hostels, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'hostels': page_obj,
    }
    return render(request, 'hostel/hostel_list.html', context)

@login_required
def hostel_detail(request, hostel_id):
    """Display detailed information about a hostel."""
    hostel = get_object_or_404(Hostel, id=hostel_id)
    rooms = HostelRoom.objects.filter(hostel=hostel).annotate(
        current_occupancy=Count('hostelallocation', filter=Q(hostelallocation__status='active'))
    ).order_by('room_number')

    allocations = HostelAllocation.objects.filter(
        hostel=hostel, status='active'
    ).select_related('student', 'room')

    facilities = HostelFacility.objects.filter(hostel=hostel)

    context = {
        'hostel': hostel,
        'rooms': rooms,
        'allocations': allocations,
        'facilities': facilities,
        'total_students': allocations.count(),
        'occupancy_rate': (allocations.count() / hostel.capacity * 100) if hostel.capacity > 0 else 0,
    }
    return render(request, 'hostel/hostel_detail.html', context)

@login_required
def add_hostel(request):
    """Add a new hostel."""
    if request.method == 'POST':
        form = HostelForm(request.POST)
        if form.is_valid():
            hostel = form.save()
            messages.success(request, f'Hostel "{hostel.name}" has been added successfully.')
            return redirect('hostel:hostel_detail', hostel_id=hostel.id)
    else:
        form = HostelForm()

    context = {'form': form}
    return render(request, 'hostel/add_hostel.html', context)

@login_required
def edit_hostel(request, hostel_id):
    """Edit an existing hostel."""
    hostel = get_object_or_404(Hostel, id=hostel_id)

    if request.method == 'POST':
        form = HostelForm(request.POST, instance=hostel)
        if form.is_valid():
            form.save()
            messages.success(request, f'Hostel "{hostel.name}" has been updated successfully.')
            return redirect('hostel:hostel_detail', hostel_id=hostel.id)
    else:
        form = HostelForm(instance=hostel)

    context = {'form': form, 'hostel': hostel}
    return render(request, 'hostel/edit_hostel.html', context)

# Room Management Views
@login_required
def room_list(request, hostel_id):
    """List all rooms for a specific hostel."""
    hostel = get_object_or_404(Hostel, id=hostel_id)
    rooms = HostelRoom.objects.filter(hostel=hostel).annotate(
        current_occupancy=Count('hostelallocation', filter=Q(hostelallocation__status='active'))
    ).order_by('room_number')

    context = {
        'hostel': hostel,
        'rooms': rooms,
    }
    return render(request, 'hostel/room_list.html', context)

@login_required
def room_detail(request, room_id):
    """Display detailed information about a room."""
    room = get_object_or_404(HostelRoom, id=room_id)
    allocations = HostelAllocation.objects.filter(
        room=room, status='active'
    ).select_related('student')
    beds = HostelBed.objects.filter(room=room).order_by('bed_number')

    context = {
        'room': room,
        'allocations': allocations,
        'beds': beds,
    }
    return render(request, 'hostel/room_detail.html', context)

@login_required
def add_room(request, hostel_id):
    """Add a new room to a hostel."""
    hostel = get_object_or_404(Hostel, id=hostel_id)

    if request.method == 'POST':
        form = HostelRoomForm(request.POST)
        if form.is_valid():
            room = form.save(commit=False)
            room.hostel = hostel
            room.save()

            # Auto-create beds for the room
            for bed_num in range(1, room.capacity + 1):
                HostelBed.objects.create(
                    room=room,
                    bed_number=str(bed_num),
                    status='available'
                )

            messages.success(request, f'Room {room.room_number} has been added with {room.capacity} beds.')
            return redirect('hostel:room_list', hostel_id=hostel.id)
    else:
        form = HostelRoomForm(initial={'hostel': hostel})

    context = {'form': form, 'hostel': hostel}
    return render(request, 'hostel/add_room.html', context)

# Student Allocation Views
@login_required
def allocation_list(request):
    """List all hostel allocations with search and filter functionality."""
    form = HostelSearchForm(request.GET)
    allocations = HostelAllocation.objects.select_related(
        'student', 'hostel', 'room'
    ).all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        hostel = form.cleaned_data.get('hostel')
        status = form.cleaned_data.get('status')

        if search_query:
            allocations = allocations.filter(
                Q(student__full_name__icontains=search_query) |
                Q(student__index_number__icontains=search_query) |
                Q(hostel__name__icontains=search_query) |
                Q(room__room_number__icontains=search_query)
            )

        if hostel:
            allocations = allocations.filter(hostel=hostel)

        if status:
            allocations = allocations.filter(status=status)

    allocations = allocations.order_by('-allocation_date')

    paginator = Paginator(allocations, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'allocations': page_obj,
    }
    return render(request, 'hostel/allocation_list.html', context)

@login_required
def allocation_detail(request, allocation_id):
    """Display detailed information about a hostel allocation."""
    allocation = get_object_or_404(HostelAllocation, id=allocation_id)
    payments = BoardingFeePayment.objects.filter(allocation=allocation).order_by('-payment_date')
    discounts = BoardingFeeDiscount.objects.filter(allocation=allocation, is_active=True)

    context = {
        'allocation': allocation,
        'payments': payments,
        'discounts': discounts,
    }
    return render(request, 'hostel/allocation_detail.html', context)

@login_required
def add_allocation(request):
    """Allocate a student to hostel accommodation."""
    if request.method == 'POST':
        form = HostelAllocationForm(request.POST)
        if form.is_valid():
            student = form.cleaned_data['student_index']
            allocation = form.save(commit=False)
            allocation.student = student
            allocation.allocated_by = request.user.id
            allocation.save()

            # Update room occupancy
            room = allocation.room
            room.current_occupancy = HostelAllocation.objects.filter(
                room=room, status='active'
            ).count()
            if room.current_occupancy >= room.capacity:
                room.status = 'full'
            room.save()

            # Update bed status if bed number is specified
            if allocation.bed_number:
                try:
                    bed = HostelBed.objects.get(
                        room=room,
                        bed_number=str(allocation.bed_number)
                    )
                    bed.status = 'occupied'
                    bed.current_occupant = student
                    bed.save()
                except HostelBed.DoesNotExist:
                    pass

            messages.success(request, f'Hostel allocation created for {student.full_name}.')
            return redirect('hostel:allocation_detail', allocation_id=allocation.id)
    else:
        form = HostelAllocationForm()

    context = {'form': form}
    return render(request, 'hostel/add_allocation.html', context)

@login_required
def edit_allocation(request, allocation_id):
    """Edit an existing hostel allocation."""
    allocation = get_object_or_404(HostelAllocation, id=allocation_id)

    if request.method == 'POST':
        form = HostelAllocationForm(request.POST, instance=allocation)
        if form.is_valid():
            student = form.cleaned_data['student_index']
            allocation = form.save(commit=False)
            allocation.student = student
            allocation.save()

            messages.success(request, f'Hostel allocation updated for {student.full_name}.')
            return redirect('hostel:allocation_detail', allocation_id=allocation.id)
    else:
        form = HostelAllocationForm(instance=allocation)
        form.fields['student_index'].initial = allocation.student.index_number

    context = {'form': form, 'allocation': allocation}
    return render(request, 'hostel/edit_allocation.html', context)

@login_required
def release_allocation(request, allocation_id):
    """Release a student from hostel accommodation."""
    allocation = get_object_or_404(HostelAllocation, id=allocation_id)

    if request.method == 'POST':
        allocation.status = 'inactive'
        allocation.release_date = date.today()
        allocation.save()

        # Update room occupancy
        room = allocation.room
        room.current_occupancy = HostelAllocation.objects.filter(
            room=room, status='active'
        ).count()
        if room.current_occupancy < room.capacity:
            room.status = 'available'
        room.save()

        # Update bed status
        if allocation.bed_number:
            try:
                bed = HostelBed.objects.get(
                    room=room,
                    bed_number=str(allocation.bed_number)
                )
                bed.status = 'available'
                bed.current_occupant = None
                bed.save()
            except HostelBed.DoesNotExist:
                pass

        messages.success(request, f'Hostel allocation released for {allocation.student.full_name}.')
        return redirect('hostel:allocation_list')

    context = {'allocation': allocation}
    return render(request, 'hostel/release_allocation.html', context)

@login_required
def hostel_reports(request):
    """Generate hostel reports and analytics."""
    # Basic statistics for now
    total_hostels = Hostel.objects.count()
    total_capacity = Hostel.objects.aggregate(
        total=models.Sum('capacity')
    )['total'] or 0

    total_occupancy = HostelAllocation.objects.filter(
        status='active'
    ).count()

    occupancy_rate = (total_occupancy / total_capacity * 100) if total_capacity > 0 else 0

    # Hostel-wise statistics
    hostel_stats = Hostel.objects.annotate(
        current_occupancy=Count('hostelallocation', filter=Q(hostelallocation__status='active')),
        room_count=Count('hostelroom')
    ).order_by('name')

    # Calculate occupancy percentage for each hostel
    for hostel in hostel_stats:
        if hostel.capacity > 0:
            hostel.occupancy_percentage = (hostel.current_occupancy / hostel.capacity) * 100
        else:
            hostel.occupancy_percentage = 0

    context = {
        'total_hostels': total_hostels,
        'total_capacity': total_capacity,
        'total_occupancy': total_occupancy,
        'occupancy_rate': occupancy_rate,
        'hostel_stats': hostel_stats,
    }
    return render(request, 'hostel/reports.html', context)
