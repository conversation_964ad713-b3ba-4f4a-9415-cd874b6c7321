from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import date, timedelta
import csv

from .models import LibraryBook, LibraryCategory, LibraryBorrowing
from .forms import (
    LibraryBookForm, LibraryCategoryForm, LibraryBorrowingForm,
    BookReturnForm, BookSearchForm
)
from apps.students.models import Student

@login_required
def library_dashboard(request):
    """Library dashboard with statistics and recent activities."""
    context = {
        'total_books': LibraryBook.objects.count(),
        'available_books': LibraryBook.objects.filter(available_copies__gt=0).count(),
        'borrowed_books': LibraryBorrowing.objects.filter(status='borrowed').count(),
        'overdue_books': LibraryBorrowing.objects.filter(
            status='borrowed',
            due_date__lt=date.today()
        ).count(),
        'total_categories': LibraryCategory.objects.count(),
        'recent_borrowings': LibraryBorrowing.objects.select_related(
            'student', 'book'
        ).order_by('-issue_date')[:10],
        'overdue_borrowings': LibraryBorrowing.objects.filter(
            status='borrowed',
            due_date__lt=date.today()
        ).select_related('student', 'book')[:10],
    }
    return render(request, 'library/dashboard.html', context)

@login_required
def book_list(request):
    """List all books with search and filter functionality."""
    form = BookSearchForm(request.GET)
    books = LibraryBook.objects.select_related('category').all()

    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        category = form.cleaned_data.get('category')
        status = form.cleaned_data.get('status')

        if search_query:
            books = books.filter(
                Q(title__icontains=search_query) |
                Q(author__icontains=search_query) |
                Q(isbn__icontains=search_query)
            )

        if category:
            books = books.filter(category=category)

        if status:
            books = books.filter(status=status)

    paginator = Paginator(books, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'books': page_obj,
    }
    return render(request, 'library/book_list.html', context)

@login_required
def book_detail(request, book_id):
    """Display detailed information about a book."""
    book = get_object_or_404(LibraryBook, id=book_id)
    recent_borrowings = LibraryBorrowing.objects.filter(
        book=book
    ).select_related('student').order_by('-issue_date')[:10]

    context = {
        'book': book,
        'recent_borrowings': recent_borrowings,
    }
    return render(request, 'library/book_detail.html', context)

@login_required
def add_book(request):
    """Add a new book to the library."""
    if request.method == 'POST':
        form = LibraryBookForm(request.POST)
        if form.is_valid():
            book = form.save(commit=False)
            book.date_added = date.today()
            book.added_by = request.user.id
            book.save()
            messages.success(request, f'Book "{book.title}" has been added successfully.')
            return redirect('library:book_detail', book_id=book.id)
    else:
        form = LibraryBookForm()

    context = {'form': form}
    return render(request, 'library/add_book.html', context)

@login_required
def edit_book(request, book_id):
    """Edit an existing book."""
    book = get_object_or_404(LibraryBook, id=book_id)

    if request.method == 'POST':
        form = LibraryBookForm(request.POST, instance=book)
        if form.is_valid():
            form.save()
            messages.success(request, f'Book "{book.title}" has been updated successfully.')
            return redirect('library:book_detail', book_id=book.id)
    else:
        form = LibraryBookForm(instance=book)

    context = {'form': form, 'book': book}
    return render(request, 'library/edit_book.html', context)

@login_required
def delete_book(request, book_id):
    """Delete a book from the library."""
    book = get_object_or_404(LibraryBook, id=book_id)
    if request.method == 'POST':
        book_title = book.title
        book.delete()
        messages.success(request, f'Book "{book_title}" has been deleted successfully.')
        return redirect('library:book_list')
    return redirect('library:book_detail', book_id=book.id)

@login_required
def category_list(request):
    """List all library categories."""
    categories = LibraryCategory.objects.all().order_by('name')
    context = {'categories': categories}
    return render(request, 'library/category_list.html', context)

@login_required
def add_category(request):
    """Add a new library category."""
    if request.method == 'POST':
        form = LibraryCategoryForm(request.POST)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Category "{category.name}" has been added successfully.')
            return redirect('library:category_list')
    else:
        form = LibraryCategoryForm()

    context = {'form': form}
    return render(request, 'library/add_category.html', context)


@login_required
def edit_category(request, category_id):
    """Edit an existing library category."""
    category = get_object_or_404(LibraryCategory, id=category_id)
    
    if request.method == 'POST':
        form = LibraryCategoryForm(request.POST, instance=category)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'Category "{category.name}" has been updated successfully.')
            return redirect('library:category_list')
    else:
        form = LibraryCategoryForm(instance=category)
    
    context = {'form': form, 'category': category}
    return render(request, 'library/edit_category.html', context)


@login_required
def delete_category(request, category_id):
    """Delete a library category."""
    category = get_object_or_404(LibraryCategory, id=category_id)
    
    if request.method == 'POST':
        category_name = category.name
        category.delete()
        messages.success(request, f'Category "{category_name}" has been deleted successfully.')
        return redirect('library:category_list')
    
    # If not a POST request, redirect to category list
    return redirect('library:category_list')

@login_required
def borrow_book(request):
    """Borrow a book to a student."""
    if request.method == 'POST':
        form = LibraryBorrowingForm(request.POST)
        if form.is_valid():
            student = form.cleaned_data['student_index']
            book = form.cleaned_data['book']

            # Check if student has overdue books
            overdue_books = LibraryBorrowing.objects.filter(
                student=student,
                status='borrowed',
                due_date__lt=date.today()
            ).count()

            if overdue_books > 0:
                messages.error(request, f'Student has {overdue_books} overdue book(s). Please return them first.')
                return render(request, 'library/borrow_book.html', {'form': form})

            # Check borrowing limit (max 3 books)
            current_borrowings = LibraryBorrowing.objects.filter(
                student=student,
                status='borrowed'
            ).count()

            if current_borrowings >= 3:
                messages.error(request, 'Student has reached the maximum borrowing limit (3 books).')
                return render(request, 'library/borrow_book.html', {'form': form})

            # Create borrowing record
            borrowing = LibraryBorrowing.objects.create(
                student=student,
                book=book,
                issue_date=date.today(),
                due_date=form.cleaned_data['due_date'],
                notes=form.cleaned_data['notes'],
                issued_by=request.user.id,
                status='borrowed'
            )

            # Update book availability
            book.available_copies -= 1
            book.save()

            messages.success(request, f'Book "{book.title}" has been borrowed to {student.full_name}.')
            return redirect('library:borrowing_detail', borrowing_id=borrowing.id)
    else:
        form = LibraryBorrowingForm()

    context = {'form': form}
    return render(request, 'library/borrow_book.html', context)

@login_required
def return_book(request, borrowing_id):
    """Return a borrowed book."""
    borrowing = get_object_or_404(LibraryBorrowing, id=borrowing_id, status='borrowed')

    if request.method == 'POST':
        form = BookReturnForm(request.POST)
        if form.is_valid():
            condition = form.cleaned_data['return_condition']
            notes = form.cleaned_data['notes']

            # Update borrowing record
            borrowing.return_date = date.today()
            borrowing.status = 'returned'
            borrowing.notes = f"{borrowing.notes}\nReturn: {notes}" if borrowing.notes else f"Return: {notes}"
            borrowing.returned_to = request.user.id

            # Calculate fine for overdue books
            if borrowing.due_date < date.today():
                days_overdue = (date.today() - borrowing.due_date).days
                fine_per_day = 10  # 10 KES per day
                borrowing.fine_amount = days_overdue * fine_per_day
                borrowing.status = 'overdue'

            borrowing.save()

            # Update book availability and status
            book = borrowing.book
            if condition == 'good':
                book.available_copies += 1
            elif condition == 'damaged':
                book.status = 'damaged'
                messages.warning(request, f'Book marked as damaged. Please review.')
            elif condition == 'lost':
                book.status = 'lost'
                borrowing.fine_amount = book.total_copies * 500  # 500 KES replacement cost
                borrowing.save()
                messages.warning(request, f'Book marked as lost. Fine of KES {borrowing.fine_amount} applied.')

            book.save()

            messages.success(request, f'Book "{book.title}" has been returned successfully.')
            return redirect('library:borrowing_detail', borrowing_id=borrowing.id)
    else:
        form = BookReturnForm(initial={'borrowing_id': borrowing_id})

    context = {'form': form, 'borrowing': borrowing}
    return render(request, 'library/return_book.html', context)

@login_required
def borrowing_list(request):
    """List all borrowing records with filters."""
    borrowings = LibraryBorrowing.objects.select_related('student', 'book').all()

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        borrowings = borrowings.filter(status=status_filter)

    # Filter by overdue
    if request.GET.get('overdue') == 'true':
        borrowings = borrowings.filter(status='borrowed', due_date__lt=date.today())

    # Search by student name or book title
    search = request.GET.get('search')
    if search:
        borrowings = borrowings.filter(
            Q(student__full_name__icontains=search) |
            Q(book__title__icontains=search) |
            Q(student__index_number__icontains=search)
        )

    borrowings = borrowings.order_by('-issue_date')

    paginator = Paginator(borrowings, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'borrowings': page_obj,
        'status_filter': status_filter,
        'search': search,
    }
    return render(request, 'library/borrowing_list.html', context)

@login_required
def borrowing_detail(request, borrowing_id):
    """Display detailed information about a borrowing record."""
    borrowing = get_object_or_404(LibraryBorrowing, id=borrowing_id)

    context = {'borrowing': borrowing}
    return render(request, 'library/borrowing_detail.html', context)

@login_required
def student_borrowing_history(request, student_id):
    """Display borrowing history for a specific student."""
    student = get_object_or_404(Student, id=student_id)
    borrowings = LibraryBorrowing.objects.filter(
        student=student
    ).select_related('book').order_by('-issue_date')

    paginator = Paginator(borrowings, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'student': student,
        'page_obj': page_obj,
        'borrowings': page_obj,
    }
    return render(request, 'library/student_borrowing_history.html', context)

@login_required
def overdue_books(request):
    """List all overdue books."""
    overdue_borrowings = LibraryBorrowing.objects.filter(
        status='borrowed',
        due_date__lt=date.today()
    ).select_related('student', 'book').order_by('due_date')

    context = {'overdue_borrowings': overdue_borrowings}
    return render(request, 'library/overdue_books.html', context)

@login_required
def export_borrowings_csv(request):
    """Export borrowing records to CSV."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="library_borrowings.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'ID', 'Student Name', 'Index Number', 'Book Title', 'Author',
        'Issue Date', 'Due Date', 'Return Date', 'Status', 'Fine Amount'
    ])

    borrowings = LibraryBorrowing.objects.select_related('student', 'book').all()
    for borrowing in borrowings:
        writer.writerow([
            borrowing.id,
            borrowing.student.full_name,
            borrowing.student.index_number,
            borrowing.book.title,
            borrowing.book.author,
            borrowing.issue_date,
            borrowing.due_date,
            borrowing.return_date or '',
            borrowing.status,
            borrowing.fine_amount or 0,
        ])

    return response

@login_required
def library_statistics(request):
    """Display library statistics and reports."""
    from django.db.models import Count, Sum

    # Basic statistics
    total_books = LibraryBook.objects.count()
    available_books = LibraryBook.objects.filter(available_copies__gt=0).count()
    borrowed_books = LibraryBorrowing.objects.filter(status='borrowed').count()
    overdue_books = LibraryBorrowing.objects.filter(
        status='borrowed', due_date__lt=date.today()
    ).count()

    # Category statistics
    category_stats = LibraryCategory.objects.annotate(
        book_count=Count('librarybook')
    ).order_by('-book_count')

    # Monthly borrowing statistics
    from django.db.models.functions import TruncMonth
    monthly_stats = LibraryBorrowing.objects.annotate(
        month=TruncMonth('issue_date')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('-month')[:12]

    # Top borrowed books
    top_books = LibraryBook.objects.annotate(
        borrow_count=Count('libraryborrowing')
    ).order_by('-borrow_count')[:10]

    # Fine statistics
    total_fines = LibraryBorrowing.objects.aggregate(
        total=Sum('fine_amount')
    )['total'] or 0

    context = {
        'total_books': total_books,
        'available_books': available_books,
        'borrowed_books': borrowed_books,
        'overdue_books': overdue_books,
        'category_stats': category_stats,
        'monthly_stats': monthly_stats,
        'top_books': top_books,
        'total_fines': total_fines,
    }
    return render(request, 'library/statistics.html', context)
