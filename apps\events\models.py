from django.db import models

# Create your models here.

class Event(models.Model):
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    note = models.CharField(max_length=255)
    color = models.CharField(max_length=255)
    category = models.ForeignKey('EventCategory', on_delete=models.CASCADE)
    grade_id = models.CharField(max_length=255)
    create_by = models.BigIntegerField()
    creator_type = models.CharField(max_length=255)
    start_date_time = models.DateTimeField()
    end_date_time = models.DateTimeField()
    location = models.CharField(max_length=255, null=True, blank=True)
    is_all_day = models.BooleanField(default=False)
    notification_sent = models.BooleanField(default=False)
    year = models.IntegerField()
    month = models.IntegerField()

    def __str__(self):
        return self.title

class EventCategory(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    color = models.CharField(max_length=7, default='#3788d8')

    def __str__(self):
        return self.name

class EventCategoryType(models.Model):
    id = models.AutoField(primary_key=True)
    category = models.ForeignKey(EventCategory, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)

    def __str__(self):
        return self.name
