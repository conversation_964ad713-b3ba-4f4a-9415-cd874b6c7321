from django.contrib import admin
from .models import TeacherSalary, TeacherSalaryHistory

@admin.register(TeacherSalary)
class TeacherSalaryAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'month', 'year', 'date', 'basic_salary', 'allowances', 'deductions', 'paid', 'payment_method', 'bank_name', 'account_number', 'transaction_id', '_status')
    search_fields = ('index_number', 'month', 'year', 'transaction_id', 'bank_name', 'account_number')
    list_filter = ('payment_method', 'year', 'month', '_status')
    ordering = ('-year', '-month', 'index_number')

@admin.register(TeacherSalaryHistory)
class TeacherSalaryHistoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'index_number', 'grade_id', 'subject_id', 'subject_fee', 'student_count', 'hall_charge', 'subtotal', 'paid', '_status', 'month', 'year', 'date', 'invoice_number', 'payment_method', 'transaction_id')
    search_fields = ('index_number', 'month', 'year', 'invoice_number', 'transaction_id')
    list_filter = ('payment_method', 'year', 'month', '_status')
    ordering = ('-year', '-month', 'index_number')
