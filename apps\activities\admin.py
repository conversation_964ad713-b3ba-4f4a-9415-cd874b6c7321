from django.contrib import admin
from .models import CoCurricularActivity, StudentActivity

@admin.register(CoCurricularActivity)
class CoCurricularActivityAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'category', 'description', 'teacher_in_charge', 'meeting_day', 'meeting_time', 'venue', 'fee', 'status')
    search_fields = ('name', 'category', 'teacher_in_charge__full_name', 'venue')
    list_filter = ('category', 'status', 'meeting_day', 'teacher_in_charge')
    ordering = ('name',)

@admin.register(StudentActivity)
class StudentActivityAdmin(admin.ModelAdmin):
    list_display = ('id', 'student', 'activity', 'join_date', 'position', 'achievements', 'status')
    search_fields = ('student__full_name', 'activity__name', 'position')
    list_filter = ('activity', 'status', 'join_date')
    ordering = ('-join_date', 'student')
