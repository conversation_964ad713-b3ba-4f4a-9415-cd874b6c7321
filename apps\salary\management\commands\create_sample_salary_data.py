from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from decimal import Decimal
from datetime import date, timedelta
import random

from apps.salary.models import (
    SalaryGrade, SalaryComponent, EmployeeSalaryStructure, 
    EmployeeSalaryComponent, PayrollPeriod, EnhancedPayroll
)
from apps.teachers.models import Teacher


class Command(BaseCommand):
    help = 'Create sample salary data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing salary data before creating new data',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing salary data...')
            EnhancedPayroll.objects.all().delete()
            PayrollPeriod.objects.all().delete()
            EmployeeSalaryComponent.objects.all().delete()
            EmployeeSalaryStructure.objects.all().delete()
            SalaryComponent.objects.all().delete()
            SalaryGrade.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Cleared existing data.'))

        # Create salary grades
        self.stdout.write('Creating salary grades...')
        grades_data = [
            {
                'grade_name': 'Junior Teacher',
                'grade_type': 'teaching',
                'minimum_salary': Decimal('3000.00'),
                'maximum_salary': Decimal('4500.00'),
                'annual_increment': Decimal('200.00'),
                'description': 'Entry level teaching position'
            },
            {
                'grade_name': 'Senior Teacher',
                'grade_type': 'teaching',
                'minimum_salary': Decimal('4500.00'),
                'maximum_salary': Decimal('6500.00'),
                'annual_increment': Decimal('300.00'),
                'description': 'Experienced teaching position'
            },
            {
                'grade_name': 'Head of Department',
                'grade_type': 'teaching',
                'minimum_salary': Decimal('6000.00'),
                'maximum_salary': Decimal('8000.00'),
                'annual_increment': Decimal('400.00'),
                'description': 'Department leadership position'
            },
            {
                'grade_name': 'Administrative Officer',
                'grade_type': 'administrative',
                'minimum_salary': Decimal('2500.00'),
                'maximum_salary': Decimal('4000.00'),
                'annual_increment': Decimal('150.00'),
                'description': 'Administrative support position'
            },
            {
                'grade_name': 'Support Staff',
                'grade_type': 'support',
                'minimum_salary': Decimal('1800.00'),
                'maximum_salary': Decimal('2800.00'),
                'annual_increment': Decimal('100.00'),
                'description': 'General support staff'
            },
        ]

        for grade_data in grades_data:
            grade, created = SalaryGrade.objects.get_or_create(
                grade_name=grade_data['grade_name'],
                defaults=grade_data
            )
            if created:
                self.stdout.write(f'Created grade: {grade.grade_name}')

        # Create salary components
        self.stdout.write('Creating salary components...')
        components_data = [
            {
                'name': 'Housing Allowance',
                'component_type': 'allowance',
                'calculation_method': 'percentage',
                'percentage_rate': Decimal('20.00'),
                'is_mandatory': True,
                'is_taxable': True,
                'description': 'Monthly housing allowance'
            },
            {
                'name': 'Transport Allowance',
                'component_type': 'allowance',
                'calculation_method': 'fixed',
                'fixed_amount': Decimal('300.00'),
                'is_mandatory': True,
                'is_taxable': True,
                'description': 'Monthly transport allowance'
            },
            {
                'name': 'Medical Allowance',
                'component_type': 'allowance',
                'calculation_method': 'fixed',
                'fixed_amount': Decimal('200.00'),
                'is_mandatory': False,
                'is_taxable': False,
                'description': 'Monthly medical allowance'
            },
            {
                'name': 'Income Tax',
                'component_type': 'deduction',
                'calculation_method': 'percentage',
                'percentage_rate': Decimal('15.00'),
                'is_mandatory': True,
                'is_taxable': False,
                'description': 'Monthly income tax deduction'
            },
            {
                'name': 'Pension Contribution',
                'component_type': 'deduction',
                'calculation_method': 'percentage',
                'percentage_rate': Decimal('5.00'),
                'is_mandatory': True,
                'is_taxable': False,
                'description': 'Monthly pension contribution'
            },
            {
                'name': 'Performance Bonus',
                'component_type': 'bonus',
                'calculation_method': 'percentage',
                'percentage_rate': Decimal('10.00'),
                'is_mandatory': False,
                'is_taxable': True,
                'description': 'Quarterly performance bonus'
            },
        ]

        for comp_data in components_data:
            component, created = SalaryComponent.objects.get_or_create(
                name=comp_data['name'],
                defaults=comp_data
            )
            if created:
                self.stdout.write(f'Created component: {component.name}')

        # Create payroll periods
        self.stdout.write('Creating payroll periods...')
        today = date.today()
        
        # Create last 3 months
        for i in range(3):
            month_start = date(today.year, today.month - i, 1)
            if month_start.month == 12:
                month_end = date(month_start.year, 12, 31)
            else:
                month_end = date(month_start.year, month_start.month + 1, 1) - timedelta(days=1)
            
            pay_date = month_end + timedelta(days=5)
            
            period_name = f"{month_start.strftime('%B %Y')}"
            
            period, created = PayrollPeriod.objects.get_or_create(
                period_name=period_name,
                defaults={
                    'period_type': 'monthly',
                    'start_date': month_start,
                    'end_date': month_end,
                    'pay_date': pay_date,
                    'status': 'paid' if i > 0 else 'approved',
                    'notes': f'Monthly payroll for {period_name}'
                }
            )
            if created:
                self.stdout.write(f'Created period: {period.period_name}')

        # Create employee salary structures for existing teachers
        self.stdout.write('Creating employee salary structures...')
        teachers = Teacher.objects.filter(status='active')[:10]  # Limit to 10 teachers
        grades = list(SalaryGrade.objects.all())
        
        if not teachers.exists():
            self.stdout.write(self.style.WARNING('No active teachers found. Please create some teachers first.'))
            return

        for teacher in teachers:
            # Assign random grade
            grade = random.choice(grades)
            
            # Random salary within grade range
            salary_range = float(grade.maximum_salary - grade.minimum_salary)
            basic_salary = grade.minimum_salary + Decimal(str(random.uniform(0, salary_range)))
            
            structure, created = EmployeeSalaryStructure.objects.get_or_create(
                employee=teacher,
                is_active=True,
                defaults={
                    'salary_grade': grade,
                    'basic_salary': basic_salary.quantize(Decimal('0.01')),
                    'effective_date': today - timedelta(days=random.randint(30, 365)),
                    'bank_name': random.choice(['ABC Bank', 'XYZ Bank', 'National Bank']),
                    'account_number': f'ACC{random.randint(100000, 999999)}',
                    'account_name': teacher.full_name,
                    'tax_rate': Decimal('15.00'),
                }
            )
            
            if created:
                self.stdout.write(f'Created salary structure for: {teacher.full_name}')
                
                # Add salary components
                components = SalaryComponent.objects.filter(is_mandatory=True)
                for component in components:
                    EmployeeSalaryComponent.objects.get_or_create(
                        salary_structure=structure,
                        component=component,
                        defaults={
                            'effective_date': structure.effective_date,
                            'is_active': True
                        }
                    )

        # Create sample payroll records
        self.stdout.write('Creating payroll records...')
        periods = PayrollPeriod.objects.all()
        structures = EmployeeSalaryStructure.objects.filter(is_active=True)
        
        for period in periods:
            for structure in structures:
                payroll, created = EnhancedPayroll.objects.get_or_create(
                    employee=structure.employee,
                    payroll_period=period,
                    defaults={
                        'salary_structure': structure,
                        'basic_salary': structure.basic_salary,
                        'working_days': 22,
                        'days_worked': random.randint(20, 22),
                        'overtime_hours': Decimal(str(random.uniform(0, 10))),
                        'total_allowances': structure.basic_salary * Decimal('0.25'),  # 25% allowances
                        'total_deductions': structure.basic_salary * Decimal('0.20'),  # 20% deductions
                        'tax_amount': structure.basic_salary * Decimal('0.15'),  # 15% tax
                        'status': period.status,
                    }
                )
                
                if created:
                    # Calculate gross and net salary
                    payroll.gross_salary = payroll.basic_salary + payroll.total_allowances
                    payroll.net_salary = payroll.gross_salary - payroll.total_deductions - payroll.tax_amount
                    payroll.save()

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample salary data:\n'
                f'- {SalaryGrade.objects.count()} salary grades\n'
                f'- {SalaryComponent.objects.count()} salary components\n'
                f'- {PayrollPeriod.objects.count()} payroll periods\n'
                f'- {EmployeeSalaryStructure.objects.count()} employee salary structures\n'
                f'- {EnhancedPayroll.objects.count()} payroll records'
            )
        )
