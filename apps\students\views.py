from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.db import transaction
from datetime import date, datetime
import csv

from .models import (
    Student, Guardian, StudentGuardian, StudentDocument,
    StudentGrade, StudentSubject, StudentStatusHistory,
    StudentPromotion, StudentGraduation
)
from .forms import (
    StudentRegistrationForm, StudentProfileForm, GuardianForm,
    StudentDocumentForm, StudentGradeAssignmentForm, StudentSubjectAssignmentForm,
    StudentStatusChangeForm, BulkStudentAssignmentForm, StudentSearchForm
)
from apps.academics.models import Grade, Subject

@login_required
def students_dashboard(request):
    """Student management dashboard"""
    # Get statistics
    total_students = Student.objects.count()
    active_students = Student.objects.filter(status='active').count()
    new_students_this_month = Student.objects.filter(
        admission_date__month=date.today().month,
        admission_date__year=date.today().year
    ).count()

    # Get students by grade
    students_by_grade = Student.objects.filter(status='active').values(
        'current_grade__name'
    ).annotate(count=Count('id')).order_by('current_grade__name')

    # Recent registrations
    recent_students = Student.objects.filter(status='active').order_by('-created_at')[:5]

    # Students needing attention (missing documents, etc.)
    students_needing_attention = Student.objects.filter(
        status='active',
        documents__isnull=True
    ).distinct()[:5]

    context = {
        'total_students': total_students,
        'active_students': active_students,
        'new_students_this_month': new_students_this_month,
        'students_by_grade': students_by_grade,
        'recent_students': recent_students,
        'students_needing_attention': students_needing_attention,
    }
    return render(request, 'students/dashboard.html', context)

@login_required
def students_list(request):
    """List all students with search and filtering"""
    form = StudentSearchForm(request.GET)
    students = Student.objects.select_related('current_grade').prefetch_related('guardians')

    # Apply filters
    if form.is_valid():
        search_query = form.cleaned_data.get('search_query')
        if search_query:
            students = students.filter(
                Q(full_name__icontains=search_query) |
                Q(student_id__icontains=search_query) |
                Q(index_number__icontains=search_query) |
                Q(admission_number__icontains=search_query)
            )

        grade = form.cleaned_data.get('grade')
        if grade:
            students = students.filter(current_grade=grade)

        status = form.cleaned_data.get('status')
        if status:
            students = students.filter(status=status)

        boarding_status = form.cleaned_data.get('boarding_status')
        if boarding_status:
            students = students.filter(boarding_status=boarding_status)

        admission_year = form.cleaned_data.get('admission_year')
        if admission_year:
            students = students.filter(admission_date__year=admission_year)

    # Pagination
    paginator = Paginator(students, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'students': page_obj,
    }
    return render(request, 'students/students_list.html', context)

@login_required
def student_detail(request, student_id):
    """Student detail view"""
    student = get_object_or_404(Student, id=student_id)

    # Get related information
    guardians = student.guardians.all()
    documents = student.documents.all()
    grade_assignments = student.grade_assignments.all().order_by('-academic_year', '-term')
    subject_assignments = student.subject_assignments.filter(
        registration_status='registered'
    ).select_related('subject', 'teacher')
    status_history = student.status_history.all().order_by('-change_date')

    context = {
        'student': student,
        'guardians': guardians,
        'documents': documents,
        'grade_assignments': grade_assignments,
        'subject_assignments': subject_assignments,
        'status_history': status_history,
    }
    return render(request, 'students/student_detail.html', context)

@login_required
def student_add(request):
    """Add a new student (admin interface)"""
    if request.method == 'POST':
        form = StudentRegistrationForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create student
                    student = form.save(commit=False)
                    student.created_by = request.user
                    student.updated_by = request.user
                    student.status = 'active'  # Default status for admin-added students
                    student.save()
                    form.save_m2m()  # Save many-to-many relationships

                    # Create guardian if information is provided
                    if form.cleaned_data.get('guardian_full_name'):
                        guardian = Guardian.objects.create(
                            full_name=form.cleaned_data['guardian_full_name'],
                            relationship=form.cleaned_data.get('guardian_relationship', 'parent'),
                            phone=form.cleaned_data.get('guardian_phone', ''),
                            email=form.cleaned_data.get('guardian_email', ''),
                            address=form.cleaned_data.get('guardian_address', ''),
                            is_primary_contact=True,
                            is_emergency_contact=True
                        )

                        # Link student and guardian
                        StudentGuardian.objects.create(
                            student=student,
                            guardian=guardian,
                            is_fee_payer=True
                        )

                    # Create initial grade assignment if grade is selected
                    if student.current_grade:
                        StudentGrade.objects.create(
                            student=student,
                            grade=student.current_grade,
                            section=student.current_section or '',
                            academic_year=student.admission_date.year,
                            term=1,
                            assignment_type='new_admission',
                            assignment_date=student.admission_date,
                            created_by=request.user
                        )

                    # Create status history
                    StudentStatusHistory.objects.create(
                        student=student,
                        previous_status='new',
                        new_status='active',
                        changed_by=request.user,
                        notes='Added by admin'
                    )

                    messages.success(request, f'Student {student.full_name} added successfully!')
                    return redirect('students:student_detail', student_id=student.id)
            except Exception as e:
                messages.error(request, f'Error adding student: {str(e)}')
    else:
        form = StudentRegistrationForm()

    context = {
        'form': form,
        'title': 'Add New Student',
    }
    return render(request, 'students/student_form.html', context)

@login_required
def student_register(request):
    """Register a new student (public registration)"""
    if request.method == 'POST':
        form = StudentRegistrationForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create student
                    student = form.save(commit=False)
                    student.created_by = request.user
                    student.updated_by = request.user
                    student.status = 'pending'  # Default status for self-registered students
                    student.save()
                    form.save_m2m()  # Save many-to-many relationships

                    # Create guardian if information is provided
                    if form.cleaned_data.get('guardian_full_name'):
                        guardian = Guardian.objects.create(
                            full_name=form.cleaned_data['guardian_full_name'],
                            relationship=form.cleaned_data.get('guardian_relationship', 'parent'),
                            phone=form.cleaned_data.get('guardian_phone', ''),
                            email=form.cleaned_data.get('guardian_email', ''),
                            address=form.cleaned_data.get('guardian_address', ''),
                            is_primary_contact=True,
                            is_emergency_contact=True
                        )

                        # Link student and guardian
                        StudentGuardian.objects.create(
                            student=student,
                            guardian=guardian,
                            is_fee_payer=True
                        )

                    # Create status history
                    StudentStatusHistory.objects.create(
                        student=student,
                        previous_status='new',
                        new_status='pending',
                        changed_by=request.user,
                        notes='Self-registered, pending approval'
                    )

                    messages.success(request, 'Registration submitted successfully! Your application is under review.')
                    return redirect('students:student_detail', student_id=student.id)
            except Exception as e:
                messages.error(request, f'Error submitting registration: {str(e)}')
    else:
        form = StudentRegistrationForm()

    context = {
        'form': form,
        'title': 'Student Registration',
    }
    return render(request, 'students/student_register.html', context)
@login_required
def student_edit(request, student_id):
    """Edit student profile"""
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        form = StudentProfileForm(request.POST, request.FILES, instance=student)
        if form.is_valid():
            student = form.save(commit=False)
            student.updated_by = request.user
            student.save()
            messages.success(request, 'Student profile updated successfully!')
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentProfileForm(instance=student)

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'students/student_edit.html', context)

@login_required
def student_status_change(request, student_id):
    """Change student status"""
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        form = StudentStatusChangeForm(request.POST)
        if form.is_valid():
            # Create status history record
            status_change = form.save(commit=False)
            status_change.student = student
            status_change.previous_status = student.status
            status_change.changed_by = request.user
            status_change.save()

            # Update student status
            student.status = status_change.new_status
            student.updated_by = request.user
            student.save()

            messages.success(request, f'Student status changed to {status_change.new_status}')
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentStatusChangeForm()

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'students/student_status_change.html', context)

@login_required
def student_grade_assignment(request, student_id):
    """Assign student to grade/class"""
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        form = StudentGradeAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.student = student
            assignment.created_by = request.user
            assignment.save()

            # Update student's current grade
            student.current_grade = assignment.grade
            student.current_section = assignment.section
            student.updated_by = request.user
            student.save()

            messages.success(request, f'Student assigned to {assignment.grade.name}')
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentGradeAssignmentForm(initial={
            'academic_year': date.today().year,
            'assignment_date': date.today()
        })

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'students/student_grade_assignment.html', context)

@login_required
def student_subject_assignment(request, student_id):
    """Assign subjects to student"""
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        form = StudentSubjectAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.student = student
            assignment.created_by = request.user
            assignment.save()

            messages.success(request, f'Subject {assignment.subject.name} assigned to student')
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentSubjectAssignmentForm(initial={
            'grade': student.current_grade,
            'academic_year': date.today().year,
            'registration_date': date.today()
        })

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'students/student_subject_assignment.html', context)

@login_required
def student_document_upload(request, student_id):
    """Upload student documents"""
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        form = StudentDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.student = student
            document.uploaded_by = request.user
            document.save()

            messages.success(request, 'Document uploaded successfully!')
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentDocumentForm()

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'students/student_document_upload.html', context)

@login_required
def guardian_add(request, student_id):
    """Add guardian to student"""
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        form = GuardianForm(request.POST)
        if form.is_valid():
            guardian = form.save()

            # Link guardian to student
            StudentGuardian.objects.create(
                student=student,
                guardian=guardian
            )

            messages.success(request, f'Guardian {guardian.full_name} added successfully!')
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = GuardianForm()

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'students/guardian_add.html', context)

@login_required
def edit_guardian(request, guardian_id):
    """Edit an existing guardian"""
    guardian = get_object_or_404(Guardian, id=guardian_id)
    
    if request.method == 'POST':
        form = GuardianForm(request.POST, instance=guardian)
        if form.is_valid():
            guardian = form.save()
            messages.success(request, f'Guardian {guardian.full_name} updated successfully!')
            
            # Redirect to the first student's detail page if available
            student_guardian = StudentGuardian.objects.filter(guardian=guardian).first()
            if student_guardian:
                return redirect('students:student_detail', student_id=student_guardian.student.id)
            return redirect('students:students_list')
    else:
        form = GuardianForm(instance=guardian)
    
    # Get all students associated with this guardian
    students = Student.objects.filter(studentguardian__guardian=guardian)
    
    context = {
        'form': form,
        'guardian': guardian,
        'students': students,
    }
    return render(request, 'students/guardian_edit.html', context)


@login_required
def delete_guardian(request, guardian_id):
    """Delete a guardian"""
    guardian = get_object_or_404(Guardian, id=guardian_id)
    
    if request.method == 'POST':
        # Get the first student to redirect to their detail page after deletion
        student_guardian = StudentGuardian.objects.filter(guardian=guardian).first()
        
        # Delete the guardian (this will cascade to StudentGuardian relationships)
        guardian_name = guardian.full_name
        guardian.delete()
        
        messages.success(request, f'Guardian {guardian_name} deleted successfully!')
        
        if student_guardian:
            return redirect('students:student_detail', student_id=student_guardian.student.id)
        return redirect('students:students_list')
    
    # If not a POST request, show confirmation page
    students = Student.objects.filter(studentguardian__guardian=guardian)
    context = {
        'guardian': guardian,
        'students': students,
    }
    return render(request, 'students/guardian_confirm_delete.html', context)


@login_required
def student_delete(request, student_id):
    """Delete a student"""
    student = get_object_or_404(Student, id=student_id)
    
    if request.method == 'POST':
        try:
            student.delete()
            messages.success(request, 'Student deleted successfully')
            return redirect('students:students_list')
        except Exception as e:
            messages.error(request, f'Error deleting student: {str(e)}')
            return redirect('students:student_detail', student_id=student_id)
    
    # If not a POST request, show confirmation page
    context = {
        'student': student,
    }
    return render(request, 'students/student_confirm_delete.html', context)


@login_required
def bulk_student_assignment(request):
    """Bulk assign students to grades"""
    if request.method == 'POST':
        form = BulkStudentAssignmentForm(request.POST)
        if form.is_valid():
            students = form.cleaned_data['students']
            grade = form.cleaned_data['grade']
            section = form.cleaned_data['section']
            academic_year = form.cleaned_data['academic_year']
            term = form.cleaned_data['term']
            assignment_date = form.cleaned_data['assignment_date']

            try:
                with transaction.atomic():
                    for student in students:
                        # Create grade assignment
                        StudentGrade.objects.create(
                            student=student,
                            grade=grade,
                            section=section or '',
                            academic_year=academic_year,
                            term=term,
                            assignment_type='promotion',
                            assignment_date=assignment_date,
                            created_by=request.user
                        )

                        # Update student's current grade
                        student.current_grade = grade
                        student.current_section = section
                        student.updated_by = request.user
                        student.save()

                    messages.success(request, f'{len(students)} students assigned to {grade.name} successfully!')
                    return redirect('students:students_list')
            except Exception as e:
                messages.error(request, f'Error in bulk assignment: {str(e)}')
    else:
        form = BulkStudentAssignmentForm()

    context = {'form': form}
    return render(request, 'students/bulk_assignment.html', context)
