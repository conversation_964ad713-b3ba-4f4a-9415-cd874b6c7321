from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
import uuid
from datetime import date, timedelta

# Create your models here.

class FeeStructure(models.Model):
    id = models.AutoField(primary_key=True)
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    fee_category = models.CharField(max_length=100)  # Tuition, Boarding, Library, etc.
    amount = models.DecimalField(max_digits=11, decimal_places=2)
    term = models.IntegerField(default=0)  # 0 for yearly, 1-3 for specific term
    year = models.PositiveIntegerField()
    description = models.CharField(max_length=255, null=True, blank=True)
    is_mandatory = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.fee_category} - {self.amount}"

class StudentPayment(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('Cash', 'Cash'),
        ('M-Pesa', 'M-Pesa'),
        ('Bank', 'Bank'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    year = models.PositiveIntegerField()
    term = models.IntegerField(default=1)  # 1, 2, 3
    month = models.CharField(max_length=255)
    date = models.DateField()
    paid = models.DecimalField(max_digits=11, decimal_places=2)
    payment_method = models.CharField(max_length=50, default='Cash', choices=PAYMENT_METHOD_CHOICES)
    transaction_id = models.CharField(max_length=100, null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    receipt_number = models.CharField(max_length=50, null=True, blank=True)
    received_by = models.BigIntegerField(null=True, blank=True)
    _status = models.CharField(max_length=255)
    student_status = models.CharField(max_length=255)

    def __str__(self):
        return f"Payment - {self.index_number}"

class StudentPaymentHistory(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('Cash', 'Cash'),
        ('M-Pesa', 'M-Pesa'),
        ('Bank', 'Bank'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    grade_id = models.IntegerField()
    subject_id = models.IntegerField()
    teacher_id = models.IntegerField()
    subject_fee = models.DecimalField(max_digits=11, decimal_places=2)
    subtotal = models.DecimalField(max_digits=11, decimal_places=2)
    _status = models.CharField(max_length=255)
    month = models.CharField(max_length=255)
    year = models.PositiveIntegerField()
    date = models.DateField()
    invoice_number = models.IntegerField()
    payment_method = models.CharField(max_length=50, default='Cash', choices=PAYMENT_METHOD_CHOICES)
    transaction_id = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"Payment History - {self.index_number}"

class StudentFeeBalance(models.Model):
    STATUS_CHOICES = [
        ('unpaid', 'Unpaid'),
        ('partially_paid', 'Partially Paid'),
        ('paid', 'Paid'),
    ]
    id = models.AutoField(primary_key=True)
    index_number = models.BigIntegerField()
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    year = models.PositiveIntegerField()
    term = models.IntegerField()
    total_amount = models.DecimalField(max_digits=11, decimal_places=2)
    amount_paid = models.DecimalField(max_digits=11, decimal_places=2, default=0)
    balance = models.DecimalField(max_digits=11, decimal_places=2)
    last_payment_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=50, default='unpaid', choices=STATUS_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Fee Balance - {self.index_number}"

class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='orders')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Order #{self.id} by {self.user.email}"

# Enhanced Finance Management Models

class FeeCategory(models.Model):
    """Fee categories for better organization"""
    CATEGORY_TYPES = [
        ('tuition', 'Tuition Fees'),
        ('boarding', 'Boarding Fees'),
        ('transport', 'Transport Fees'),
        ('library', 'Library Fees'),
        ('laboratory', 'Laboratory Fees'),
        ('sports', 'Sports Fees'),
        ('examination', 'Examination Fees'),
        ('development', 'Development Fees'),
        ('activity', 'Activity Fees'),
        ('uniform', 'Uniform Fees'),
        ('medical', 'Medical Fees'),
        ('other', 'Other Fees'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField(blank=True)
    is_mandatory = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    color_code = models.CharField(max_length=7, default='#3B82F6', help_text="Hex color for UI")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Fee Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

class EnhancedFeeStructure(models.Model):
    """Enhanced fee structure with more flexibility"""
    FEE_TYPES = [
        ('fixed', 'Fixed Amount'),
        ('percentage', 'Percentage Based'),
        ('per_unit', 'Per Unit'),
        ('sliding_scale', 'Sliding Scale'),
    ]

    PAYMENT_FREQUENCIES = [
        ('one_time', 'One Time'),
        ('monthly', 'Monthly'),
        ('termly', 'Termly'),
        ('yearly', 'Yearly'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    category = models.ForeignKey(FeeCategory, on_delete=models.CASCADE, related_name='fee_structures')
    grade = models.ForeignKey('academics.Grade', on_delete=models.CASCADE)
    academic_year = models.IntegerField()
    term = models.IntegerField(null=True, blank=True, help_text="Leave blank for yearly fees")

    # Fee calculation
    fee_type = models.CharField(max_length=20, choices=FEE_TYPES, default='fixed')
    base_amount = models.DecimalField(max_digits=12, decimal_places=2)
    percentage_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Payment terms
    payment_frequency = models.CharField(max_length=20, choices=PAYMENT_FREQUENCIES, default='termly')
    due_date = models.DateField()
    late_fee_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    late_fee_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    grace_period_days = models.IntegerField(default=0)

    # Status and metadata
    is_mandatory = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    description = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['category', 'grade', 'academic_year', 'term']
        ordering = ['academic_year', 'term', 'grade', 'category']

    def calculate_amount(self, base_value=None, units=None):
        """Calculate fee amount based on fee type"""
        if self.fee_type == 'fixed':
            return self.base_amount
        elif self.fee_type == 'percentage' and base_value:
            return (base_value * self.percentage_rate) / 100
        elif self.fee_type == 'per_unit' and units:
            return self.unit_price * units
        return self.base_amount

    def calculate_late_fee(self, days_late):
        """Calculate late fee based on days late"""
        if days_late <= self.grace_period_days:
            return Decimal('0.00')

        late_fee = self.late_fee_amount
        if self.late_fee_percentage > 0:
            late_fee += (self.base_amount * self.late_fee_percentage) / 100

        return late_fee

    def __str__(self):
        return f"{self.name} - {self.grade.name} ({self.academic_year})"

class PaymentMethod(models.Model):
    """Payment methods configuration"""
    METHOD_TYPES = [
        ('cash', 'Cash'),
        ('mpesa', 'M-Pesa'),
        ('bank_transfer', 'Bank Transfer'),
        ('cheque', 'Cheque'),
        ('card', 'Credit/Debit Card'),
        ('mobile_money', 'Mobile Money'),
        ('online', 'Online Payment'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    method_type = models.CharField(max_length=20, choices=METHOD_TYPES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    requires_reference = models.BooleanField(default=False)
    processing_fee_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    processing_fee_fixed = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Integration settings
    api_endpoint = models.URLField(blank=True)
    api_key = models.CharField(max_length=500, blank=True)
    merchant_id = models.CharField(max_length=100, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_processing_fee(self, amount):
        """Calculate processing fee for this payment method"""
        percentage_fee = (amount * self.processing_fee_percentage) / 100
        return percentage_fee + self.processing_fee_fixed

    def __str__(self):
        return self.name

class Invoice(models.Model):
    """Enhanced invoice management"""
    INVOICE_STATUSES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('viewed', 'Viewed'),
        ('paid', 'Paid'),
        ('partially_paid', 'Partially Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    INVOICE_TYPES = [
        ('fee_invoice', 'Fee Invoice'),
        ('custom_invoice', 'Custom Invoice'),
        ('penalty_invoice', 'Penalty Invoice'),
        ('refund_invoice', 'Refund Invoice'),
    ]

    id = models.AutoField(primary_key=True)
    invoice_number = models.CharField(max_length=50, unique=True)
    invoice_type = models.CharField(max_length=20, choices=INVOICE_TYPES, default='fee_invoice')
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='invoices')

    # Invoice details
    issue_date = models.DateField(auto_now_add=True)
    due_date = models.DateField()
    description = models.TextField()
    notes = models.TextField(blank=True)

    # Financial details
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    amount_paid = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    balance_due = models.DecimalField(max_digits=12, decimal_places=2)

    # Status and tracking
    status = models.CharField(max_length=20, choices=INVOICE_STATUSES, default='draft')
    sent_date = models.DateTimeField(null=True, blank=True)
    viewed_date = models.DateTimeField(null=True, blank=True)
    paid_date = models.DateTimeField(null=True, blank=True)

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

        # Calculate totals
        self.tax_amount = (self.subtotal * self.tax_rate) / 100
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        self.balance_due = self.total_amount - self.amount_paid

        # Update status based on payment
        if self.amount_paid >= self.total_amount:
            self.status = 'paid'
            if not self.paid_date:
                self.paid_date = timezone.now()
        elif self.amount_paid > 0:
            self.status = 'partially_paid'
        elif self.due_date < date.today() and self.status not in ['paid', 'cancelled']:
            self.status = 'overdue'

        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate unique invoice number"""
        year = date.today().year
        count = Invoice.objects.filter(created_at__year=year).count() + 1
        return f"INV-{year}-{count:06d}"

    @property
    def is_overdue(self):
        return self.due_date < date.today() and self.status not in ['paid', 'cancelled']

    @property
    def days_overdue(self):
        if self.is_overdue:
            return (date.today() - self.due_date).days
        return 0

    def __str__(self):
        return f"{self.invoice_number} - {self.student.full_name}"

class InvoiceItem(models.Model):
    """Individual items in an invoice"""
    id = models.AutoField(primary_key=True)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    fee_structure = models.ForeignKey(EnhancedFeeStructure, on_delete=models.CASCADE, null=True, blank=True)
    description = models.CharField(max_length=200)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=12, decimal_places=2)

    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.description} - {self.total_price}"

class EnhancedPayment(models.Model):
    """Enhanced payment tracking"""
    PAYMENT_STATUSES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    id = models.AutoField(primary_key=True)
    payment_reference = models.CharField(max_length=100, unique=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='enhanced_payments')
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments', null=True, blank=True)

    # Payment details
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    payment_date = models.DateTimeField()

    # Transaction details
    transaction_id = models.CharField(max_length=200, blank=True)
    external_reference = models.CharField(max_length=200, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)

    # Processing details
    processing_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=12, decimal_places=2)

    # Status and tracking
    status = models.CharField(max_length=20, choices=PAYMENT_STATUSES, default='pending')
    failure_reason = models.TextField(blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    received_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-payment_date']

    def save(self, *args, **kwargs):
        if not self.payment_reference:
            self.payment_reference = self.generate_payment_reference()

        # Calculate processing fee and net amount
        self.processing_fee = self.payment_method.calculate_processing_fee(self.amount)
        self.net_amount = self.amount - self.processing_fee

        super().save(*args, **kwargs)

    def generate_payment_reference(self):
        """Generate unique payment reference"""
        year = date.today().year
        count = EnhancedPayment.objects.filter(created_at__year=year).count() + 1
        return f"PAY-{year}-{count:08d}"

    def __str__(self):
        return f"{self.payment_reference} - {self.student.full_name} - {self.amount}"

class Scholarship(models.Model):
    """Scholarship and bursary management"""
    SCHOLARSHIP_TYPES = [
        ('academic', 'Academic Excellence'),
        ('need_based', 'Need-Based'),
        ('sports', 'Sports Scholarship'),
        ('talent', 'Talent-Based'),
        ('government', 'Government Bursary'),
        ('sponsor', 'Sponsor Scholarship'),
        ('staff_child', 'Staff Child'),
        ('sibling', 'Sibling Discount'),
        ('other', 'Other'),
    ]

    COVERAGE_TYPES = [
        ('full', 'Full Coverage'),
        ('partial', 'Partial Coverage'),
        ('percentage', 'Percentage Based'),
        ('fixed_amount', 'Fixed Amount'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    scholarship_type = models.CharField(max_length=20, choices=SCHOLARSHIP_TYPES)
    description = models.TextField()

    # Coverage details
    coverage_type = models.CharField(max_length=20, choices=COVERAGE_TYPES)
    coverage_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    coverage_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    max_amount_per_year = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)

    # Eligibility
    eligible_grades = models.ManyToManyField('academics.Grade', related_name='scholarships')
    minimum_grade_average = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    family_income_threshold = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)

    # Validity
    start_date = models.DateField()
    end_date = models.DateField()
    is_renewable = models.BooleanField(default=False)
    renewal_criteria = models.TextField(blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    total_budget = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    allocated_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_discount(self, base_amount):
        """Calculate scholarship discount amount"""
        if self.coverage_type == 'full':
            return base_amount
        elif self.coverage_type == 'percentage':
            discount = (base_amount * self.coverage_percentage) / 100
            if self.max_amount_per_year:
                return min(discount, self.max_amount_per_year)
            return discount
        elif self.coverage_type == 'fixed_amount':
            return min(self.coverage_amount, base_amount)
        return Decimal('0.00')

    def __str__(self):
        return self.name

class StudentScholarship(models.Model):
    """Student scholarship assignments"""
    APPLICATION_STATUSES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('suspended', 'Suspended'),
        ('expired', 'Expired'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='scholarships')
    scholarship = models.ForeignKey(Scholarship, on_delete=models.CASCADE, related_name='recipients')

    # Application details
    application_date = models.DateField(auto_now_add=True)
    approved_date = models.DateField(null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()

    # Status and tracking
    status = models.CharField(max_length=20, choices=APPLICATION_STATUSES, default='pending')
    total_amount_awarded = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    amount_utilized = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Review details
    reviewed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    review_notes = models.TextField(blank=True)

    # Renewal tracking
    is_renewable = models.BooleanField(default=False)
    renewal_date = models.DateField(null=True, blank=True)
    renewal_criteria_met = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['student', 'scholarship']
        ordering = ['-created_at']

    def calculate_available_amount(self):
        """Calculate remaining scholarship amount"""
        return self.total_amount_awarded - self.amount_utilized

    def is_active(self):
        """Check if scholarship is currently active"""
        today = date.today()
        return (self.status == 'approved' and
                self.start_date <= today <= self.end_date)

    def __str__(self):
        return f"{self.student.full_name} - {self.scholarship.name}"

class PaymentReminder(models.Model):
    """Payment reminders and notifications"""
    REMINDER_TYPES = [
        ('due_soon', 'Payment Due Soon'),
        ('overdue', 'Payment Overdue'),
        ('partial_payment', 'Partial Payment Reminder'),
        ('final_notice', 'Final Notice'),
    ]

    DELIVERY_METHODS = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('phone', 'Phone Call'),
        ('letter', 'Physical Letter'),
        ('in_person', 'In Person'),
    ]

    REMINDER_STATUSES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='payment_reminders')
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='reminders', null=True, blank=True)

    # Reminder details
    reminder_type = models.CharField(max_length=20, choices=REMINDER_TYPES)
    delivery_method = models.CharField(max_length=20, choices=DELIVERY_METHODS)
    scheduled_date = models.DateTimeField()
    sent_date = models.DateTimeField(null=True, blank=True)

    # Content
    subject = models.CharField(max_length=200)
    message = models.TextField()
    amount_due = models.DecimalField(max_digits=12, decimal_places=2)

    # Status and tracking
    status = models.CharField(max_length=20, choices=REMINDER_STATUSES, default='pending')
    delivery_status = models.CharField(max_length=100, blank=True)
    failure_reason = models.TextField(blank=True)

    # Contact details used
    email_sent_to = models.EmailField(blank=True)
    phone_sent_to = models.CharField(max_length=20, blank=True)

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-scheduled_date']

    def __str__(self):
        return f"{self.get_reminder_type_display()} - {self.student.full_name}"

class FeeDiscount(models.Model):
    """Fee discount management"""
    DISCOUNT_TYPES = [
        ('percentage', 'Percentage Discount'),
        ('fixed_amount', 'Fixed Amount Discount'),
        ('waiver', 'Complete Waiver'),
    ]

    DISCOUNT_REASONS = [
        ('early_payment', 'Early Payment'),
        ('sibling_discount', 'Sibling Discount'),
        ('staff_child', 'Staff Child'),
        ('hardship', 'Financial Hardship'),
        ('loyalty', 'Loyalty Discount'),
        ('bulk_payment', 'Bulk Payment'),
        ('promotional', 'Promotional Offer'),
        ('other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    discount_type = models.CharField(max_length=20, choices=DISCOUNT_TYPES)
    discount_reason = models.CharField(max_length=20, choices=DISCOUNT_REASONS)

    # Discount calculation
    percentage_value = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    fixed_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    max_discount_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Applicability
    applicable_categories = models.ManyToManyField(FeeCategory, related_name='discounts')
    applicable_grades = models.ManyToManyField('academics.Grade', related_name='fee_discounts')

    # Validity
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=False)

    # Conditions
    minimum_payment_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    early_payment_days = models.IntegerField(null=True, blank=True)

    # Metadata
    description = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_discount(self, base_amount):
        """Calculate discount amount"""
        if self.discount_type == 'percentage':
            discount = (base_amount * self.percentage_value) / 100
            if self.max_discount_amount:
                return min(discount, self.max_discount_amount)
            return discount
        elif self.discount_type == 'fixed_amount':
            return min(self.fixed_amount, base_amount)
        elif self.discount_type == 'waiver':
            return base_amount
        return Decimal('0.00')

    def is_valid_today(self):
        """Check if discount is valid today"""
        today = date.today()
        return self.is_active and self.start_date <= today <= self.end_date

    def __str__(self):
        return self.name
