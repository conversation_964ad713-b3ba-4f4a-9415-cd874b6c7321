from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView
from django.db import models
from django.db.models import Q
from apps.core.models import SchoolInfo
from apps.students.models import Student
from apps.teachers.models import Teacher
from apps.academics.models import Subject
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from datetime import timedelta
from apps.communication.models import Feedback
from apps.finance.models import Order
from apps.communication.forms import FeedbackForm
from django.contrib import messages
from django.shortcuts import redirect
from apps.petty_cash.forms import PettyCashRequestForm
from apps.petty_cash.models import PettyCash
from apps.attendance.models import StudentAttendance, TeacherAttendance
from apps.exams.models import StudentExam, StudentOverallExamResult

# Create your views here.

class AdminDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/admin_dashboard.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        context['feedback_form'] = FeedbackForm()
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        if 'feedback_submit' in request.POST:
            form = FeedbackForm(request.POST)
            if form.is_valid():
                feedback = form.save(commit=False)
                feedback.user = request.user
                feedback.save()
                messages.success(request, 'Feedback submitted!')
                return redirect('dashboard:admin_dashboard')
        return self.get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        User = get_user_model()

        # Basic school information
        context['school_info'] = SchoolInfo.objects.first()

        # Core statistics
        context['total_users'] = User.objects.count()
        context['active_users'] = User.objects.filter(status='active').count()
        context['total_students'] = Student.objects.count()
        context['active_students'] = Student.objects.filter(status='active').count()
        context['pending_admissions'] = Student.objects.filter(status='pending').count()
        context['new_students_month'] = Student.objects.filter(
            created_at__gte=timezone.now().replace(day=1)
        ).count()

        context['total_teachers'] = Teacher.objects.count()
        try:
            context['teachers_active_today'] = TeacherAttendance.objects.filter(
                date=timezone.now().date(),
                status='present'
            ).count()
        except Exception as e:
            # Fallback if TeacherAttendance table doesn't have status column
            context['teachers_active_today'] = 0

        # Academic statistics
        context['total_subjects'] = Subject.objects.count()
        try:
            from apps.academics.models import Grade
            context['total_grades'] = Grade.objects.count()
        except Exception:
            context['total_grades'] = 0

        try:
            from apps.timetable.models import Timetable
            context['active_timetables'] = Timetable.objects.filter(status='active').count()
        except Exception:
            context['active_timetables'] = 0

        try:
            from apps.exams.models import Exam
            context['upcoming_exams'] = Exam.objects.filter(
                date__gte=timezone.now().date()
            ).count()
        except Exception:
            context['upcoming_exams'] = 0

        # User role summary
        context['user_roles'] = {
            'admin': User.objects.filter(type='admin').count(),
            'teacher': User.objects.filter(type='teacher').count(),
            'student': User.objects.filter(type='student').count(),
            'parent': User.objects.filter(type='parent').count(),
            'accountant': User.objects.filter(type='accountant').count(),
        }
        context['support_staff_count'] = User.objects.filter(
            type__in=['librarian', 'nurse', 'security', 'maintenance']
        ).count()

        # Financial statistics
        try:
            from apps.finance.models import FeePayment, FeeStructure
            context['total_revenue'] = FeePayment.objects.filter(
                payment_date__month=timezone.now().month,
                payment_date__year=timezone.now().year,
                status='completed'
            ).aggregate(total=models.Sum('amount'))['total'] or 0

            context['pending_fees'] = FeeStructure.objects.filter(
                status='pending'
            ).aggregate(total=models.Sum('amount'))['total'] or 0
        except:
            context['total_revenue'] = 0
            context['pending_fees'] = 0

        # Auxiliary services statistics
        try:
            from apps.library.models import Book, BookBorrowing
            context['total_books'] = Book.objects.count()
            context['books_borrowed'] = BookBorrowing.objects.filter(
                return_date__isnull=True
            ).count()
            context['books_available'] = context['total_books'] - context['books_borrowed']
        except:
            context['total_books'] = 0
            context['books_borrowed'] = 0
            context['books_available'] = 0

        try:
            from apps.transport.models import Route, Vehicle, StudentTransport
            context['active_routes'] = Route.objects.filter(is_active=True).count()
            context['total_vehicles'] = Vehicle.objects.count()
            context['transport_students'] = StudentTransport.objects.filter(
                is_active=True
            ).count()
        except:
            context['active_routes'] = 0
            context['total_vehicles'] = 0
            context['transport_students'] = 0

        try:
            from apps.hostel.models import Room, StudentHostel
            context['total_rooms'] = Room.objects.count()
            context['occupied_rooms'] = Room.objects.filter(
                is_occupied=True
            ).count()
            context['occupancy_rate'] = (
                context['occupied_rooms'] / context['total_rooms'] * 100
            ) if context['total_rooms'] > 0 else 0
        except:
            context['total_rooms'] = 0
            context['occupied_rooms'] = 0
            context['occupancy_rate'] = 0

        try:
            from apps.health.models import HealthRecord, HealthVisit
            context['health_records'] = HealthRecord.objects.count()
            context['recent_health_visits'] = HealthVisit.objects.filter(
                visit_date__gte=timezone.now().date() - timedelta(days=7)
            ).count()
            context['health_alerts'] = HealthRecord.objects.filter(
                has_allergies=True
            ).count()
        except:
            context['health_records'] = 0
            context['recent_health_visits'] = 0
            context['health_alerts'] = 0

        # System health (mock calculation)
        context['system_health'] = 98  # This would be calculated based on various metrics

        # System alerts
        context['system_alerts'] = self.get_system_alerts()

        # Recent activities
        context['recent_activities'] = self.get_recent_activities()

        # Chart data
        context['profile_views'] = [200, 150, 180, 350, 300, 220]
        context['profile_views_labels'] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

        return context

    def get_system_alerts(self):
        """Generate system alerts based on various conditions"""
        alerts = []

        # Check for low attendance
        try:
            today_attendance = StudentAttendance.objects.filter(
                date=timezone.now().date()
            ).count()
            total_students = Student.objects.filter(status='active').count()

            if total_students > 0:
                attendance_rate = (today_attendance / total_students) * 100
                if attendance_rate < 80:
                    alerts.append({
                        'title': 'Low Attendance Alert',
                        'message': f'Today\'s attendance is only {attendance_rate:.1f}%',
                        'color': 'red',
                        'icon': 'exclamation-triangle',
                        'time': 'Today'
                    })
        except:
            pass

        # Check for pending fee payments
        try:
            from apps.finance.models import FeeStructure
            pending_fees = FeeStructure.objects.filter(status='pending').count()
            if pending_fees > 10:
                alerts.append({
                    'title': 'Pending Fee Payments',
                    'message': f'{pending_fees} students have pending fee payments',
                    'color': 'yellow',
                    'icon': 'dollar-sign',
                    'time': 'This week'
                })
        except:
            pass

        # Check for upcoming exams
        try:
            from apps.exams.models import Exam
            upcoming_exams = Exam.objects.filter(
                date__gte=timezone.now().date(),
                date__lte=timezone.now().date() + timedelta(days=7)
            ).count()
            if upcoming_exams > 0:
                alerts.append({
                    'title': 'Upcoming Examinations',
                    'message': f'{upcoming_exams} exams scheduled for next week',
                    'color': 'blue',
                    'icon': 'clipboard-list',
                    'time': 'Next week'
                })
        except:
            pass

        return alerts

    def get_recent_activities(self):
        """Get recent system activities"""
        activities = []

        # Recent user registrations
        User = get_user_model()
        recent_users = User.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:3]

        for user in recent_users:
            try:
                full_name = user.get_full_name()
            except Exception:
                full_name = user.email

            activities.append({
                'title': 'New User Registration',
                'description': f'{full_name} registered as {user.type}',
                'timestamp': user.created_at.strftime('%Y-%m-%d %H:%M'),
                'color': 'green',
                'icon': 'user-plus'
            })

        # Recent student admissions
        recent_students = Student.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:3]

        for student in recent_students:
            activities.append({
                'title': 'New Student Admission',
                'description': f'{student.full_name} admitted to {student.current_grade}',
                'timestamp': student.created_at.strftime('%Y-%m-%d %H:%M'),
                'color': 'blue',
                'icon': 'graduation-cap'
            })

        # Recent feedback
        recent_feedback = Feedback.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).order_by('-created_at')[:2]

        for feedback in recent_feedback:
            try:
                user_name = feedback.user.get_full_name()
            except Exception:
                user_name = feedback.user.email

            activities.append({
                'title': 'New Feedback Received',
                'description': f'Feedback from {user_name}',
                'timestamp': feedback.created_at.strftime('%Y-%m-%d %H:%M'),
                'color': 'purple',
                'icon': 'comment'
            })

        # Sort by timestamp and return latest 10
        activities.sort(key=lambda x: x['timestamp'], reverse=True)
        return activities[:10]

class TeacherDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/teacher_dashboard.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        context['feedback_form'] = FeedbackForm()
        context['petty_cash_form'] = PettyCashRequestForm()
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        if 'feedback_submit' in request.POST:
            form = FeedbackForm(request.POST)
            if form.is_valid():
                feedback = form.save(commit=False)
                feedback.user = request.user
                feedback.save()
                messages.success(request, 'Feedback submitted!')
                return redirect('dashboard:teacher_dashboard')
        elif 'petty_cash_submit' in request.POST:
            form = PettyCashRequestForm(request.POST)
            if form.is_valid():
                petty_cash = form.save(commit=False)
                petty_cash.received_by = request.user.id
                petty_cash.approved_by = None
                petty_cash.year = timezone.now().year
                petty_cash.month = timezone.now().strftime('%B')
                petty_cash.date = timezone.now().date()
                petty_cash.time = timezone.now().time()
                petty_cash.received_type = 'teacher'
                petty_cash._status = 'pending'
                petty_cash.save()
                messages.success(request, 'Petty cash request submitted!')
                return redirect('dashboard:teacher_dashboard')
        return self.get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['school_info'] = SchoolInfo.objects.first()
        user = self.request.user
        today = timezone.now().date()

        # Get teacher profile
        try:
            teacher = Teacher.objects.get(email=user.email)
            context['teacher'] = teacher
        except Teacher.DoesNotExist:
            teacher = None
            context['teacher'] = None

        # Class and Subject Information
        if teacher:
            # Get class assignments
            try:
                from apps.academics.models import ClassAssignment
                class_assignments = ClassAssignment.objects.filter(
                    teacher=teacher,
                    is_active=True
                ).select_related('grade', 'subject')

                context['my_class_assignments'] = []
                for assignment in class_assignments:
                    # Get student count for each class
                    student_count = Student.objects.filter(
                        current_grade=assignment.grade,
                        status='active'
                    ).count()

                    context['my_class_assignments'].append({
                        'id': assignment.id,
                        'grade': assignment.grade,
                        'subject': assignment.subject,
                        'students_count': student_count,
                        'schedule': f"{assignment.grade.name} - {assignment.subject.name}",
                    })

                context['my_classes_count'] = class_assignments.count()
            except:
                context['my_class_assignments'] = []
                context['my_classes_count'] = 0

            # Get today's schedule
            try:
                from apps.timetable.models import TimetableEntry
                todays_schedule = TimetableEntry.objects.filter(
                    teacher=teacher,
                    day_of_week=today.strftime('%A').lower(),
                    is_active=True
                ).select_related('subject', 'grade', 'classroom').order_by('start_time')

                context['todays_schedule'] = []
                for entry in todays_schedule:
                    context['todays_schedule'].append({
                        'subject': entry.subject,
                        'grade': entry.grade,
                        'classroom': entry.classroom.name if entry.classroom else 'TBA',
                        'start_time': entry.start_time.strftime('%H:%M'),
                        'end_time': entry.end_time.strftime('%H:%M'),
                        'duration': int((entry.end_time.hour * 60 + entry.end_time.minute) -
                                     (entry.start_time.hour * 60 + entry.start_time.minute))
                    })

                context['active_classes_today'] = todays_schedule.count()
            except:
                context['todays_schedule'] = []
                context['active_classes_today'] = 0
        else:
            context['my_class_assignments'] = []
            context['my_classes_count'] = 0
            context['todays_schedule'] = []
            context['active_classes_today'] = 0

        # Student Management Statistics
        if teacher:
            # Get all students assigned to this teacher
            try:
                teacher_students = Student.objects.filter(
                    current_grade__in=[assignment.grade for assignment in class_assignments],
                    status='active'
                ).distinct()

                context['my_students_count'] = teacher_students.count()

                # Get today's attendance for teacher's students
                students_present_today = StudentAttendance.objects.filter(
                    student__in=teacher_students,
                    date=today,
                    status='present'
                ).count()

                context['students_present_today'] = students_present_today
            except:
                context['my_students_count'] = 0
                context['students_present_today'] = 0
        else:
            context['my_students_count'] = 0
            context['students_present_today'] = 0

        # Exam Management and Grading
        try:
            from apps.exams.models import Exam, ExamResult

            # Upcoming exams for teacher's subjects
            upcoming_exams = Exam.objects.filter(
                subject__in=[assignment.subject for assignment in class_assignments] if teacher else [],
                date__gte=today
            ).select_related('subject', 'grade').order_by('date')[:5]

            context['upcoming_exams'] = []
            for exam in upcoming_exams:
                context['upcoming_exams'].append({
                    'id': exam.id,
                    'subject': exam.subject,
                    'grade': exam.grade,
                    'date': exam.date.strftime('%Y-%m-%d'),
                    'time': exam.start_time.strftime('%H:%M') if exam.start_time else 'TBA',
                    'duration': exam.duration if hasattr(exam, 'duration') else 120,
                    'total_marks': exam.total_marks if hasattr(exam, 'total_marks') else 100
                })

            # Pending grades
            pending_grades = []
            for exam in Exam.objects.filter(
                subject__in=[assignment.subject for assignment in class_assignments] if teacher else [],
                date__lte=today
            ):
                ungraded_count = ExamResult.objects.filter(
                    exam=exam,
                    marks__isnull=True
                ).count()

                if ungraded_count > 0:
                    pending_grades.append({
                        'exam_id': exam.id,
                        'exam_name': f"{exam.subject.name} - {exam.grade.name}",
                        'student_count': ungraded_count
                    })

            context['pending_grades'] = pending_grades
            context['pending_grades_count'] = sum(pg['student_count'] for pg in pending_grades)
            context['grades_due_week'] = len([pg for pg in pending_grades if pg['student_count'] > 0])

        except:
            context['upcoming_exams'] = []
            context['pending_grades'] = []
            context['pending_grades_count'] = 0
            context['grades_due_week'] = 0

        # Communication and Messages
        try:
            from apps.communication.models import Message

            # Unread messages count
            unread_messages = Message.objects.filter(
                recipient=user,
                is_read=False
            ).count()
            context['unread_messages_count'] = unread_messages

            # New messages today
            new_messages_today = Message.objects.filter(
                recipient=user,
                created_at__date=today
            ).count()
            context['new_messages_today'] = new_messages_today

            # Recent messages
            recent_messages = Message.objects.filter(
                recipient=user
            ).select_related('sender').order_by('-created_at')[:5]

            context['recent_messages'] = []
            for message in recent_messages:
                context['recent_messages'].append({
                    'id': message.id,
                    'sender_name': getattr(message.sender, 'get_full_name', lambda: message.sender.email)(),
                    'content': message.content,
                    'timestamp': message.created_at.strftime('%Y-%m-%d %H:%M'),
                    'is_read': message.is_read
                })

        except:
            context['unread_messages_count'] = 0
            context['new_messages_today'] = 0
            context['recent_messages'] = []

        # Attendance Tracking
        # Teacher's own attendance
        context['today_attendance'] = TeacherAttendance.objects.filter(
            teacher=teacher,
            date=today
        ).first() if teacher else None

        # Class attendance statistics
        if teacher:
            try:
                context['class_attendance_stats'] = []
                for assignment in class_assignments:
                    students_in_class = Student.objects.filter(
                        current_grade=assignment.grade,
                        status='active'
                    )

                    present_today = StudentAttendance.objects.filter(
                        student__in=students_in_class,
                        date=today,
                        status='present'
                    ).count()

                    total_students = students_in_class.count()
                    attendance_rate = (present_today / total_students * 100) if total_students > 0 else 0

                    context['class_attendance_stats'].append({
                        'class_name': f"{assignment.grade.name} - {assignment.subject.name}",
                        'present_count': present_today,
                        'total_count': total_students,
                        'attendance_rate': round(attendance_rate, 1)
                    })
            except:
                context['class_attendance_stats'] = []
        else:
            context['class_attendance_stats'] = []

        # Recent Student Activities
        try:
            context['recent_student_activities'] = []
            if teacher:
                # Get recent activities for teacher's students
                teacher_students = Student.objects.filter(
                    current_grade__in=[assignment.grade for assignment in class_assignments],
                    status='active'
                )[:10]

                for student in teacher_students:
                    # Check recent attendance
                    recent_attendance = StudentAttendance.objects.filter(
                        student=student,
                        date__gte=today - timedelta(days=7)
                    ).order_by('-date').first()

                    if recent_attendance:
                        context['recent_student_activities'].append({
                            'student_name': student.full_name,
                            'description': f"Attendance: {recent_attendance.status}",
                            'timestamp': recent_attendance.date.strftime('%Y-%m-%d'),
                            'color': 'green' if recent_attendance.status == 'present' else 'red',
                            'icon': 'check' if recent_attendance.status == 'present' else 'times'
                        })
        except:
            context['recent_student_activities'] = []

        # Petty Cash Requests
        context['petty_cash_requests'] = PettyCash.objects.filter(
            received_by=user.id
        ).order_by('-date')[:5]

        # Performance Metrics (mock data - would be calculated from real metrics)
        try:
            # Calculate teacher's attendance rate
            if teacher:
                teacher_attendance_records = TeacherAttendance.objects.filter(
                    teacher=teacher,
                    date__gte=today - timedelta(days=30)
                )

                present_days = teacher_attendance_records.filter(status='present').count()
                total_days = teacher_attendance_records.count()
                attendance_rate = (present_days / total_days * 100) if total_days > 0 else 0

                context['attendance_rate'] = round(attendance_rate, 1)
            else:
                context['attendance_rate'] = 95  # Default value
        except:
            context['attendance_rate'] = 95  # Default value

        # Mock performance data (would be calculated from real metrics)
        context['class_average'] = 78  # Average class performance
        context['grading_efficiency'] = 92  # Percentage of grades submitted on time
        context['parent_satisfaction'] = 88  # Parent feedback rating

        return context

class StudentDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/student_dashboard.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        context['feedback_form'] = FeedbackForm()
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        if 'feedback_submit' in request.POST:
            form = FeedbackForm(request.POST)
            if form.is_valid():
                feedback = form.save(commit=False)
                feedback.user = request.user
                feedback.save()
                messages.success(request, 'Feedback submitted!')
                return redirect('dashboard:student_dashboard')
        return self.get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['school_info'] = SchoolInfo.objects.first()
        user = self.request.user
        today = timezone.now().date()

        # Get student profile
        try:
            student = Student.objects.get(email=user.email)
            context['student_profile'] = student
        except Student.DoesNotExist:
            student = None
            context['student_profile'] = None

        # Personal Academic Information
        if student:
            # Current subjects
            try:
                from apps.academics.models import SubjectAssignment
                subject_assignments = SubjectAssignment.objects.filter(
                    grade=student.current_grade,
                    is_active=True
                ).select_related('subject', 'teacher')

                context['current_subjects'] = []
                for assignment in subject_assignments:
                    context['current_subjects'].append({
                        'name': assignment.subject.name,
                        'teacher_name': assignment.teacher.full_name if assignment.teacher else 'TBA'
                    })
            except:
                context['current_subjects'] = []

            # Today's classes/schedule
            try:
                from apps.timetable.models import TimetableEntry
                todays_classes = TimetableEntry.objects.filter(
                    grade=student.current_grade,
                    day_of_week=today.strftime('%A').lower(),
                    is_active=True
                ).select_related('subject', 'teacher', 'classroom').order_by('start_time')

                context['todays_classes'] = []
                for entry in todays_classes:
                    context['todays_classes'].append({
                        'subject': entry.subject,
                        'teacher_name': entry.teacher.full_name if entry.teacher else 'TBA',
                        'classroom': entry.classroom.name if entry.classroom else 'TBA',
                        'start_time': entry.start_time.strftime('%H:%M'),
                        'end_time': entry.end_time.strftime('%H:%M'),
                        'duration': int((entry.end_time.hour * 60 + entry.end_time.minute) -
                                     (entry.start_time.hour * 60 + entry.start_time.minute))
                    })
            except:
                context['todays_classes'] = []
        else:
            context['current_subjects'] = []
            context['todays_classes'] = []

        # Attendance Tracking
        if student:
            # Attendance summary (last 30 days)
            attendance_records = StudentAttendance.objects.filter(
                student=student,
                date__gte=today - timedelta(days=30)
            ).order_by('-date')

            present_days = attendance_records.filter(status='present').count()
            absent_days = attendance_records.filter(status='absent').count()
            total_days = attendance_records.count()

            context['attendance_summary'] = {
                'present': present_days,
                'absent': absent_days,
                'total': total_days,
                'percentage': (present_days / total_days * 100) if total_days > 0 else 0
            }

            # Recent attendance (last 7 days)
            recent_attendance = attendance_records.filter(
                date__gte=today - timedelta(days=7)
            )[:7]

            context['recent_attendance'] = []
            for record in recent_attendance:
                context['recent_attendance'].append({
                    'date': record.date,
                    'status': record.status
                })
        else:
            context['attendance_summary'] = {
                'present': 0,
                'absent': 0,
                'total': 0,
                'percentage': 0
            }
            context['recent_attendance'] = []

        # Academic Performance
        if student:
            try:
                from apps.exams.models import ExamResult

                # Calculate GPA and academic performance
                recent_results = ExamResult.objects.filter(
                    student=student,
                    exam__date__gte=today - timedelta(days=90)  # Last 3 months
                )

                if recent_results.exists():
                    total_marks = sum(result.marks for result in recent_results)
                    total_possible = sum(result.total_marks for result in recent_results)
                    gpa = (total_marks / total_possible * 4.0) if total_possible > 0 else 0.0

                    # Calculate rank (simplified)
                    all_students_avg = ExamResult.objects.filter(
                        exam__date__gte=today - timedelta(days=90),
                        student__current_grade=student.current_grade
                    ).values('student').annotate(
                        avg_percentage=models.Avg(
                            models.F('marks') * 100.0 / models.F('total_marks')
                        )
                    ).order_by('-avg_percentage')

                    student_avg = (total_marks / total_possible * 100) if total_possible > 0 else 0
                    rank = 1
                    for i, student_data in enumerate(all_students_avg):
                        if student_data['avg_percentage'] <= student_avg:
                            rank = i + 1
                            break

                    context['academic_performance'] = {
                        'gpa': gpa,
                        'rank': rank
                    }
                else:
                    context['academic_performance'] = {
                        'gpa': 0.0,
                        'rank': 1
                    }
            except:
                context['academic_performance'] = {
                    'gpa': 0.0,
                    'rank': 1
                }
        else:
            context['academic_performance'] = {
                'gpa': 0.0,
                'rank': 1
            }

        # Exam Schedules and Results
        if student:
            try:
                from apps.exams.models import Exam, ExamResult

                # Upcoming exams
                upcoming_exams = Exam.objects.filter(
                    grade=student.current_grade,
                    date__gte=today
                ).order_by('date')[:5]

                context['upcoming_exams'] = []
                for exam in upcoming_exams:
                    context['upcoming_exams'].append({
                        'subject': exam.subject,
                        'exam_type': getattr(exam, 'exam_type', 'Regular Exam'),
                        'date': exam.date,
                        'start_time': getattr(exam, 'start_time', None),
                        'duration': getattr(exam, 'duration', 120),
                        'total_marks': getattr(exam, 'total_marks', 100),
                        'classroom': getattr(exam, 'classroom', 'TBA')
                    })

                context['upcoming_exams_count'] = upcoming_exams.count()
                context['next_exam_date'] = upcoming_exams.first().date if upcoming_exams.exists() else None

                # Recent exam results
                recent_results = ExamResult.objects.filter(
                    student=student
                ).select_related('exam', 'subject').order_by('-exam__date')[:5]

                context['recent_exam_results'] = []
                for result in recent_results:
                    percentage = (result.marks / result.total_marks * 100) if result.total_marks > 0 else 0
                    context['recent_exam_results'].append({
                        'subject': result.subject,
                        'exam_name': f"{result.exam.name}" if hasattr(result.exam, 'name') else f"{result.subject.name} Exam",
                        'marks': result.marks,
                        'total_marks': result.total_marks,
                        'percentage': percentage,
                        'grade': result.grade if hasattr(result, 'grade') else self.calculate_grade(percentage),
                        'exam_date': result.exam.date
                    })

            except:
                context['upcoming_exams'] = []
                context['upcoming_exams_count'] = 0
                context['next_exam_date'] = None
                context['recent_exam_results'] = []
        else:
            context['upcoming_exams'] = []
            context['upcoming_exams_count'] = 0
            context['next_exam_date'] = None
            context['recent_exam_results'] = []

        # Fee Payment Tracking
        if student:
            try:
                from apps.finance.models import FeePayment, FeeStructure

                # Fee summary
                total_paid = FeePayment.objects.filter(
                    student=student,
                    payment_date__year=today.year,
                    status='completed'
                ).aggregate(total=models.Sum('amount'))['total'] or 0

                pending_fees = FeeStructure.objects.filter(
                    student=student,
                    status='pending'
                ).aggregate(total=models.Sum('amount'))['total'] or 0

                # Get next due date
                next_due = FeeStructure.objects.filter(
                    student=student,
                    status='pending',
                    due_date__gte=today
                ).order_by('due_date').first()

                context['fee_summary'] = {
                    'total_paid': total_paid,
                    'pending': pending_fees,
                    'due_date': next_due.due_date if next_due else None
                }

                # Recent payments
                recent_payments = FeePayment.objects.filter(
                    student=student
                ).order_by('-payment_date')[:5]

                context['recent_payments'] = []
                for payment in recent_payments:
                    context['recent_payments'].append({
                        'amount': payment.amount,
                        'fee_type': payment.fee_type if hasattr(payment, 'fee_type') else 'School Fee',
                        'payment_date': payment.payment_date,
                        'status': payment.status,
                        'receipt_number': getattr(payment, 'receipt_number', None)
                    })

            except:
                context['fee_summary'] = {
                    'total_paid': 0,
                    'pending': 0,
                    'due_date': None
                }
                context['recent_payments'] = []
        else:
            context['fee_summary'] = {
                'total_paid': 0,
                'pending': 0,
                'due_date': None
            }
            context['recent_payments'] = []

        # Communication with Teachers and Peers
        try:
            from apps.communication.models import Message

            # Recent messages
            recent_messages = Message.objects.filter(
                recipient=user
            ).select_related('sender').order_by('-created_at')[:5]

            context['recent_messages'] = []
            for message in recent_messages:
                context['recent_messages'].append({
                    'id': message.id,
                    'sender_name': message.sender.get_full_name(),
                    'content': message.content,
                    'timestamp': message.created_at.strftime('%Y-%m-%d %H:%M'),
                    'is_read': message.is_read
                })

        except:
            context['recent_messages'] = []

        # Academic Resources and Additional Data
        if student:
            # Pending assignments count (mock data)
            context['pending_assignments_count'] = 3  # Would be calculated from assignments model

            # Borrowed books count (mock data)
            try:
                from apps.library.models import BookBorrowing
                context['borrowed_books_count'] = BookBorrowing.objects.filter(
                    student=student,
                    return_date__isnull=True
                ).count()
            except:
                context['borrowed_books_count'] = 0

            # Unread announcements count (mock data)
            context['unread_announcements_count'] = 2  # Would be calculated from announcements model

            # Study groups
            try:
                from apps.academics.models import StudyGroup
                study_groups = StudyGroup.objects.filter(
                    members=student,
                    is_active=True
                )[:3]

                context['study_groups'] = []
                for group in study_groups:
                    context['study_groups'].append({
                        'name': group.name,
                        'subject': group.subject.name if group.subject else 'General',
                        'member_count': group.members.count()
                    })
            except:
                context['study_groups'] = []
        else:
            context['pending_assignments_count'] = 0
            context['borrowed_books_count'] = 0
            context['unread_announcements_count'] = 0
            context['study_groups'] = []

        return context

    def calculate_grade(self, percentage):
        """Calculate letter grade from percentage"""
        if percentage >= 90:
            return 'A+'
        elif percentage >= 80:
            return 'A'
        elif percentage >= 70:
            return 'B'
        elif percentage >= 60:
            return 'C'
        elif percentage >= 50:
            return 'D'
        else:
            return 'F'

class ParentDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/parent_dashboard.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        context['feedback_form'] = FeedbackForm()
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        if 'feedback_submit' in request.POST:
            form = FeedbackForm(request.POST)
            if form.is_valid():
                feedback = form.save(commit=False)
                feedback.user = request.user
                feedback.save()
                messages.success(request, 'Feedback submitted!')
                return redirect('dashboard:parent_dashboard')
        return self.get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['school_info'] = SchoolInfo.objects.first()
        user = self.request.user
        today = timezone.now().date()

        # Get parent-child relationships
        try:
            from apps.accounts.models import ParentChildRelationship
            relationships = ParentChildRelationship.objects.filter(
                parent=user
            ).select_related('student')
            children = [rel.student for rel in relationships]
        except:
            children = []
            relationships = []

        context['children'] = children
        context['relationships'] = relationships

        # Monitor Child's Academic Progress
        if children:
            # Academic performance for each child
            for child in children:
                try:
                    from apps.exams.models import ExamResult

                    # Get subject performances
                    subject_performances = []
                    recent_results = ExamResult.objects.filter(
                        student=child,
                        exam__date__gte=today - timedelta(days=90)
                    ).select_related('subject', 'exam')

                    # Group by subject
                    subject_results = {}
                    for result in recent_results:
                        subject_name = result.subject.name
                        if subject_name not in subject_results:
                            subject_results[subject_name] = []
                        subject_results[subject_name].append(result)

                    for subject_name, results in subject_results.items():
                        if results:
                            latest_result = results[0]  # Most recent
                            avg_percentage = sum(
                                (r.marks / r.total_marks * 100) for r in results
                            ) / len(results)

                            subject_performances.append({
                                'subject_name': subject_name,
                                'latest_grade': self.calculate_grade(
                                    latest_result.marks / latest_result.total_marks * 100
                                ),
                                'percentage': round(avg_percentage, 1)
                            })

                    child.subject_performances = subject_performances

                    # Calculate overall GPA
                    if recent_results:
                        total_marks = sum(r.marks for r in recent_results)
                        total_possible = sum(r.total_marks for r in recent_results)
                        gpa = (total_marks / total_possible * 4.0) if total_possible > 0 else 0.0
                        child.current_gpa = round(gpa, 2)
                    else:
                        child.current_gpa = None

                except:
                    child.subject_performances = []
                    child.current_gpa = None

        # View Attendance, Exam Results, and Fee Information
        child_attendance = []
        child_fees = []
        recent_exam_results = []

        for child in children:
            # Attendance data
            try:
                from apps.attendance.models import StudentAttendance

                # Last 30 days attendance
                attendance_records = StudentAttendance.objects.filter(
                    student=child,
                    date__gte=today - timedelta(days=30)
                ).order_by('-date')

                present_days = attendance_records.filter(status='present').count()
                absent_days = attendance_records.filter(status='absent').count()
                total_days = attendance_records.count()
                attendance_rate = (present_days / total_days * 100) if total_days > 0 else 0

                # Recent 7 days for visual display
                recent_7_days = attendance_records[:7]
                recent_attendance = []
                for record in recent_7_days:
                    recent_attendance.append({
                        'date': record.date,
                        'status': record.status
                    })

                # Last attendance status
                last_attendance = attendance_records.first()
                last_status = last_attendance.status if last_attendance else None

                child_attendance.append({
                    'student': child,
                    'attendance_rate': round(attendance_rate, 1),
                    'present_days': present_days,
                    'absent_days': absent_days,
                    'total_days': total_days,
                    'recent_attendance': recent_attendance,
                    'last_attendance_status': last_status,
                    'recent_gpa': child.current_gpa,
                    'pending_fees': 0  # Will be calculated below
                })

            except:
                child_attendance.append({
                    'student': child,
                    'attendance_rate': 0,
                    'present_days': 0,
                    'absent_days': 0,
                    'total_days': 0,
                    'recent_attendance': [],
                    'last_attendance_status': None,
                    'recent_gpa': child.current_gpa,
                    'pending_fees': 0
                })

            # Fee information
            try:
                from apps.finance.models import FeePayment, FeeStructure

                # Total paid this year
                total_paid = FeePayment.objects.filter(
                    student=child,
                    payment_date__year=today.year,
                    status='completed'
                ).aggregate(total=models.Sum('amount'))['total'] or 0

                # Pending fees
                pending_amount = FeeStructure.objects.filter(
                    student=child,
                    status='pending'
                ).aggregate(total=models.Sum('amount'))['total'] or 0

                # Update pending fees in attendance data
                for attendance_data in child_attendance:
                    if attendance_data['student'] == child:
                        attendance_data['pending_fees'] = pending_amount
                        break

                # Next due date
                next_due = FeeStructure.objects.filter(
                    student=child,
                    status='pending',
                    due_date__gte=today
                ).order_by('due_date').first()

                # Recent payments
                recent_payments = FeePayment.objects.filter(
                    student=child
                ).order_by('-payment_date')[:3]

                payment_list = []
                for payment in recent_payments:
                    payment_list.append({
                        'amount': payment.amount,
                        'fee_type': getattr(payment, 'fee_type', 'School Fee'),
                        'status': payment.status
                    })

                child_fees.append({
                    'student': child,
                    'total_paid': total_paid,
                    'pending_amount': pending_amount,
                    'next_due_date': next_due.due_date if next_due else None,
                    'recent_payments': payment_list
                })

            except:
                child_fees.append({
                    'student': child,
                    'total_paid': 0,
                    'pending_amount': 0,
                    'next_due_date': None,
                    'recent_payments': []
                })

            # Recent exam results
            try:
                from apps.exams.models import ExamResult

                child_results = ExamResult.objects.filter(
                    student=child
                ).select_related('exam', 'subject').order_by('-exam__date')[:3]

                for result in child_results:
                    percentage = (result.marks / result.total_marks * 100) if result.total_marks > 0 else 0
                    recent_exam_results.append({
                        'student_name': child.full_name,
                        'subject_name': result.subject.name,
                        'exam_name': getattr(result.exam, 'name', f"{result.subject.name} Exam"),
                        'marks': result.marks,
                        'total_marks': result.total_marks,
                        'percentage': round(percentage, 1),
                        'grade': self.calculate_grade(percentage),
                        'exam_date': result.exam.date,
                        'class_rank': getattr(result, 'class_rank', None)
                    })

            except:
                pass

        context['child_attendance'] = child_attendance
        context['child_fees'] = child_fees
        context['recent_exam_results'] = recent_exam_results

        # Communication with Teachers
        try:
            from apps.communication.models import Message

            # Recent communications with teachers
            recent_communications = Message.objects.filter(
                Q(sender=user) | Q(recipient=user),
                sender__type='teacher'
            ).select_related('sender', 'recipient').order_by('-created_at')[:5]

            context['recent_communications'] = []
            for comm in recent_communications:
                # Determine which child this communication is about
                child_name = "General"
                if children:
                    child_name = children[0].full_name  # Default to first child

                context['recent_communications'].append({
                    'id': comm.id,
                    'teacher_name': comm.sender.get_full_name() if comm.sender.type == 'teacher' else comm.recipient.get_full_name(),
                    'subject': getattr(comm, 'subject', 'General'),
                    'message': comm.content,
                    'timestamp': comm.created_at.strftime('%Y-%m-%d %H:%M'),
                    'child_name': child_name
                })

        except:
            context['recent_communications'] = []

        # Parent Resources and Additional Data
        # Total pending homework across all children
        context['total_pending_homework'] = len(children) * 2  # Mock data

        # Upcoming events
        try:
            from apps.school_calendar.models import Event
            upcoming_events = Event.objects.filter(
                date__gte=today,
                date__lte=today + timedelta(days=30)
            ).order_by('date')[:5]

            context['upcoming_events'] = []
            for event in upcoming_events:
                context['upcoming_events'].append({
                    'title': event.title,
                    'description': event.description,
                    'date': event.date
                })
        except:
            context['upcoming_events'] = []

        return context

    def calculate_grade(self, percentage):
        """Calculate letter grade from percentage"""
        if percentage >= 90:
            return 'A+'
        elif percentage >= 80:
            return 'A'
        elif percentage >= 70:
            return 'B'
        elif percentage >= 60:
            return 'C'
        elif percentage >= 50:
            return 'D'
        else:
            return 'F'

class AccountantDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/accountant_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['school_info'] = SchoolInfo.objects.first()
        context['total_orders'] = Order.objects.count()
        context['completed_orders'] = Order.objects.filter(status='completed').count()
        context['pending_orders'] = Order.objects.filter(status='pending').count()
        context['recent_orders'] = Order.objects.select_related('user').order_by('-created_at')[:10]
        return context
