// Real-time Notifications System
class NotificationManager {
    constructor() {
        this.notifications = [];
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.init();
    }

    init() {
        this.setupWebSocket();
        this.setupEventListeners();
        this.loadStoredNotifications();
        this.requestNotificationPermission();
    }

    setupWebSocket() {
        // In a real implementation, this would connect to a WebSocket server
        // For now, we'll simulate with polling
        this.startPolling();
    }

    startPolling() {
        // Poll for new notifications every 30 seconds
        setInterval(() => {
            this.fetchNotifications();
        }, 30000);
        
        // Initial fetch
        this.fetchNotifications();
    }

    async fetchNotifications() {
        try {
            // Simulate API call
            const response = await fetch('/api/notifications/', {
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.processNewNotifications(data.notifications);
            }
        } catch (error) {
            console.error('Failed to fetch notifications:', error);
        }
    }

    processNewNotifications(newNotifications) {
        newNotifications.forEach(notification => {
            if (!this.notifications.find(n => n.id === notification.id)) {
                this.addNotification(notification);
            }
        });
    }

    addNotification(notification) {
        // Add timestamp if not present
        if (!notification.timestamp) {
            notification.timestamp = new Date().toISOString();
        }

        // Add to notifications array
        this.notifications.unshift(notification);

        // Limit to 50 notifications
        if (this.notifications.length > 50) {
            this.notifications = this.notifications.slice(0, 50);
        }

        // Show notification
        this.showNotification(notification);

        // Update UI
        this.updateNotificationUI();

        // Store in localStorage
        this.storeNotifications();

        // Send browser notification if permission granted
        this.sendBrowserNotification(notification);
    }

    showNotification(notification) {
        // Create toast notification
        this.createToast(notification);

        // Update notification badge
        this.updateNotificationBadge();

        // Play sound if enabled
        this.playNotificationSound(notification);
    }

    createToast(notification) {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 ${this.getNotificationColor(notification.type)} transform translate-x-full transition-transform duration-300 ease-in-out`;
        
        toast.innerHTML = `
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas ${this.getNotificationIcon(notification.type)} text-lg ${this.getNotificationTextColor(notification.type)}"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900">
                            ${notification.title}
                        </p>
                        <p class="mt-1 text-sm text-gray-500">
                            ${notification.message}
                        </p>
                        <p class="mt-1 text-xs text-gray-400">
                            ${this.formatTime(notification.timestamp)}
                        </p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }

    updateNotificationBadge() {
        const unreadCount = this.notifications.filter(n => !n.read).length;
        const badges = document.querySelectorAll('.notification-badge');
        
        badges.forEach(badge => {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        });
    }

    updateNotificationUI() {
        // Update notification panel
        const notificationPanel = document.getElementById('notification-panel');
        if (notificationPanel) {
            this.renderNotificationPanel(notificationPanel);
        }

        // Update notification dropdown
        const notificationDropdown = document.getElementById('notification-dropdown');
        if (notificationDropdown) {
            this.renderNotificationDropdown(notificationDropdown);
        }
    }

    renderNotificationPanel(container) {
        const unreadNotifications = this.notifications.filter(n => !n.read).slice(0, 10);
        
        container.innerHTML = `
            <div class="p-4 border-b">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold">Notifications</h3>
                    <button onclick="notificationManager.markAllAsRead()" class="text-sm text-purple-600 hover:text-purple-800">
                        Mark all as read
                    </button>
                </div>
            </div>
            <div class="max-h-96 overflow-y-auto">
                ${unreadNotifications.length > 0 ? 
                    unreadNotifications.map(n => this.renderNotificationItem(n)).join('') :
                    '<div class="p-4 text-center text-gray-500">No new notifications</div>'
                }
            </div>
            <div class="p-4 border-t">
                <a href="/notifications/" class="text-sm text-purple-600 hover:text-purple-800">
                    View all notifications
                </a>
            </div>
        `;
    }

    renderNotificationItem(notification) {
        return `
            <div class="p-4 border-b hover:bg-gray-50 cursor-pointer ${notification.read ? 'opacity-60' : ''}" 
                 onclick="notificationManager.markAsRead('${notification.id}')">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas ${this.getNotificationIcon(notification.type)} ${this.getNotificationTextColor(notification.type)}"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                            ${notification.title}
                        </p>
                        <p class="text-sm text-gray-500 truncate">
                            ${notification.message}
                        </p>
                        <p class="text-xs text-gray-400 mt-1">
                            ${this.formatTime(notification.timestamp)}
                        </p>
                    </div>
                    ${!notification.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                </div>
            </div>
        `;
    }

    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.updateNotificationUI();
            this.storeNotifications();
            
            // Send to server
            this.sendReadStatus(notificationId);
        }
    }

    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.updateNotificationUI();
        this.storeNotifications();
        
        // Send to server
        this.sendBulkReadStatus();
    }

    async sendReadStatus(notificationId) {
        try {
            await fetch(`/api/notifications/${notificationId}/read/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
        }
    }

    async sendBulkReadStatus() {
        try {
            await fetch('/api/notifications/mark-all-read/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Failed to mark all notifications as read:', error);
        }
    }

    sendBrowserNotification(notification) {
        if (Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/static/images/notification-icon.png',
                tag: notification.id
            });

            browserNotification.onclick = () => {
                window.focus();
                this.markAsRead(notification.id);
                browserNotification.close();
            };

            // Auto close after 5 seconds
            setTimeout(() => {
                browserNotification.close();
            }, 5000);
        }
    }

    playNotificationSound(notification) {
        // Only play sound for high priority notifications
        if (notification.priority === 'high' || notification.priority === 'critical') {
            const audio = new Audio('/static/sounds/notification.mp3');
            audio.volume = 0.3;
            audio.play().catch(e => {
                // Ignore audio play errors (user interaction required)
            });
        }
    }

    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    getNotificationColor(type) {
        const colors = {
            'info': 'border-blue-400',
            'success': 'border-green-400',
            'warning': 'border-yellow-400',
            'error': 'border-red-400',
            'critical': 'border-red-600'
        };
        return colors[type] || 'border-gray-400';
    }

    getNotificationTextColor(type) {
        const colors = {
            'info': 'text-blue-500',
            'success': 'text-green-500',
            'warning': 'text-yellow-500',
            'error': 'text-red-500',
            'critical': 'text-red-600'
        };
        return colors[type] || 'text-gray-500';
    }

    getNotificationIcon(type) {
        const icons = {
            'info': 'fa-info-circle',
            'success': 'fa-check-circle',
            'warning': 'fa-exclamation-triangle',
            'error': 'fa-times-circle',
            'critical': 'fa-exclamation-circle'
        };
        return icons[type] || 'fa-bell';
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        return date.toLocaleDateString();
    }

    storeNotifications() {
        localStorage.setItem('notifications', JSON.stringify(this.notifications));
    }

    loadStoredNotifications() {
        const stored = localStorage.getItem('notifications');
        if (stored) {
            this.notifications = JSON.parse(stored);
            this.updateNotificationUI();
        }
    }

    setupEventListeners() {
        // Listen for custom notification events
        document.addEventListener('add-notification', (event) => {
            this.addNotification(event.detail);
        });

        // Listen for page visibility changes to update notifications
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.fetchNotifications();
            }
        });
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Initialize notification manager
const notificationManager = new NotificationManager();

// Global function to add notifications
window.addNotification = function(notification) {
    notificationManager.addNotification(notification);
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
