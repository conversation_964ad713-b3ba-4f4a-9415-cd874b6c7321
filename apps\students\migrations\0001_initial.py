# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('index_number', models.BigIntegerField(unique=True)),
                ('full_name', models.CharField(max_length=255)),
                ('TSC_number', models.Char<PERSON>ield(max_length=255)),
                ('i_name', models.Char<PERSON><PERSON>(max_length=255)),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], max_length=255)),
                ('address', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('phone', models.Char<PERSON><PERSON>(max_length=255)),
                ('email', models.<PERSON><PERSON><PERSON><PERSON>(max_length=254)),
                ('image_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('b_date', models.DateField()),
                ('nationality', models.CharField(blank=True, choices=[('Kenyan', 'Kenyan'), ('Other', 'Other')], max_length=50, null=True)),
                ('religion', models.CharField(blank=True, max_length=50, null=True)),
                ('boarding_status', models.CharField(choices=[('Day', 'Day'), ('Boarding', 'Boarding')], default='Day', max_length=50)),
                ('kcpe_index', models.CharField(blank=True, max_length=50, null=True)),
                ('kcpe_score', models.IntegerField(blank=True, null=True)),
                ('kcpe_year', models.PositiveIntegerField(blank=True, null=True)),
                ('primary_school', models.CharField(blank=True, max_length=255, null=True)),
                ('previous_school', models.CharField(blank=True, max_length=255, null=True)),
                ('transfer_status', models.CharField(blank=True, max_length=50, null=True)),
                ('health_conditions', models.TextField(blank=True, null=True)),
                ('emergency_contact', models.CharField(blank=True, max_length=255, null=True)),
                ('_status', models.CharField(max_length=255)),
                ('reg_year', models.PositiveIntegerField()),
                ('reg_month', models.CharField(max_length=255)),
                ('reg_date', models.DateField()),
            ],
        ),
        migrations.CreateModel(
            name='StudentGrade',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('index_number', models.BigIntegerField()),
                ('section', models.CharField(blank=True, max_length=50, null=True)),
                ('year', models.PositiveIntegerField()),
                ('term', models.IntegerField(default=1)),
                ('status', models.CharField(choices=[('active', 'Active'), ('graduated', 'Graduated'), ('transferred', 'Transferred')], default='active', max_length=50)),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.grade')),
            ],
        ),
        migrations.CreateModel(
            name='StudentSubject',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('index_number', models.BigIntegerField()),
                ('_status', models.CharField(max_length=255)),
                ('year', models.IntegerField()),
                ('term', models.IntegerField(default=1)),
                ('reg_month', models.CharField(max_length=255)),
                ('sr', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subjectrouting')),
            ],
        ),
    ]
