{% extends 'teachers/base.html' %}
{% load static %}

{% block teachers_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">My Students</h1>
            <p class="mt-2 text-gray-600">Manage students in your assigned classes and track their progress</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
            <button onclick="exportStudentList()" class="action-button btn-success">
                <i class="fas fa-download mr-2"></i>Export List
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-users text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ total_students }}</div>
            <div class="text-sm opacity-90">Total Students</div>
            <div class="text-xs opacity-75 mt-1">In Assigned Classes</div>
        </div>

        <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-chalkboard-teacher text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ assigned_grades.count }}</div>
            <div class="text-sm opacity-90">Assigned Classes</div>
            <div class="text-xs opacity-75 mt-1">Current Academic Year</div>
        </div>

        <div class="stats-card" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-book text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ assignments.count }}</div>
            <div class="text-sm opacity-90">Subject Assignments</div>
            <div class="text-xs opacity-75 mt-1">Active</div>
        </div>

        <div class="stats-card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="flex items-center justify-center mb-3">
                <i class="fas fa-star text-3xl"></i>
            </div>
            <div class="text-2xl font-bold mb-1">{{ class_teacher_grades.count }}</div>
            <div class="text-sm opacity-90">Class Teacher</div>
            <div class="text-xs opacity-75 mt-1">Responsibilities</div>
        </div>
    </div>

    <!-- Subject Assignments Overview -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Teaching Assignments</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for assignment in assignments %}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-gray-900">{{ assignment.subject.name }}</h3>
                    <span class="text-sm text-gray-500">{{ assignment.grade.name }}</span>
                </div>
                <p class="text-sm text-gray-600 mb-3">{{ assignment.subject.description|truncatechars:60 }}</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-500">
                        {{ assignment.weekly_periods }} periods/week
                    </span>
                    <a href="{% url 'teachers:class_progress' assignment.grade.id %}" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View Progress
                    </a>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-8 text-gray-500">
                <i class="fas fa-chalkboard-teacher text-4xl mb-4"></i>
                <p>No teaching assignments found for the current academic year.</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Students by Class -->
    {% for grade, students in grade_students.items %}
    <div class="bg-white rounded-lg shadow-md mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">{{ grade.name }}</h2>
                    <p class="text-gray-600">{{ students.count }} students</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'teachers:class_progress' grade.id %}" 
                       class="text-blue-600 hover:text-blue-800 font-medium">
                        <i class="fas fa-chart-line mr-1"></i>Class Progress
                    </a>
                    {% if teacher.is_class_teacher and grade in class_teacher_grades %}
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        Class Teacher
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for student in students %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    {% if student.photo %}
                                    <img class="h-10 w-10 rounded-full object-cover" src="{{ student.photo.url }}" alt="{{ student.get_full_name }}">
                                    {% else %}
                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-user text-gray-500"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ student.get_full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ student.date_of_birth|date:"M d, Y"|default:"DOB not set" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ student.student_id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ student.gender|default:"Not specified" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if student.status == 'active' %}bg-green-100 text-green-800
                                {% elif student.status == 'inactive' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ student.status|capfirst }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'teachers:student_detail' student.id %}" 
                               class="text-blue-600 hover:text-blue-900 mr-3">View Details</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            No students found in this class.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% empty %}
    <div class="bg-white rounded-lg shadow-md p-8 text-center">
        <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Students Found</h3>
        <p class="text-gray-600">You don't have any active teaching assignments for the current academic year.</p>
    </div>
    {% endfor %}

    <!-- Recent Results Summary -->
    {% if recent_results %}
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Exam Results</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marks</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for result in recent_results %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ result.student.get_full_name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ result.timetable_entry.subject.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ result.timetable_entry.exam.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span class="font-medium">{{ result.marks_obtained }}%</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ result.entry_date|date:"M d, Y" }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

<script>
function exportStudentList() {
    // Implementation for exporting student list
    alert('Export functionality will be implemented');
}
</script>
{% endblock %}
