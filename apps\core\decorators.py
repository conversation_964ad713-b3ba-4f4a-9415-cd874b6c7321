from functools import wraps
from django.shortcuts import redirect

def role_required(role):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated or request.user.type != role:
                return redirect('/')
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

admin_required = role_required('admin')
teacher_required = role_required('teacher')
student_required = role_required('student')
parent_required = role_required('parent')
accountant_required = role_required('accountant') 