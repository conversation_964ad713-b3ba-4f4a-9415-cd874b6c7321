from django.urls import path
from . import views

app_name = 'finance'

urlpatterns = [
    # Dashboard
    path('', views.finance_dashboard, name='dashboard'),

    # Fee Structure Management
    path('fee-structures/', views.fee_structure_list, name='fee_structure_list'),
    path('fee-structures/add/', views.add_fee_structure, name='add_fee_structure'),
    path('fee-structures/<int:pk>/edit/', views.edit_fee_structure, name='edit_fee_structure'),
    path('fee-structures/<int:pk>/delete/', views.delete_fee_structure, name='delete_fee_structure'),

    # Invoice Management
    path('invoices/', views.invoice_list, name='invoice_list'),
    path('invoices/generate/', views.generate_invoice, name='generate_invoice'),
    path('invoices/<int:pk>/', views.invoice_detail, name='invoice_detail'),
    path('invoices/<int:invoice_id>/send-reminder/', views.send_payment_reminder, name='send_payment_reminder'),

    # Payment Management
    path('payments/', views.payment_list, name='payment_list'),
    path('payments/record/', views.record_payment, name='record_payment'),
    path('payments/<int:pk>/', views.payment_detail, name='payment_detail'),

    # Scholarship Management
    path('scholarships/', views.scholarship_list, name='scholarship_list'),
    path('scholarships/add/', views.add_scholarship, name='add_scholarship'),
    path('scholarships/<int:pk>/', views.scholarship_detail, name='scholarship_detail'),
    path('scholarships/<int:pk>/edit/', views.edit_scholarship, name='edit_scholarship'),
    path('scholarships/<int:pk>/delete/', views.delete_scholarship, name='delete_scholarship'),
    path('scholarships/<int:pk>/recipients/', views.scholarship_recipients, name='scholarship_recipients'),
    path('scholarships/applications/', views.scholarship_applications, name='scholarship_applications'),

    # Financial Reports and Tools
    path('outstanding-balances/', views.outstanding_balances, name='outstanding_balances'),
    path('payment-calculator/', views.payment_calculator, name='payment_calculator'),
    path('reports/', views.financial_reports, name='financial_reports'),

    # Settings
    path('fee-categories/', views.fee_categories, name='fee_categories'),
    path('payment-methods/', views.payment_methods, name='payment_methods'),

    # Salary Management
    path('salary/', views.salary_management, name='salary_management'),
]
