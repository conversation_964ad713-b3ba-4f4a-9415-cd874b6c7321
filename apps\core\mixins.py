from django.shortcuts import redirect
from django.contrib.auth.mixins import AccessMixin

class RoleRequiredMixin(AccessMixin):
    role = None
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated or request.user.type != self.role:
            return redirect('/')
        return super().dispatch(request, *args, **kwargs)

class AdminRequiredMixin(RoleRequiredMixin):
    role = 'admin'

class TeacherRequiredMixin(RoleRequiredMixin):
    role = 'teacher'

class StudentRequiredMixin(RoleRequiredMixin):
    role = 'student'

class ParentRequiredMixin(RoleRequiredMixin):
    role = 'parent'

class AccountantRequiredMixin(RoleRequiredMixin):
    role = 'accountant' 