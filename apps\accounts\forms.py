from django import forms
from django.contrib.auth import authenticate
from django.utils import timezone
from .models import User, LoginAttempt, ParentChildRelationship

class LoginForm(forms.Form):
    email = forms.EmailField(label='Email', max_length=255, widget=forms.EmailInput(attrs={'autocomplete': 'email'}))
    password = forms.CharField(label='Password', widget=forms.PasswordInput(attrs={'autocomplete': 'current-password'}))

    def clean(self):
        cleaned_data = super().clean()
        email = cleaned_data.get('email')
        password = cleaned_data.get('password')

        if email and password:
            try:
                user = User.objects.get(email=email)

                # Check if account is locked
                if user.is_account_locked():
                    lockout_time = user.account_locked_until.strftime('%Y-%m-%d %H:%M:%S')
                    raise forms.ValidationError(
                        f'Account is locked until {lockout_time} due to multiple failed login attempts. '
                        f'Please contact administrator or try again later.'
                    )

                # Attempt authentication
                authenticated_user = authenticate(email=email, password=password)
                if not authenticated_user:
                    # Increment failed login attempts
                    user.increment_failed_login()
                    remaining_attempts = 5 - user.failed_login_attempts
                    if remaining_attempts > 0:
                        raise forms.ValidationError(
                            f'Invalid email or password. {remaining_attempts} attempts remaining before account lockout.'
                        )
                    else:
                        raise forms.ValidationError(
                            'Account has been locked due to multiple failed login attempts. '
                            'Please contact administrator.'
                        )

                # Check if user is active
                if not authenticated_user.is_active:
                    raise forms.ValidationError('This account is inactive.')

                # Reset failed login attempts on successful authentication
                user.reset_failed_login()
                cleaned_data['user'] = authenticated_user

            except User.DoesNotExist:
                raise forms.ValidationError('Invalid email or password.')

        return cleaned_data

class UserRegistrationForm(forms.ModelForm):
    # Additional fields not in User model
    # first_name = forms.CharField(
    #     max_length=100,
    #     label='First Name',
    #     widget=forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Enter first name'})
    # )
    # last_name = forms.CharField(
    #     max_length=100,
    #     label='Last Name',
    #     widget=forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Enter last name'})
    # )
    # phone = forms.CharField(
    #     max_length=20,
    #     label='Phone Number',
    #     widget=forms.TextInput(attrs={'class': 'form-input', 'placeholder': 'Enter phone number'})
    # )
    password1 = forms.CharField(
        label='Password',
        widget=forms.PasswordInput(attrs={'class': 'form-input', 'placeholder': 'Enter password'})
    )
    password2 = forms.CharField(
        label='Confirm Password',
        widget=forms.PasswordInput(attrs={'class': 'form-input', 'placeholder': 'Confirm password'})
    )

    class Meta:
        model = User
        fields = ('email', 'type')
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-input', 'placeholder': 'Enter email address'}),
            'type': forms.HiddenInput(),
        }

    def clean_password2(self):
        cd = self.cleaned_data
        if cd.get('password1') != cd.get('password2'):
            raise forms.ValidationError('Passwords don\'t match.')
        return cd.get('password2')

    def clean_email(self):
        email = self.cleaned_data['email']
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError('A user with this email already exists.')
        return email


class ParentChildRelationshipForm(forms.ModelForm):
    """
    Form for creating and managing parent-child relationships
    """
    class Meta:
        model = ParentChildRelationship
        fields = [
            'parent', 'student', 'relationship_type',
            'is_primary_contact', 'is_emergency_contact',
            'can_pick_up', 'notes'
        ]
        widgets = {
            'parent': forms.Select(attrs={'class': 'form-select'}),
            'student': forms.Select(attrs={'class': 'form-select'}),
            'relationship_type': forms.Select(attrs={'class': 'form-select'}),
            'is_primary_contact': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'is_emergency_contact': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'can_pick_up': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'notes': forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter parent choices to only show users with 'parent' type
        self.fields['parent'].queryset = User.objects.filter(type='parent')

        # Add help text
        self.fields['is_primary_contact'].help_text = "Primary contact for school communications"
        self.fields['is_emergency_contact'].help_text = "Contact in case of emergencies"
        self.fields['can_pick_up'].help_text = "Authorized to pick up the student"


class AccountUnlockForm(forms.Form):
    """
    Form for administrators to unlock user accounts
    """
    email = forms.EmailField(
        label='User Email',
        widget=forms.EmailInput(attrs={'class': 'form-input'})
    )
    unlock_reason = forms.CharField(
        label='Unlock Reason',
        widget=forms.Textarea(attrs={'class': 'form-textarea', 'rows': 3}),
        help_text='Reason for unlocking this account'
    )

    def clean_email(self):
        email = self.cleaned_data['email']
        try:
            user = User.objects.get(email=email)
            if not user.is_account_locked():
                raise forms.ValidationError('This account is not currently locked.')
            return email
        except User.DoesNotExist:
            raise forms.ValidationError('User with this email does not exist.')


class BulkParentChildLinkForm(forms.Form):
    """
    Form for bulk linking parents to children via CSV upload
    """
    csv_file = forms.FileField(
        label='CSV File',
        widget=forms.FileInput(attrs={'class': 'form-file', 'accept': '.csv'}),
        help_text='Upload CSV with columns: parent_email, student_id, relationship_type'
    )
    overwrite_existing = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        help_text='Overwrite existing parent-child relationships'
    )