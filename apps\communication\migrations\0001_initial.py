# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Chat",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("conversation_id", models.IntegerField()),
                ("grade", models.CharField(max_length=255)),
                ("sender_index", models.BigIntegerField()),
                ("sender_type", models.CharField(max_length=255)),
                ("receiver_index", models.BigIntegerField()),
                ("receiver_type", models.CharField(max_length=255)),
                ("msg", models.TextField()),
                ("date", models.DateField()),
                ("time", models.TimeField()),
                ("_isread", models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="GroupMessage",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("conversation_id", models.IntegerField()),
                ("message", models.TextField()),
                ("sender_index", models.BigIntegerField()),
                ("sender_type", models.CharField(max_length=255)),
                ("group_id", models.IntegerField()),
                ("grade", models.CharField(max_length=255)),
                ("date", models.DateField()),
                ("time", models.TimeField()),
                ("has_attachment", models.BooleanField(default=False)),
                (
                    "attachment_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "attachment_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MyFriends",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("my_index", models.BigIntegerField()),
                ("friend_index", models.BigIntegerField()),
                ("_status", models.CharField(max_length=255)),
                ("conversation_id", models.IntegerField()),
                ("my_type", models.CharField(max_length=255)),
                ("friend_type", models.CharField(max_length=255)),
                ("_isread", models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="OnlineChat",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("conversation_id", models.IntegerField()),
                ("user_index", models.BigIntegerField()),
                ("msg", models.TextField()),
                ("user_type", models.CharField(max_length=255)),
                ("_isread", models.IntegerField()),
                ("date", models.DateField()),
                ("time", models.TimeField()),
                ("has_attachment", models.BooleanField(default=False)),
                (
                    "attachment_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Feedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedbacks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
