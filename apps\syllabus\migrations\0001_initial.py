# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("academics", "0002_initial"),
        ("students", "0001_initial"),
        ("teachers", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AcademicSyllabus",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("file_name", models.CharField(blank=True, max_length=255, null=True)),
                ("file_type", models.CharField(blank=True, max_length=50, null=True)),
                ("file_size", models.IntegerField(blank=True, null=True)),
                ("year", models.PositiveIntegerField()),
                ("term", models.IntegerField()),
                ("upload_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("archived", "Archived")],
                        default="active",
                        max_length=50,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Assignment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("file_name", models.CharField(blank=True, max_length=255, null=True)),
                ("file_type", models.CharField(blank=True, max_length=50, null=True)),
                ("file_size", models.IntegerField(blank=True, null=True)),
                ("assign_date", models.DateField()),
                ("due_date", models.DateField()),
                ("total_marks", models.IntegerField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("expired", "Expired"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=50,
                    ),
                ),
                ("year", models.PositiveIntegerField()),
                ("term", models.IntegerField()),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentAssignment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("submission_date", models.DateTimeField(blank=True, null=True)),
                ("file_name", models.CharField(blank=True, max_length=255, null=True)),
                ("file_type", models.CharField(blank=True, max_length=50, null=True)),
                ("file_size", models.IntegerField(blank=True, null=True)),
                (
                    "marks",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=11, null=True
                    ),
                ),
                ("comments", models.TextField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("submitted", "Submitted"),
                            ("graded", "Graded"),
                            ("late", "Late"),
                        ],
                        default="pending",
                        max_length=50,
                    ),
                ),
                ("graded_by", models.IntegerField(blank=True, null=True)),
                ("graded_date", models.DateTimeField(blank=True, null=True)),
                (
                    "assignment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="syllabus.assignment",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudyMaterial",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("file_name", models.CharField(blank=True, max_length=255, null=True)),
                ("file_type", models.CharField(blank=True, max_length=50, null=True)),
                ("file_size", models.IntegerField(blank=True, null=True)),
                (
                    "material_type",
                    models.CharField(
                        choices=[
                            ("notes", "Notes"),
                            ("presentation", "Presentation"),
                            ("video", "Video"),
                            ("link", "Link"),
                        ],
                        max_length=50,
                    ),
                ),
                ("url", models.CharField(blank=True, max_length=255, null=True)),
                ("upload_date", models.DateField()),
                ("year", models.PositiveIntegerField()),
                ("term", models.IntegerField()),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("archived", "Archived")],
                        default="active",
                        max_length=50,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
            ],
        ),
    ]
