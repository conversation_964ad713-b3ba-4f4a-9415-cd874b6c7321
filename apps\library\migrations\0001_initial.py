# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("students", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="LibraryBook",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("isbn", models.<PERSON>r<PERSON>ield(blank=True, max_length=50, null=True)),
                ("author", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("publisher", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                (
                    "publication_year",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("edition", models.CharField(blank=True, max_length=50, null=True)),
                ("pages", models.IntegerField(blank=True, null=True)),
                ("available_copies", models.Integer<PERSON>ield(default=0)),
                ("total_copies", models.IntegerField(default=0)),
                (
                    "shelf_location",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "cover_image",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                ("date_added", models.DateField()),
                ("added_by", models.IntegerField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("damaged", "Damaged"),
                            ("lost", "Lost"),
                        ],
                        default="available",
                        max_length=50,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="LibraryCategory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="LibraryBorrowing",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("issue_date", models.DateField()),
                ("due_date", models.DateField()),
                ("return_date", models.DateField(blank=True, null=True)),
                (
                    "fine_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=11),
                ),
                ("fine_paid", models.BooleanField(default=False)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("borrowed", "Borrowed"),
                            ("returned", "Returned"),
                            ("overdue", "Overdue"),
                            ("lost", "Lost"),
                        ],
                        default="borrowed",
                        max_length=50,
                    ),
                ),
                ("issued_by", models.IntegerField(blank=True, null=True)),
                ("returned_to", models.IntegerField(blank=True, null=True)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "book",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="library.librarybook",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="students.student",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="librarybook",
            name="category",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="library.librarycategory",
            ),
        ),
    ]
