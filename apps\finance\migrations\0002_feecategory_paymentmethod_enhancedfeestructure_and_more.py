# Generated by Django 5.2.1 on 2025-06-09 10:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0002_initial"),
        ("finance", "0001_initial"),
        ("students", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="FeeCategory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "category_type",
                    models.CharField(
                        choices=[
                            ("tuition", "Tuition Fees"),
                            ("boarding", "Boarding Fees"),
                            ("transport", "Transport Fees"),
                            ("library", "Library Fees"),
                            ("laboratory", "Laboratory Fees"),
                            ("sports", "Sports Fees"),
                            ("examination", "Examination Fees"),
                            ("development", "Development Fees"),
                            ("activity", "Activity Fees"),
                            ("uniform", "Uniform Fees"),
                            ("medical", "Medical Fees"),
                            ("other", "Other Fees"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("is_mandatory", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "color_code",
                    models.CharField(
                        default="#3B82F6", help_text="Hex color for UI", max_length=7
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Fee Categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=100)),
                (
                    "method_type",
                    models.CharField(
                        choices=[
                            ("cash", "Cash"),
                            ("mpesa", "M-Pesa"),
                            ("bank_transfer", "Bank Transfer"),
                            ("cheque", "Cheque"),
                            ("card", "Credit/Debit Card"),
                            ("mobile_money", "Mobile Money"),
                            ("online", "Online Payment"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("requires_reference", models.BooleanField(default=False)),
                (
                    "processing_fee_percentage",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "processing_fee_fixed",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("api_endpoint", models.URLField(blank=True)),
                ("api_key", models.CharField(blank=True, max_length=500)),
                ("merchant_id", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="EnhancedFeeStructure",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200)),
                ("academic_year", models.IntegerField()),
                (
                    "term",
                    models.IntegerField(
                        blank=True, help_text="Leave blank for yearly fees", null=True
                    ),
                ),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Amount"),
                            ("percentage", "Percentage Based"),
                            ("per_unit", "Per Unit"),
                            ("sliding_scale", "Sliding Scale"),
                        ],
                        default="fixed",
                        max_length=20,
                    ),
                ),
                ("base_amount", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "percentage_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "unit_price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "payment_frequency",
                    models.CharField(
                        choices=[
                            ("one_time", "One Time"),
                            ("monthly", "Monthly"),
                            ("termly", "Termly"),
                            ("yearly", "Yearly"),
                        ],
                        default="termly",
                        max_length=20,
                    ),
                ),
                ("due_date", models.DateField()),
                (
                    "late_fee_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "late_fee_percentage",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("grace_period_days", models.IntegerField(default=0)),
                ("is_mandatory", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fee_structures",
                        to="finance.feecategory",
                    ),
                ),
            ],
            options={
                "ordering": ["academic_year", "term", "grade", "category"],
                "unique_together": {("category", "grade", "academic_year", "term")},
            },
        ),
        migrations.CreateModel(
            name="FeeDiscount",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200)),
                (
                    "discount_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage Discount"),
                            ("fixed_amount", "Fixed Amount Discount"),
                            ("waiver", "Complete Waiver"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "discount_reason",
                    models.CharField(
                        choices=[
                            ("early_payment", "Early Payment"),
                            ("sibling_discount", "Sibling Discount"),
                            ("staff_child", "Staff Child"),
                            ("hardship", "Financial Hardship"),
                            ("loyalty", "Loyalty Discount"),
                            ("bulk_payment", "Bulk Payment"),
                            ("promotional", "Promotional Offer"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "percentage_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "fixed_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "max_discount_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("is_active", models.BooleanField(default=True)),
                ("requires_approval", models.BooleanField(default=False)),
                (
                    "minimum_payment_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("early_payment_days", models.IntegerField(blank=True, null=True)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "applicable_categories",
                    models.ManyToManyField(
                        related_name="discounts", to="finance.feecategory"
                    ),
                ),
                (
                    "applicable_grades",
                    models.ManyToManyField(
                        related_name="fee_discounts", to="academics.grade"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Invoice",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("invoice_number", models.CharField(max_length=50, unique=True)),
                (
                    "invoice_type",
                    models.CharField(
                        choices=[
                            ("fee_invoice", "Fee Invoice"),
                            ("custom_invoice", "Custom Invoice"),
                            ("penalty_invoice", "Penalty Invoice"),
                            ("refund_invoice", "Refund Invoice"),
                        ],
                        default="fee_invoice",
                        max_length=20,
                    ),
                ),
                ("issue_date", models.DateField(auto_now_add=True)),
                ("due_date", models.DateField()),
                ("description", models.TextField()),
                ("notes", models.TextField(blank=True)),
                ("subtotal", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "tax_rate",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "discount_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("total_amount", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "amount_paid",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("balance_due", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("sent", "Sent"),
                            ("viewed", "Viewed"),
                            ("paid", "Paid"),
                            ("partially_paid", "Partially Paid"),
                            ("overdue", "Overdue"),
                            ("cancelled", "Cancelled"),
                            ("refunded", "Refunded"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("sent_date", models.DateTimeField(blank=True, null=True)),
                ("viewed_date", models.DateTimeField(blank=True, null=True)),
                ("paid_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("description", models.CharField(max_length=200)),
                (
                    "quantity",
                    models.DecimalField(decimal_places=2, default=1, max_digits=10),
                ),
                ("unit_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("total_price", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "fee_structure",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="finance.enhancedfeestructure",
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="finance.invoice",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EnhancedPayment",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("payment_reference", models.CharField(max_length=100, unique=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=12)),
                ("payment_date", models.DateTimeField()),
                ("transaction_id", models.CharField(blank=True, max_length=200)),
                ("external_reference", models.CharField(blank=True, max_length=200)),
                ("phone_number", models.CharField(blank=True, max_length=20)),
                (
                    "processing_fee",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("net_amount", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("failure_reason", models.TextField(blank=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "received_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="enhanced_payments",
                        to="students.student",
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="finance.invoice",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="finance.paymentmethod",
                    ),
                ),
            ],
            options={
                "ordering": ["-payment_date"],
            },
        ),
        migrations.CreateModel(
            name="PaymentReminder",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "reminder_type",
                    models.CharField(
                        choices=[
                            ("due_soon", "Payment Due Soon"),
                            ("overdue", "Payment Overdue"),
                            ("partial_payment", "Partial Payment Reminder"),
                            ("final_notice", "Final Notice"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "delivery_method",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("phone", "Phone Call"),
                            ("letter", "Physical Letter"),
                            ("in_person", "In Person"),
                        ],
                        max_length=20,
                    ),
                ),
                ("scheduled_date", models.DateTimeField()),
                ("sent_date", models.DateTimeField(blank=True, null=True)),
                ("subject", models.CharField(max_length=200)),
                ("message", models.TextField()),
                ("amount_due", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("delivery_status", models.CharField(blank=True, max_length=100)),
                ("failure_reason", models.TextField(blank=True)),
                ("email_sent_to", models.EmailField(blank=True, max_length=254)),
                ("phone_sent_to", models.CharField(blank=True, max_length=20)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reminders",
                        to="finance.invoice",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_reminders",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "ordering": ["-scheduled_date"],
            },
        ),
        migrations.CreateModel(
            name="Scholarship",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200)),
                (
                    "scholarship_type",
                    models.CharField(
                        choices=[
                            ("academic", "Academic Excellence"),
                            ("need_based", "Need-Based"),
                            ("sports", "Sports Scholarship"),
                            ("talent", "Talent-Based"),
                            ("government", "Government Bursary"),
                            ("sponsor", "Sponsor Scholarship"),
                            ("staff_child", "Staff Child"),
                            ("sibling", "Sibling Discount"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "coverage_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Coverage"),
                            ("partial", "Partial Coverage"),
                            ("percentage", "Percentage Based"),
                            ("fixed_amount", "Fixed Amount"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "coverage_percentage",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "coverage_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "max_amount_per_year",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "minimum_grade_average",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "family_income_threshold",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("is_renewable", models.BooleanField(default=False)),
                ("renewal_criteria", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "total_budget",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "allocated_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "eligible_grades",
                    models.ManyToManyField(
                        related_name="scholarships", to="academics.grade"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentScholarship",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("application_date", models.DateField(auto_now_add=True)),
                ("approved_date", models.DateField(blank=True, null=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("suspended", "Suspended"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "total_amount_awarded",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "amount_utilized",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("review_notes", models.TextField(blank=True)),
                ("is_renewable", models.BooleanField(default=False)),
                ("renewal_date", models.DateField(blank=True, null=True)),
                ("renewal_criteria_met", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "scholarship",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recipients",
                        to="finance.scholarship",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="scholarships",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "unique_together": {("student", "scholarship")},
            },
        ),
    ]
