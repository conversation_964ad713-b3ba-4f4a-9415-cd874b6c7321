from django.contrib import admin
from .models import Grade, Department, Subject, SubjectRouting, ClassRoom

@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'section', 'level', 'class_teacher', 'admission_fee', 'boarding_fee', 'is_boarding')
    search_fields = ('name', 'section', 'class_teacher__full_name')
    list_filter = ('section', 'level', 'is_boarding')
    ordering = ('name',)

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'head_teacher', 'created_at')
    search_fields = ('name', 'head_teacher__full_name')
    ordering = ('name',)

@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'department', 'is_optional', 'lessons_per_week')
    search_fields = ('name', 'code', 'department__name')
    list_filter = ('department', 'is_optional')
    ordering = ('name',)

@admin.register(SubjectRouting)
class SubjectRoutingAdmin(admin.ModelAdmin):
    list_display = ('id', 'grade', 'subject', 'teacher', 'fee', 'is_mandatory')
    search_fields = ('grade__name', 'subject__name', 'teacher__full_name')
    list_filter = ('grade', 'subject', 'teacher', 'is_mandatory')
    ordering = ('grade', 'subject')

@admin.register(ClassRoom)
class ClassRoomAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'student_count', 'capacity', 'section', 'building', 'floor', 'room_number', 'has_projector', 'has_ac', 'class_teacher')
    search_fields = ('name', 'building', 'room_number', 'class_teacher__full_name')
    list_filter = ('section', 'building', 'has_projector', 'has_ac')
    ordering = ('name',)
