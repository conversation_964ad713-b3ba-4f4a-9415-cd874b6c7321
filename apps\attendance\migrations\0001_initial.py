# Generated by Django 5.2.1 on 2025-05-21 18:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("students", "0001_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="StudentAttendance",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("present", "Present"),
                            ("absent", "Absent"),
                            ("late", "Late"),
                            ("excused", "Excused"),
                        ],
                        max_length=10,
                    ),
                ),
                ("reason", models.CharField(blank=True, max_length=255, null=True)),
                ("notified", models.BooleanField(default=False)),
                (
                    "recorded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendances",
                        to="students.student",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
                "unique_together": {("student", "date")},
            },
        ),
        migrations.CreateModel(
            name="TeacherAttendance",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("present", "Present"),
                            ("absent", "Absent"),
                            ("late", "Late"),
                            ("excused", "Excused"),
                        ],
                        max_length=10,
                    ),
                ),
                ("reason", models.CharField(blank=True, max_length=255, null=True)),
                ("notified", models.BooleanField(default=False)),
                (
                    "recorded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attendances",
                        to="teachers.teacher",
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
                "unique_together": {("teacher", "date")},
            },
        ),
    ]
