"""school_management URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('apps.core.urls')),  # Landing page at root URL
    path('accounts/', include('apps.accounts.urls')),
    path('dashboard/', include('apps.dashboard.urls')),
    path('academics/', include('apps.academics.urls')),  # Added academics URLs
    path('attendance/', include('apps.attendance.urls')),  # Added attendance URLs
    path('library/', include('apps.library.urls')),
    path('transport/', include('apps.transport.urls')),
    path('hostel/', include('apps.hostel.urls')),
    path('health/', include('apps.health.urls')),
    path('activities/', include('apps.activities.urls')),
    path('syllabus/', include('apps.syllabus.urls')),
    path('calendar/', include('apps.school_calendar.urls')),
    path('timetable/', include('apps.timetable.urls')),
    path('finance/', include('apps.finance.urls')),
    path('salary/', include('apps.salary.urls')),
    path('exams/', include('apps.exams.urls')),
    path('students/', include('apps.students.urls')),
    path('teachers/', include('apps.teachers.urls')),
    path('communication/', include('apps.communication.urls')),  # Added communication URLs
    path('reporting/', include('apps.reporting.urls')),

    # API endpoints
    path('api/', include('apps.communication.urls')),  # API routes
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    
    # Debug toolbar URLs
    import debug_toolbar
    urlpatterns += [
        path('__debug__/', include(debug_toolbar.urls)),
    ]
