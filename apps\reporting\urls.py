from django.urls import path
from . import views

app_name = 'reporting'

urlpatterns = [
    # Dashboard and main views
    path('', views.reporting_dashboard, name='dashboard'),
    
    # Report Templates
    path('templates/', views.ReportTemplateListView.as_view(), name='template_list'),
    path('templates/create/', views.ReportTemplateCreateView.as_view(), name='template_create'),
    path('templates/<int:pk>/', views.ReportTemplateDetailView.as_view(), name='template_detail'),
    path('templates/<int:pk>/edit/', views.ReportTemplateUpdateView.as_view(), name='template_edit'),
    path('templates/<int:pk>/delete/', views.ReportTemplateDeleteView.as_view(), name='template_delete'),
    
    # Report Generation
    path('generate/<int:template_id>/', views.generate_report, name='generate_report'),

    # Predefined Reports
    path('generate/system-overview/', views.generate_system_overview, name='generate_system_overview'),
    path('generate/attendance-summary/', views.generate_attendance_summary, name='generate_attendance_summary'),
    path('generate/financial-overview/', views.generate_financial_overview, name='generate_financial_overview'),
    path('generate/student-performance/', views.generate_student_performance, name='generate_student_performance'),
    path('generate/teacher-workload/', views.generate_teacher_workload, name='generate_teacher_workload'),

    path('reports/', views.GeneratedReportListView.as_view(), name='report_list'),
    path('reports/<int:report_id>/', views.report_detail, name='report_detail'),
    path('reports/<int:report_id>/download/', views.download_report, name='download_report'),
    
    # Dashboards
    path('dashboards/', views.DashboardListView.as_view(), name='dashboard_list'),
    path('dashboards/create/', views.DashboardCreateView.as_view(), name='dashboard_create'),
    path('dashboards/<int:pk>/', views.DashboardDetailView.as_view(), name='dashboard_detail'),
    path('dashboards/<int:pk>/edit/', views.DashboardUpdateView.as_view(), name='dashboard_edit'),
    path('dashboards/<int:pk>/delete/', views.DashboardDeleteView.as_view(), name='dashboard_delete'),
    
    # Dashboard Widgets
    path('dashboards/<int:dashboard_id>/widgets/create/', views.DashboardWidgetCreateView.as_view(), name='widget_create'),
    path('widgets/<int:pk>/edit/', views.DashboardWidgetUpdateView.as_view(), name='widget_edit'),
    path('widgets/<int:pk>/delete/', views.DashboardWidgetDeleteView.as_view(), name='widget_delete'),
    
    # Report Schedules
    path('schedules/', views.ReportScheduleListView.as_view(), name='schedule_list'),
    path('schedules/create/', views.ReportScheduleCreateView.as_view(), name='schedule_create'),
    path('schedules/<int:pk>/', views.ReportScheduleDetailView.as_view(), name='schedule_detail'),
    path('schedules/<int:pk>/edit/', views.ReportScheduleUpdateView.as_view(), name='schedule_edit'),
    path('schedules/<int:pk>/delete/', views.ReportScheduleDeleteView.as_view(), name='schedule_delete'),
    
    # Data Visualizations
    path('visualizations/', views.DataVisualizationListView.as_view(), name='visualization_list'),
    path('visualizations/create/', views.DataVisualizationCreateView.as_view(), name='visualization_create'),
    path('visualizations/<int:pk>/', views.DataVisualizationDetailView.as_view(), name='visualization_detail'),
    path('visualizations/<int:pk>/edit/', views.DataVisualizationUpdateView.as_view(), name='visualization_edit'),
    path('visualizations/<int:pk>/delete/', views.DataVisualizationDeleteView.as_view(), name='visualization_delete'),
    
    # API Endpoints
    path('api/dashboard-data/', views.api_dashboard_data, name='api_dashboard_data'),
    path('api/widget-data/<int:widget_id>/', views.api_widget_data, name='api_widget_data'),
    path('api/report-status/<int:report_id>/', views.api_report_status, name='api_report_status'),
    path('api/templates/', views.api_template_list, name='api_template_list'),
    path('api/generate-preview/', views.api_generate_preview, name='api_generate_preview'),
]
