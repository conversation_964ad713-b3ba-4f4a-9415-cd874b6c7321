from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import datetime, time
import json

from .models import Timetable
from apps.academics.models import Grade, Subject, ClassRoom
from apps.teachers.models import Teacher

@login_required
def timetable_dashboard(request):
    """Timetable management dashboard."""
    # Basic statistics
    total_timetables = Timetable.objects.count()
    total_grades = Grade.objects.count()
    total_subjects = Subject.objects.count()
    total_teachers = Teacher.objects.count()

    # Recent timetables
    recent_timetables = Timetable.objects.select_related(
        'grade', 'subject', 'teacher', 'classroom'
    ).order_by('-id')[:10]

    # Days of the week
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    # Timetable statistics by day
    day_stats = []
    for day in days_of_week:
        count = Timetable.objects.filter(day=day).count()
        # Calculate percentage
        if total_timetables > 0:
            percentage = (count / total_timetables) * 100
        else:
            percentage = 0
        day_stats.append({
            'day': day,
            'count': count,
            'percentage': percentage
        })

    context = {
        'total_timetables': total_timetables,
        'total_grades': total_grades,
        'total_subjects': total_subjects,
        'total_teachers': total_teachers,
        'recent_timetables': recent_timetables,
        'day_stats': day_stats,
        'days_of_week': days_of_week,
    }
    return render(request, 'timetable/dashboard.html', context)

@login_required
def timetable_view(request):
    """View all timetables with filtering options."""
    timetables = Timetable.objects.select_related(
        'grade', 'subject', 'teacher', 'classroom'
    ).all()

    # Filtering
    grade_filter = request.GET.get('grade')
    day_filter = request.GET.get('day')
    teacher_filter = request.GET.get('teacher')
    search = request.GET.get('search')

    if grade_filter:
        timetables = timetables.filter(grade_id=grade_filter)

    if day_filter:
        timetables = timetables.filter(day=day_filter)

    if teacher_filter:
        timetables = timetables.filter(teacher_id=teacher_filter)

    if search:
        timetables = timetables.filter(
            Q(grade__name__icontains=search) |
            Q(subject__name__icontains=search) |
            Q(teacher__first_name__icontains=search) |
            Q(teacher__last_name__icontains=search) |
            Q(classroom__name__icontains=search)
        )

    # Pagination
    paginator = Paginator(timetables, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Filter options
    grades = Grade.objects.all()
    teachers = Teacher.objects.all()
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    context = {
        'page_obj': page_obj,
        'grades': grades,
        'teachers': teachers,
        'days_of_week': days_of_week,
        'current_filters': {
            'grade': grade_filter,
            'day': day_filter,
            'teacher': teacher_filter,
            'search': search,
        }
    }
    return render(request, 'timetable/timetable_list.html', context)

@login_required
def create_timetable(request):
    """Create a new timetable entry."""
    if request.method == 'POST':
        try:
            grade_id = request.POST.get('grade')
            day = request.POST.get('day')
            subject_id = request.POST.get('subject')
            teacher_id = request.POST.get('teacher')
            classroom_id = request.POST.get('classroom')
            start_time = float(request.POST.get('start_time'))
            end_time = float(request.POST.get('end_time'))
            period_number = request.POST.get('period_number')
            is_break = request.POST.get('is_break') == 'on'

            # Validation
            if start_time >= end_time:
                messages.error(request, 'Start time must be before end time.')
                return redirect('timetable:create')

            # Check for conflicts
            conflicts = Timetable.objects.filter(
                grade_id=grade_id,
                day=day,
                start_time__lt=end_time,
                end_time__gt=start_time
            )

            if conflicts.exists():
                messages.error(request, 'Time slot conflicts with existing timetable entry.')
                return redirect('timetable:create')

            # Create timetable
            timetable = Timetable.objects.create(
                grade_id=grade_id,
                day=day,
                subject_id=subject_id,
                teacher_id=teacher_id,
                classroom_id=classroom_id,
                start_time=start_time,
                end_time=end_time,
                period_number=period_number if period_number else None,
                is_break=is_break
            )

            messages.success(request, 'Timetable entry created successfully.')
            return redirect('timetable:view')

        except Exception as e:
            messages.error(request, f'Error creating timetable: {str(e)}')

    # Get form data
    grades = Grade.objects.all()
    subjects = Subject.objects.all()
    teachers = Teacher.objects.all()
    classrooms = ClassRoom.objects.all()
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    context = {
        'grades': grades,
        'subjects': subjects,
        'teachers': teachers,
        'classrooms': classrooms,
        'days_of_week': days_of_week,
    }
    return render(request, 'timetable/create_timetable.html', context)

@login_required
def edit_timetable(request, timetable_id):
    """Edit an existing timetable entry."""
    timetable = get_object_or_404(Timetable, id=timetable_id)

    if request.method == 'POST':
        try:
            timetable.grade_id = request.POST.get('grade')
            timetable.day = request.POST.get('day')
            timetable.subject_id = request.POST.get('subject')
            timetable.teacher_id = request.POST.get('teacher')
            timetable.classroom_id = request.POST.get('classroom')
            timetable.start_time = float(request.POST.get('start_time'))
            timetable.end_time = float(request.POST.get('end_time'))
            timetable.period_number = request.POST.get('period_number') or None
            timetable.is_break = request.POST.get('is_break') == 'on'

            # Validation
            if timetable.start_time >= timetable.end_time:
                messages.error(request, 'Start time must be before end time.')
                return redirect('timetable:edit', timetable_id=timetable_id)

            # Check for conflicts (excluding current timetable)
            conflicts = Timetable.objects.filter(
                grade_id=timetable.grade_id,
                day=timetable.day,
                start_time__lt=timetable.end_time,
                end_time__gt=timetable.start_time
            ).exclude(id=timetable_id)

            if conflicts.exists():
                messages.error(request, 'Time slot conflicts with existing timetable entry.')
                return redirect('timetable:edit', timetable_id=timetable_id)

            timetable.save()
            messages.success(request, 'Timetable entry updated successfully.')
            return redirect('timetable:view')

        except Exception as e:
            messages.error(request, f'Error updating timetable: {str(e)}')

    # Get form data
    grades = Grade.objects.all()
    subjects = Subject.objects.all()
    teachers = Teacher.objects.all()
    classrooms = ClassRoom.objects.all()
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    context = {
        'timetable': timetable,
        'grades': grades,
        'subjects': subjects,
        'teachers': teachers,
        'classrooms': classrooms,
        'days_of_week': days_of_week,
    }
    return render(request, 'timetable/edit_timetable.html', context)

@login_required
def delete_timetable(request, timetable_id):
    """Delete a timetable entry."""
    timetable = get_object_or_404(Timetable, id=timetable_id)

    if request.method == 'POST':
        timetable.delete()
        messages.success(request, 'Timetable entry deleted successfully.')
        return redirect('timetable:view')

    context = {'timetable': timetable}
    return render(request, 'timetable/delete_timetable.html', context)

@login_required
def grade_timetable(request, grade_id):
    """View timetable for a specific grade."""
    grade = get_object_or_404(Grade, id=grade_id)

    # Get timetables for this grade
    timetables = Timetable.objects.filter(grade=grade).select_related(
        'subject', 'teacher', 'classroom'
    ).order_by('day', 'start_time')

    # Organize by day
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    timetable_by_day = {}

    for day in days_of_week:
        timetable_by_day[day] = timetables.filter(day=day).order_by('start_time')

    context = {
        'grade': grade,
        'timetable_by_day': timetable_by_day,
        'days_of_week': days_of_week,
    }
    return render(request, 'timetable/grade_timetable.html', context)

@login_required
def teacher_timetable(request, teacher_id):
    """View timetable for a specific teacher."""
    teacher = get_object_or_404(Teacher, id=teacher_id)

    # Get timetables for this teacher
    timetables = Timetable.objects.filter(teacher=teacher).select_related(
        'grade', 'subject', 'classroom'
    ).order_by('day', 'start_time')

    # Organize by day
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    timetable_by_day = {}

    for day in days_of_week:
        timetable_by_day[day] = timetables.filter(day=day).order_by('start_time')

    context = {
        'teacher': teacher,
        'timetable_by_day': timetable_by_day,
        'days_of_week': days_of_week,
    }
    return render(request, 'timetable/teacher_timetable.html', context)

@login_required
def classroom_timetable(request, classroom_id):
    """View timetable for a specific classroom."""
    classroom = get_object_or_404(ClassRoom, id=classroom_id)

    # Get timetables for this classroom
    timetables = Timetable.objects.filter(classroom=classroom).select_related(
        'grade', 'subject', 'teacher'
    ).order_by('day', 'start_time')

    # Organize by day
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    timetable_by_day = {}

    for day in days_of_week:
        timetable_by_day[day] = timetables.filter(day=day).order_by('start_time')

    context = {
        'classroom': classroom,
        'timetable_by_day': timetable_by_day,
        'days_of_week': days_of_week,
    }
    return render(request, 'timetable/classroom_timetable.html', context)

@login_required
def timetable_reports(request):
    """Generate timetable reports and analytics."""
    # Basic statistics
    total_periods = Timetable.objects.count()
    total_grades = Grade.objects.count()
    total_subjects = Subject.objects.count()
    total_teachers = Teacher.objects.count()

    # Subject distribution
    subject_stats = Timetable.objects.values('subject__name').annotate(
        count=Count('id')
    ).order_by('-count')

    # Calculate percentages for subjects
    for stat in subject_stats:
        if total_periods > 0:
            stat['percentage'] = (stat['count'] / total_periods) * 100
        else:
            stat['percentage'] = 0

    # Teacher workload
    teacher_stats = Timetable.objects.values(
        'teacher__first_name', 'teacher__last_name'
    ).annotate(
        periods_count=Count('id')
    ).order_by('-periods_count')

    # Calculate percentages for teachers
    for stat in teacher_stats:
        if total_periods > 0:
            stat['percentage'] = (stat['periods_count'] / total_periods) * 100
        else:
            stat['percentage'] = 0

    # Day-wise distribution
    day_stats = Timetable.objects.values('day').annotate(
        count=Count('id')
    ).order_by('day')

    # Calculate percentages for days
    for stat in day_stats:
        if total_periods > 0:
            stat['percentage'] = (stat['count'] / total_periods) * 100
        else:
            stat['percentage'] = 0

    context = {
        'total_periods': total_periods,
        'total_grades': total_grades,
        'total_subjects': total_subjects,
        'total_teachers': total_teachers,
        'subject_stats': subject_stats,
        'teacher_stats': teacher_stats,
        'day_stats': day_stats,
    }
    return render(request, 'timetable/reports.html', context)

@login_required
def export_timetable(request):
    """Export timetable data."""
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="timetable.csv"'

    writer = csv.writer(response)
    writer.writerow(['Grade', 'Day', 'Subject', 'Teacher', 'Classroom', 'Start Time', 'End Time', 'Period', 'Is Break'])

    timetables = Timetable.objects.select_related(
        'grade', 'subject', 'teacher', 'classroom'
    ).all()

    for timetable in timetables:
        writer.writerow([
            timetable.grade.name,
            timetable.day,
            timetable.subject.name,
            f"{timetable.teacher.first_name} {timetable.teacher.last_name}",
            timetable.classroom.name,
            timetable.start_time,
            timetable.end_time,
            timetable.period_number or '',
            'Yes' if timetable.is_break else 'No'
        ])

    return response
