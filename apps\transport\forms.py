from django import forms
from django.core.exceptions import ValidationError
from django.db import models
from .models import TransportRoute, TransportVehicle, TransportStop, TransportAssignment
from apps.students.models import Student
from datetime import date

class TransportRouteForm(forms.ModelForm):
    class Meta:
        model = TransportRoute
        fields = ['name', 'description', 'distance', 'estimated_time', 'fee']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Enter route name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Route description'
            }),
            'distance': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Distance in kilometers',
                'step': '0.1'
            }),
            'estimated_time': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Estimated time in minutes'
            }),
            'fee': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Transport fee (KES)',
                'step': '0.01'
            }),
        }

class TransportVehicleForm(forms.ModelForm):
    class Meta:
        model = TransportVehicle
        fields = [
            'name', 'registration_number', 'vehicle_type', 'capacity',
            'driver_name', 'driver_contact', 'insurance_expiry', 
            'service_date', 'status'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Vehicle name/identifier'
            }),
            'registration_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Registration number (e.g., KAA 123A)'
            }),
            'vehicle_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Seating capacity'
            }),
            'driver_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Driver name'
            }),
            'driver_contact': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Driver phone number'
            }),
            'insurance_expiry': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'service_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
        }

    def clean_registration_number(self):
        registration_number = self.cleaned_data['registration_number']
        # Check for duplicate registration numbers
        existing = TransportVehicle.objects.filter(registration_number=registration_number)
        if self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)
        if existing.exists():
            raise ValidationError("A vehicle with this registration number already exists.")
        return registration_number

class TransportStopForm(forms.ModelForm):
    class Meta:
        model = TransportStop
        fields = ['route', 'name', 'sequence', 'arrival_time', 'departure_time', 'coordinates']
        widgets = {
            'route': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Stop name/location'
            }),
            'sequence': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Stop sequence number'
            }),
            'arrival_time': forms.TimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'time'
            }),
            'departure_time': forms.TimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'time'
            }),
            'coordinates': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Latitude,Longitude (optional)'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        route = cleaned_data.get('route')
        sequence = cleaned_data.get('sequence')
        
        if route and sequence:
            # Check for duplicate sequence numbers in the same route
            existing = TransportStop.objects.filter(route=route, sequence=sequence)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError("A stop with this sequence number already exists for this route.")
        
        return cleaned_data

class TransportAssignmentForm(forms.ModelForm):
    student_index = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter student index number'
        })
    )
    
    class Meta:
        model = TransportAssignment
        fields = ['route', 'stop', 'vehicle', 'fee', 'start_date', 'end_date', 'status']
        widgets = {
            'route': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'stop': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'vehicle': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'fee': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Transport fee (KES)',
                'step': '0.01'
            }),
            'start_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default start date to today
        self.fields['start_date'].initial = date.today()
        # Only show active vehicles
        self.fields['vehicle'].queryset = TransportVehicle.objects.filter(status='active')
        # Initially empty stops (will be populated via JavaScript based on route selection)
        self.fields['stop'].queryset = TransportStop.objects.none()

    def clean_student_index(self):
        student_index = self.cleaned_data['student_index']
        try:
            student = Student.objects.get(index_number=student_index)
            return student
        except Student.DoesNotExist:
            raise ValidationError("Student with this index number does not exist.")

    def clean(self):
        cleaned_data = super().clean()
        route = cleaned_data.get('route')
        stop = cleaned_data.get('stop')
        vehicle = cleaned_data.get('vehicle')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        # Validate stop belongs to route
        if route and stop and stop.route != route:
            raise ValidationError("Selected stop does not belong to the selected route.")
        
        # Validate end date is after start date
        if start_date and end_date and end_date <= start_date:
            raise ValidationError("End date must be after start date.")
        
        # Check vehicle capacity
        if vehicle and route and start_date:
            current_assignments = TransportAssignment.objects.filter(
                vehicle=vehicle,
                route=route,
                status='active',
                start_date__lte=start_date
            ).filter(
                models.Q(end_date__isnull=True) | models.Q(end_date__gte=start_date)
            ).count()
            
            if current_assignments >= vehicle.capacity:
                raise ValidationError(f"Vehicle {vehicle.name} is at full capacity for this route.")
        
        return cleaned_data

class TransportFeePaymentForm(forms.Form):
    assignment_id = forms.IntegerField(widget=forms.HiddenInput())
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Payment amount (KES)',
            'step': '0.01'
        })
    )
    payment_method = forms.ChoiceField(
        choices=[
            ('cash', 'Cash'),
            ('mpesa', 'M-Pesa'),
            ('bank', 'Bank Transfer'),
            ('cheque', 'Cheque'),
        ],
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    reference_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Payment reference/transaction ID'
        })
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'rows': 3,
            'placeholder': 'Payment notes (optional)'
        })
    )

class TransportDiscountForm(forms.Form):
    assignment_id = forms.IntegerField(widget=forms.HiddenInput())
    discount_type = forms.ChoiceField(
        choices=[
            ('percentage', 'Percentage'),
            ('fixed', 'Fixed Amount'),
        ],
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    discount_value = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Discount value',
            'step': '0.01'
        })
    )
    reason = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'rows': 3,
            'placeholder': 'Reason for discount'
        })
    )

class TransportReportForm(forms.Form):
    report_type = forms.ChoiceField(
        choices=[
            ('assignments', 'Student Assignments'),
            ('payments', 'Fee Payments'),
            ('routes', 'Route Utilization'),
            ('vehicles', 'Vehicle Usage'),
            ('overdue', 'Overdue Payments'),
        ],
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )
    route = forms.ModelChoiceField(
        queryset=TransportRoute.objects.all(),
        required=False,
        empty_label="All Routes",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    vehicle = forms.ModelChoiceField(
        queryset=TransportVehicle.objects.all(),
        required=False,
        empty_label="All Vehicles",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

class TransportSearchForm(forms.Form):
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Search by student name, route, or vehicle'
        })
    )
    route = forms.ModelChoiceField(
        queryset=TransportRoute.objects.all(),
        required=False,
        empty_label="All Routes",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    vehicle = forms.ModelChoiceField(
        queryset=TransportVehicle.objects.filter(status='active'),
        required=False,
        empty_label="All Vehicles",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    status = forms.ChoiceField(
        choices=[('', 'All Status')] + TransportAssignment.ASSIGNMENT_STATUSES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
