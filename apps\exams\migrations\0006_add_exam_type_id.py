# Generated manually to add exam_type_id column

from django.db import migrations


def add_exam_type_id_column(apps, schema_editor):
    """Add exam_type_id column to exams_exam table"""
    from django.db import connection
    
    cursor = connection.cursor()
    
    # Check if exam_type_id column exists
    cursor.execute("PRAGMA table_info(exams_exam);")
    columns = [column[1] for column in cursor.fetchall()]
    
    if 'exam_type_id' not in columns:
        cursor.execute("ALTER TABLE exams_exam ADD COLUMN exam_type_id INTEGER NULL;")
        print("Added exam_type_id column to exams_exam table")
    else:
        print("exam_type_id column already exists in exams_exam table")


def reverse_exam_type_id_column(apps, schema_editor):
    """Reverse operation - no-op"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("exams", "0005_merge_20250617_1709"),
    ]

    operations = [
        migrations.RunPython(add_exam_type_id_column, reverse_exam_type_id_column),
    ]
