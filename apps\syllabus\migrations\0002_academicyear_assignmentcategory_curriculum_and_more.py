# Generated by Django 5.2.1 on 2025-06-09 09:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("academics", "0002_initial"),
        ("syllabus", "0001_initial"),
        ("teachers", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AcademicYear",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("year", models.CharField(max_length=20, unique=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("is_current", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="AssignmentCategory",
            fields=[
                ("id", models.<PERSON>Field(primary_key=True, serialize=False)),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                (
                    "category_type",
                    models.Char<PERSON><PERSON>(
                        choices=[
                            ("homework", "Homework"),
                            ("classwork", "Classwork"),
                            ("project", "Project"),
                            ("research", "Research"),
                            ("presentation", "Presentation"),
                            ("practical", "Practical Work"),
                            ("quiz", "Quiz"),
                            ("test", "Test"),
                            ("group_work", "Group Work"),
                            ("individual", "Individual Work"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "default_weight",
                    models.DecimalField(
                        decimal_places=2,
                        default=1.0,
                        help_text="Default weight for grading",
                        max_digits=5,
                    ),
                ),
                (
                    "color_code",
                    models.CharField(
                        default="#3B82F6",
                        help_text="Hex color code for UI",
                        max_length=7,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "Assignment Categories",
            },
        ),
        migrations.CreateModel(
            name="Curriculum",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200)),
                ("code", models.CharField(max_length=50, unique=True)),
                (
                    "curriculum_type",
                    models.CharField(
                        choices=[
                            ("national", "National Curriculum"),
                            ("international", "International Curriculum"),
                            ("local", "Local Curriculum"),
                            ("custom", "Custom Curriculum"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "total_hours",
                    models.IntegerField(help_text="Total curriculum hours"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("draft", "Draft"),
                            ("archived", "Archived"),
                            ("under_review", "Under Review"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="syllabus.academicyear",
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_curricula",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade_levels",
                    models.ManyToManyField(
                        related_name="curricula", to="academics.grade"
                    ),
                ),
                (
                    "subjects",
                    models.ManyToManyField(
                        related_name="curricula", to="academics.subject"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Curricula",
            },
        ),
        migrations.CreateModel(
            name="SyllabusUnit",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("unit_number", models.IntegerField()),
                ("title", models.CharField(max_length=200)),
                (
                    "unit_type",
                    models.CharField(
                        choices=[
                            ("chapter", "Chapter"),
                            ("topic", "Topic"),
                            ("module", "Module"),
                            ("unit", "Unit"),
                            ("lesson", "Lesson"),
                        ],
                        default="chapter",
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "learning_objectives",
                    models.TextField(help_text="Learning objectives for this unit"),
                ),
                (
                    "duration_hours",
                    models.IntegerField(help_text="Estimated hours to complete"),
                ),
                ("start_week", models.IntegerField(blank=True, null=True)),
                ("end_week", models.IntegerField(blank=True, null=True)),
                (
                    "prerequisites",
                    models.TextField(
                        blank=True, help_text="Prerequisites for this unit"
                    ),
                ),
                (
                    "assessment_methods",
                    models.TextField(
                        blank=True, help_text="Assessment methods for this unit"
                    ),
                ),
                (
                    "resources_required",
                    models.TextField(
                        blank=True, help_text="Resources and materials required"
                    ),
                ),
                ("is_mandatory", models.BooleanField(default=True)),
                ("order", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "syllabus",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="units",
                        to="syllabus.academicsyllabus",
                    ),
                ),
            ],
            options={
                "ordering": ["order", "unit_number"],
                "unique_together": {("syllabus", "unit_number")},
            },
        ),
        migrations.CreateModel(
            name="LearningMaterial",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "material_type",
                    models.CharField(
                        choices=[
                            ("textbook", "Textbook"),
                            ("workbook", "Workbook"),
                            ("reference", "Reference Book"),
                            ("video", "Video"),
                            ("audio", "Audio"),
                            ("presentation", "Presentation"),
                            ("document", "Document"),
                            ("worksheet", "Worksheet"),
                            ("quiz", "Quiz"),
                            ("interactive", "Interactive Content"),
                            ("external_link", "External Link"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "material_format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("doc", "Word Document"),
                            ("ppt", "PowerPoint"),
                            ("video", "Video File"),
                            ("audio", "Audio File"),
                            ("image", "Image"),
                            ("url", "Web Link"),
                            ("zip", "Archive"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "file_path",
                    models.FileField(
                        blank=True, null=True, upload_to="learning_materials/"
                    ),
                ),
                ("external_url", models.URLField(blank=True)),
                (
                    "file_size",
                    models.BigIntegerField(
                        blank=True, help_text="File size in bytes", null=True
                    ),
                ),
                (
                    "duration_minutes",
                    models.IntegerField(
                        blank=True,
                        help_text="Duration for video/audio materials",
                        null=True,
                    ),
                ),
                (
                    "access_level",
                    models.CharField(
                        choices=[
                            ("public", "Public"),
                            ("students", "Students Only"),
                            ("teachers", "Teachers Only"),
                            ("restricted", "Restricted Access"),
                        ],
                        default="students",
                        max_length=20,
                    ),
                ),
                ("is_mandatory", models.BooleanField(default=False)),
                ("download_count", models.IntegerField(default=0)),
                ("view_count", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "syllabus",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="learning_materials",
                        to="syllabus.academicsyllabus",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "syllabus_unit",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="learning_materials",
                        to="syllabus.syllabusunit",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Term",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("term_1", "Term 1"),
                            ("term_2", "Term 2"),
                            ("term_3", "Term 3"),
                        ],
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("is_current", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="terms",
                        to="syllabus.academicyear",
                    ),
                ),
            ],
            options={
                "unique_together": {("name", "academic_year")},
            },
        ),
        migrations.CreateModel(
            name="SyllabusReport",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=200)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("progress", "Progress Report"),
                            ("completion", "Completion Report"),
                            ("material_usage", "Material Usage Report"),
                            ("assignment_summary", "Assignment Summary"),
                            ("term_summary", "Term Summary"),
                            ("annual_summary", "Annual Summary"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "report_format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("excel", "Excel"),
                            ("csv", "CSV"),
                            ("html", "HTML"),
                        ],
                        default="pdf",
                        max_length=10,
                    ),
                ),
                ("date_from", models.DateField()),
                ("date_to", models.DateField()),
                (
                    "file_path",
                    models.FileField(
                        blank=True, null=True, upload_to="syllabus_reports/"
                    ),
                ),
                ("generated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="syllabus.academicyear",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.subject",
                    ),
                ),
                (
                    "syllabus",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="syllabus.academicsyllabus",
                    ),
                ),
                (
                    "term",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="syllabus.term",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SyllabusProgress",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("planned_start_date", models.DateField()),
                ("actual_start_date", models.DateField(blank=True, null=True)),
                ("planned_end_date", models.DateField()),
                ("actual_end_date", models.DateField(blank=True, null=True)),
                (
                    "completion_percentage",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("not_started", "Not Started"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("behind_schedule", "Behind Schedule"),
                            ("ahead_schedule", "Ahead of Schedule"),
                        ],
                        default="not_started",
                        max_length=20,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Progress notes and observations"
                    ),
                ),
                (
                    "challenges",
                    models.TextField(blank=True, help_text="Challenges encountered"),
                ),
                (
                    "adjustments_made",
                    models.TextField(
                        blank=True, help_text="Adjustments made to original plan"
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="academics.grade",
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teachers.teacher",
                    ),
                ),
                (
                    "syllabus_unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="progress_records",
                        to="syllabus.syllabusunit",
                    ),
                ),
            ],
            options={
                "unique_together": {("syllabus_unit", "teacher", "grade")},
            },
        ),
        migrations.CreateModel(
            name="TermPlan",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=200)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("total_weeks", models.IntegerField()),
                ("total_hours", models.IntegerField()),
                ("objectives", models.TextField(help_text="Term objectives and goals")),
                ("key_topics", models.TextField(help_text="Key topics to be covered")),
                (
                    "assessment_plan",
                    models.TextField(help_text="Assessment and evaluation plan"),
                ),
                (
                    "resources_needed",
                    models.TextField(
                        blank=True, help_text="Resources and materials needed"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("approved", "Approved"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "academic_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="syllabus.academicyear",
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_term_plans",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "syllabus",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="term_plans",
                        to="syllabus.academicsyllabus",
                    ),
                ),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="syllabus.term"
                    ),
                ),
            ],
            options={
                "unique_together": {("syllabus", "academic_year", "term")},
            },
        ),
    ]
