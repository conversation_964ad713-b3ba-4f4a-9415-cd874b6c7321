{% extends 'exams/base.html' %}

{% block exam_content %}
<div class="px-4 py-6 sm:px-0">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ exam.name }}</h1>
                    <p class="text-sm text-gray-600 mt-1">{{ exam.exam_type.name }} - {{ exam.get_status_display }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'exams:edit_exam' exam.id %}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-edit mr-2"></i>Edit Exam
                    </a>
                    <a href="{% url 'exams:generate_timetable' exam.id %}" 
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-calendar-plus mr-2"></i>Generate Timetable
                    </a>
                    <a href="{% url 'exams:bulk_result_upload' exam.id %}" 
                       class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-upload mr-2"></i>Upload Results
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Exam Details -->
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Exam Period</h3>
                    <p class="mt-1 text-sm text-gray-900">
                        {{ exam.start_date|date:"M d, Y" }} - {{ exam.end_date|date:"M d, Y" }}
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Total Marks</h3>
                    <p class="mt-1 text-sm text-gray-900">{{ exam.total_marks }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Grading System</h3>
                    <p class="mt-1 text-sm text-gray-900">{{ exam.grading_system.name }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Students</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_students }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Submitted Results</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ submitted_results }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Verified Results</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ verified_results }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Timetable Entries</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ timetable_entries.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Timetable -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">Exam Timetable</h2>
                <a href="{% url 'exams:add_timetable' %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                    Add Entry
                </a>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for entry in timetable_entries %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ entry.subject.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ entry.grade.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ entry.exam_date|date:"M d, Y" }} at {{ entry.start_time }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ entry.duration_minutes }} mins
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if entry.status == 'published' %}bg-green-100 text-green-800
                                {% elif entry.status == 'draft' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ entry.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'exams:timetable_detail' entry.id %}" 
                               class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                            <a href="{% url 'exams:result_entry_form' entry.id %}" 
                               class="text-green-600 hover:text-green-900">Enter Results</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                            No timetable entries found. 
                            <a href="{% url 'exams:generate_timetable' exam.id %}" class="text-blue-600 hover:text-blue-900">
                                Generate timetable
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Recent Activities</h2>
        </div>
        <div class="px-6 py-4">
            {% if recent_activities %}
                <div class="flow-root">
                    <ul class="-mb-8">
                        {% for activity in recent_activities %}
                        <li>
                            <div class="relative pb-8">
                                {% if not forloop.last %}
                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                {% endif %}
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                            <i class="fas fa-clipboard-check text-white text-xs"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">
                                                {{ activity.description }}
                                                <span class="font-medium text-gray-900">by {{ activity.user.get_full_name|default:activity.user.username }}</span>
                                            </p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ activity.timestamp|timesince }} ago
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            {% else %}
                <p class="text-sm text-gray-500">No recent activities.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
